import mongoose, { Document, Schema } from 'mongoose';

export interface INotification extends Document {
  _id: string;
  recipient: {
    id: string;
    name: string;
    email: string;
  };
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  type: 'info' | 'success' | 'warning' | 'error' | 'order' | 'course' | 'nft' | 'social' | 'system';
  category: 'general' | 'order' | 'payment' | 'course' | 'nft' | 'social' | 'security' | 'promotion';
  title: string;
  message: string;
  data?: {
    orderId?: string;
    courseId?: string;
    nftId?: string;
    productId?: string;
    userId?: string;
    url?: string;
    action?: string;
    [key: string]: any;
  };
  priority: 'low' | 'medium' | 'high' | 'urgent';
  channels: Array<'in-app' | 'email' | 'sms' | 'push'>;
  status: {
    sent: boolean;
    sentAt?: Date;
    read: boolean;
    readAt?: Date;
    clicked: boolean;
    clickedAt?: Date;
  };
  scheduling: {
    sendAt?: Date;
    expiresAt?: Date;
  };
  delivery: {
    email?: {
      sent: boolean;
      sentAt?: Date;
      error?: string;
    };
    sms?: {
      sent: boolean;
      sentAt?: Date;
      error?: string;
    };
    push?: {
      sent: boolean;
      sentAt?: Date;
      error?: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const notificationSchema = new Schema<INotification>({
  recipient: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: { type: String, required: true },
    email: { type: String, required: true }
  },
  sender: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    name: String,
    avatar: String
  },
  type: {
    type: String,
    required: true,
    enum: ['info', 'success', 'warning', 'error', 'order', 'course', 'nft', 'social', 'system']
  },
  category: {
    type: String,
    required: true,
    enum: ['general', 'order', 'payment', 'course', 'nft', 'social', 'security', 'promotion']
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  data: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {}
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  channels: [{
    type: String,
    enum: ['in-app', 'email', 'sms', 'push']
  }],
  status: {
    sent: { type: Boolean, default: false },
    sentAt: Date,
    read: { type: Boolean, default: false },
    readAt: Date,
    clicked: { type: Boolean, default: false },
    clickedAt: Date
  },
  scheduling: {
    sendAt: Date,
    expiresAt: Date
  },
  delivery: {
    email: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    sms: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    },
    push: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      error: String
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
notificationSchema.index({ 'recipient.id': 1, createdAt: -1 });
notificationSchema.index({ 'status.read': 1, 'recipient.id': 1 });
notificationSchema.index({ type: 1, category: 1 });
notificationSchema.index({ priority: 1, 'status.sent': 1 });
notificationSchema.index({ 'scheduling.sendAt': 1 });
notificationSchema.index({ 'scheduling.expiresAt': 1 });

// Virtual for checking if notification is expired
notificationSchema.virtual('isExpired').get(function() {
  return this.scheduling.expiresAt && this.scheduling.expiresAt < new Date();
});

// Virtual for checking if notification should be sent
notificationSchema.virtual('shouldSend').get(function() {
  const now = new Date();
  return !this.status.sent && 
         (!this.scheduling.sendAt || this.scheduling.sendAt <= now) &&
         (!this.scheduling.expiresAt || this.scheduling.expiresAt > now);
});

export const Notification = mongoose.model<INotification>('Notification', notificationSchema);
