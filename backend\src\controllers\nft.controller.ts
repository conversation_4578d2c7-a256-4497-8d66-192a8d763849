import { Request, Response } from 'express';
import { NFT } from '../models/nft.model';

// Get all NFTs with filtering and pagination
export const getNFTs = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 12,
      category,
      blockchain,
      minPrice,
      maxPrice,
      search,
      sortBy = 'mintedAt',
      sortOrder = 'desc',
      isFor<PERSON><PERSON>,
      creator,
      owner
    } = req.query;

    // Build filter object
    const filter: any = { status: { $ne: 'burned' } };

    if (category) filter.category = category;
    if (blockchain) filter.blockchain = blockchain;
    if (creator) filter['creator.id'] = creator;
    if (owner) filter['owner.id'] = owner;
    if (isForSale !== undefined) filter['pricing.isForSale'] = isForSale === 'true';

    // Price range filter
    if (minPrice || maxPrice) {
      filter['pricing.currentPrice'] = {};
      if (minPrice) filter['pricing.currentPrice'].$gte = Number(minPrice);
      if (maxPrice) filter['pricing.currentPrice'].$lte = Number(maxPrice);
    }

    // Search filter
    if (search) {
      filter.$or = [
        { name: new RegExp(search as string, 'i') },
        { description: new RegExp(search as string, 'i') },
        { tags: { $in: [new RegExp(search as string, 'i')] } }
      ];
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (Number(page) - 1) * Number(limit);
    
    const [nfts, total] = await Promise.all([
      NFT.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .populate('creator.id', 'name avatar')
        .populate('owner.id', 'name avatar'),
      NFT.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / Number(limit));

    res.json({
      success: true,
      data: {
        nfts,
        pagination: {
          currentPage: Number(page),
          totalPages,
          totalItems: total,
          itemsPerPage: Number(limit),
          hasNextPage: Number(page) < totalPages,
          hasPrevPage: Number(page) > 1
        }
      }
    });
  } catch (error: any) {
    console.error('Get NFTs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch NFTs',
      error: error.message
    });
  }
};

// Get single NFT by ID
export const getNFTById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const nft = await NFT.findById(id)
      .populate('creator.id', 'name avatar profile.bio')
      .populate('owner.id', 'name avatar profile.bio');

    if (!nft) {
      return res.status(404).json({
        success: false,
        message: 'NFT not found'
      });
    }

    // Increment view count
    nft.trading.views += 1;
    await nft.save();

    res.json({
      success: true,
      data: { nft }
    });
  } catch (error: any) {
    console.error('Get NFT error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch NFT',
      error: error.message
    });
  }
};

// Create/Mint new NFT
export const createNFT = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user has creator role
    if (!req.user.roles.includes('creator') && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Creator role required to mint NFTs'
      });
    }

    const nftData = {
      ...req.body,
      tokenId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      contractAddress: process.env.NFT_CONTRACT_ADDRESS || '0x...',
      creator: {
        id: req.user._id,
        name: req.user.name,
        avatar: req.user.avatar || '',
        walletAddress: req.user.profile?.walletAddress || ''
      },
      owner: {
        id: req.user._id,
        name: req.user.name,
        avatar: req.user.avatar || '',
        walletAddress: req.user.profile?.walletAddress || ''
      },
      metadata: {
        standard: 'ERC-721',
        ipfsHash: `Qm${Math.random().toString(36).substr(2, 44)}`, // Mock IPFS hash
        metadataUri: `https://ipfs.io/ipfs/Qm${Math.random().toString(36).substr(2, 44)}`
      },
      status: 'minted'
    };

    const nft = new NFT(nftData);
    await nft.save();

    // Update user stats
    req.user.stats.nftsOwned += 1;
    await req.user.save();

    res.status(201).json({
      success: true,
      message: 'NFT minted successfully',
      data: { nft }
    });
  } catch (error: any) {
    console.error('Create NFT error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mint NFT',
      error: error.message
    });
  }
};

// Update NFT (owner only)
export const updateNFT = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const nft = await NFT.findById(id);
    if (!nft) {
      return res.status(404).json({
        success: false,
        message: 'NFT not found'
      });
    }

    // Check if user owns the NFT or is admin
    if (nft.owner.id.toString() !== req.user._id.toString() && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this NFT'
      });
    }

    // Only allow certain fields to be updated
    const allowedUpdates = ['pricing', 'tags'];
    const updates: any = {};
    
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const updatedNFT = await NFT.findByIdAndUpdate(
      id,
      updates,
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      message: 'NFT updated successfully',
      data: { nft: updatedNFT }
    });
  } catch (error: any) {
    console.error('Update NFT error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update NFT',
      error: error.message
    });
  }
};

// Transfer NFT ownership
export const transferNFT = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { newOwnerId, price } = req.body;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const nft = await NFT.findById(id);
    if (!nft) {
      return res.status(404).json({
        success: false,
        message: 'NFT not found'
      });
    }

    // Check if user owns the NFT
    if (nft.owner.id.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to transfer this NFT'
      });
    }

    // Get new owner details
    const newOwner = await req.user.constructor.findById(newOwnerId);
    if (!newOwner) {
      return res.status(404).json({
        success: false,
        message: 'New owner not found'
      });
    }

    // Update NFT ownership
    nft.owner = {
      id: newOwner._id,
      name: newOwner.name,
      avatar: newOwner.avatar || '',
      walletAddress: newOwner.profile?.walletAddress || ''
    };

    // Update trading stats if this was a sale
    if (price) {
      nft.trading.totalSales += 1;
      nft.trading.lastSalePrice = price;
      nft.trading.lastSaleDate = new Date();
      if (price > nft.trading.highestSale) {
        nft.trading.highestSale = price;
      }
    }

    nft.status = 'sold';
    await nft.save();

    // Update user stats
    req.user.stats.nftsOwned -= 1;
    newOwner.stats.nftsOwned += 1;
    await Promise.all([req.user.save(), newOwner.save()]);

    res.json({
      success: true,
      message: 'NFT transferred successfully',
      data: { nft }
    });
  } catch (error: any) {
    console.error('Transfer NFT error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to transfer NFT',
      error: error.message
    });
  }
};

// Like/Unlike NFT
export const toggleNFTLike = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const nft = await NFT.findById(id);
    if (!nft) {
      return res.status(404).json({
        success: false,
        message: 'NFT not found'
      });
    }

    // For simplicity, we'll just increment/decrement likes
    // In a real app, you'd track which users liked which NFTs
    nft.trading.likes += 1;
    await nft.save();

    res.json({
      success: true,
      message: 'NFT liked successfully',
      data: { likes: nft.trading.likes }
    });
  } catch (error: any) {
    console.error('Toggle NFT like error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to like NFT',
      error: error.message
    });
  }
};
