import React, { useState } from 'react';
import { 
  User, 
  <PERSON>ting<PERSON>, 
  Bell, 
  Shield, 
  CreditCard, 
  Award, 
  TrendingUp,
  Calendar,
  MapPin,
  Mail,
  Phone,
  Edit,
  Camera,
  Star,
  Zap,
  Target,
  Heart
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const Profile: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const { state } = useApp();

  const tabs = [
    { id: 'overview', name: 'Overview', icon: User },
    { id: 'achievements', name: 'Achievements', icon: Award },
    { id: 'settings', name: 'Settings', icon: Settings },
    { id: 'billing', name: 'Billing', icon: CreditCard }
  ];

  const achievements = [
    { 
      title: 'Early Adopter', 
      description: 'Joined NeuroSphere in the first month', 
      icon: Star, 
      earned: true,
      date: 'Jan 2025',
      rarity: 'Legendary'
    },
    { 
      title: 'Content Creator', 
      description: 'Published 50+ pieces of content', 
      icon: Zap, 
      earned: true,
      date: 'Feb 2025',
      rarity: 'Epic'
    },
    { 
      title: 'Learning Enthusiast', 
      description: 'Completed 10 courses', 
      icon: Target, 
      earned: true,
      date: 'Mar 2025',
      rarity: 'Rare'
    },
    { 
      title: 'Community Builder', 
      description: 'Helped 1000+ users', 
      icon: Heart, 
      earned: false,
      date: null,
      rarity: 'Legendary'
    }
  ];

  const stats = [
    { label: 'Total Points', value: state.user?.points.toLocaleString() || '0', icon: Star, color: 'from-yellow-500 to-yellow-600' },
    { label: 'Level', value: state.user?.level || 1, icon: TrendingUp, color: 'from-blue-500 to-blue-600' },
    { label: 'Courses Completed', value: '12', icon: Award, color: 'from-green-500 to-green-600' },
    { label: 'Days Active', value: '89', icon: Calendar, color: 'from-purple-500 to-purple-600' }
  ];

  const activityData = [
    { date: '2025-01-15', activity: 'Completed AI Fundamentals Course', points: 500 },
    { date: '2025-01-14', activity: 'Published new content', points: 250 },
    { date: '2025-01-13', activity: 'Reached 1000 followers', points: 1000 },
    { date: '2025-01-12', activity: 'Completed daily tasks', points: 100 },
    { date: '2025-01-11', activity: 'Made first investment', points: 300 }
  ];

  const subscriptions = [
    { name: 'NeuroSphere Premium', price: 49, status: 'Active', nextBilling: '2025-02-15' },
    { name: 'Creator Pro Tools', price: 29, status: 'Active', nextBilling: '2025-02-10' },
    { name: 'AI Analytics Plus', price: 19, status: 'Cancelled', nextBilling: null }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Legendary': return 'from-yellow-500 to-orange-500';
      case 'Epic': return 'from-purple-500 to-pink-500';
      case 'Rare': return 'from-blue-500 to-cyan-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
          <div className="relative">
            <img
              src={state.user?.avatar}
              alt={state.user?.name}
              className="w-32 h-32 rounded-full border-4 border-purple-400"
            />
            <button className="absolute bottom-2 right-2 bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-full transition-colors">
              <Camera className="h-4 w-4" />
            </button>
          </div>
          <div className="flex-1 text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start space-x-3 mb-2">
              <h2 className="text-3xl font-bold text-white">{state.user?.name}</h2>
              <button className="text-white/60 hover:text-white transition-colors">
                <Edit className="h-5 w-5" />
              </button>
            </div>
            <p className="text-purple-300 text-lg mb-4">Level {state.user?.level} Creator</p>
            <div className="flex flex-wrap justify-center md:justify-start gap-4 text-sm text-white/70">
              <div className="flex items-center space-x-1">
                <Mail className="h-4 w-4" />
                <span>{state.user?.email}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MapPin className="h-4 w-4" />
                <span>San Francisco, CA</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Joined Jan 2025</span>
              </div>
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-1">${state.user?.balance.toFixed(2)}</div>
            <div className="text-white/60">Account Balance</div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
            <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${stat.color} mb-4`}>
              <stat.icon className="h-8 w-8 text-white" />
            </div>
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-white/60 text-sm">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div>
        <h3 className="text-2xl font-bold text-white mb-6">Recent Activity</h3>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="space-y-4">
            {activityData.map((activity, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0">
                <div>
                  <div className="text-white font-medium">{activity.activity}</div>
                  <div className="text-white/60 text-sm">{activity.date}</div>
                </div>
                <div className="text-green-400 font-semibold">+{activity.points} pts</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderAchievements = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Your Achievements</h3>
        <p className="text-white/70 text-lg">Unlock badges and rewards as you explore NeuroSphere</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {achievements.map((achievement, index) => (
          <div key={index} className={`bg-gradient-to-br backdrop-blur-sm border rounded-2xl p-6 transition-all duration-300 ${
            achievement.earned 
              ? `from-white/15 to-white/10 border-white/20 hover:from-white/20 hover:to-white/15` 
              : 'from-white/5 to-white/5 border-white/10 opacity-60'
          }`}>
            <div className="flex items-start space-x-4">
              <div className={`p-4 rounded-2xl ${
                achievement.earned 
                  ? `bg-gradient-to-r ${getRarityColor(achievement.rarity)}` 
                  : 'bg-white/10'
              }`}>
                <achievement.icon className={`h-8 w-8 ${
                  achievement.earned ? 'text-white' : 'text-white/40'
                }`} />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className={`text-lg font-semibold ${
                    achievement.earned ? 'text-white' : 'text-white/60'
                  }`}>
                    {achievement.title}
                  </h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(achievement.rarity)} text-white`}>
                    {achievement.rarity}
                  </span>
                </div>
                <p className={`text-sm mb-3 ${
                  achievement.earned ? 'text-white/70' : 'text-white/50'
                }`}>
                  {achievement.description}
                </p>
                {achievement.earned ? (
                  <div className="text-green-400 text-sm font-medium">
                    Earned on {achievement.date}
                  </div>
                ) : (
                  <div className="text-white/40 text-sm">
                    Not earned yet
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Progress to Next Level */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="text-center mb-6">
          <h4 className="text-2xl font-bold text-white mb-2">Level Progress</h4>
          <p className="text-white/70">You're 2,580 points away from Level {(state.user?.level || 1) + 1}</p>
        </div>
        <div className="max-w-md mx-auto">
          <div className="flex justify-between text-sm mb-2">
            <span className="text-white/70">Level {state.user?.level}</span>
            <span className="text-white/70">Level {(state.user?.level || 1) + 1}</span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-4">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-4 rounded-full transition-all duration-300"
              style={{ width: '72%' }}
            ></div>
          </div>
          <div className="text-center mt-2 text-white/60 text-sm">
            {state.user?.points.toLocaleString()} / {((state.user?.points || 0) + 2580).toLocaleString()} points
          </div>
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-8">
      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Account Settings</h3>
          <div className="space-y-4">
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Bell className="h-5 w-5 text-blue-400" />
                  <div>
                    <div className="text-white font-medium">Notifications</div>
                    <div className="text-white/60 text-sm">Manage your notification preferences</div>
                  </div>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                  Configure
                </button>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-400" />
                  <div>
                    <div className="text-white font-medium">Privacy & Security</div>
                    <div className="text-white/60 text-sm">Control your privacy settings</div>
                  </div>
                </div>
                <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                  Manage
                </button>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-purple-400" />
                  <div>
                    <div className="text-white font-medium">Profile Information</div>
                    <div className="text-white/60 text-sm">Update your personal details</div>
                  </div>
                </div>
                <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                  Edit
                </button>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Preferences</h3>
          <div className="space-y-4">
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white font-medium">Dark Mode</span>
                <div className="bg-blue-600 w-12 h-6 rounded-full p-1 cursor-pointer">
                  <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                </div>
              </div>
              <p className="text-white/60 text-sm">Use dark theme across the platform</p>
            </div>

            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white font-medium">AI Recommendations</span>
                <div className="bg-blue-600 w-12 h-6 rounded-full p-1 cursor-pointer">
                  <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                </div>
              </div>
              <p className="text-white/60 text-sm">Enable personalized AI suggestions</p>
            </div>

            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white font-medium">Auto-Save Progress</span>
                <div className="bg-blue-600 w-12 h-6 rounded-full p-1 cursor-pointer">
                  <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                </div>
              </div>
              <p className="text-white/60 text-sm">Automatically save your learning progress</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBilling = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Billing & Subscriptions</h3>
        <p className="text-white/70 text-lg">Manage your subscriptions and payment methods</p>
      </div>

      {/* Current Balance */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 text-center">
        <div className="text-4xl font-bold text-white mb-2">${state.user?.balance.toFixed(2)}</div>
        <div className="text-green-400 text-lg mb-4">Current Balance</div>
        <button className="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors">
          Add Funds
        </button>
      </div>

      {/* Subscriptions */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Active Subscriptions</h4>
        <div className="space-y-4">
          {subscriptions.map((subscription, index) => (
            <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="text-lg font-semibold text-white mb-1">{subscription.name}</h5>
                  <div className="flex items-center space-x-4 text-sm text-white/60">
                    <span>${subscription.price}/month</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      subscription.status === 'Active' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {subscription.status}
                    </span>
                    {subscription.nextBilling && (
                      <span>Next billing: {subscription.nextBilling}</span>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  {subscription.status === 'Active' ? (
                    <>
                      <button className="bg-white/10 border border-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/20 transition-colors text-sm">
                        Modify
                      </button>
                      <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                        Cancel
                      </button>
                    </>
                  ) : (
                    <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                      Reactivate
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Payment Methods */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Payment Methods</h4>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 w-12 h-8 rounded flex items-center justify-center">
                <CreditCard className="h-4 w-4 text-white" />
              </div>
              <div>
                <div className="text-white font-medium">•••• •••• •••• 4242</div>
                <div className="text-white/60 text-sm">Expires 12/27</div>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="bg-white/10 border border-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/20 transition-colors text-sm">
                Edit
              </button>
              <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                Remove
              </button>
            </div>
          </div>
        </div>
        <button className="mt-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors">
          Add Payment Method
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Your <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">Profile</span>
          </h1>
          <p className="text-white/70 text-lg">Manage your account, achievements, and preferences</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'achievements' && renderAchievements()}
          {activeTab === 'settings' && renderSettings()}
          {activeTab === 'billing' && renderBilling()}
        </div>
      </div>
    </div>
  );
};

export default Profile;