import React, { useState } from 'react';
import { 
  Video, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Play, 
  Upload, 
  Eye, 
  Heart, 
  MessageCircle, 
  Share2,
  Calendar,
  BarChart3,
  Settings,
  Star,
  Gift,
  Zap
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const CreatorHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { state } = useApp();

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
    { id: 'content', name: 'Content', icon: Video },
    { id: 'audience', name: 'Audience', icon: Users },
    { id: 'monetization', name: 'Monetization', icon: DollarSign },
    { id: 'analytics', name: 'Analytics', icon: TrendingUp }
  ];

  const stats = [
    { label: 'Total Followers', value: '847K', change: '+12.5%', icon: Users, color: 'from-blue-500 to-blue-600' },
    { label: 'Monthly Revenue', value: '$24,847', change: '+18.2%', icon: DollarSign, color: 'from-green-500 to-green-600' },
    { label: 'Content Views', value: '2.3M', change: '+8.7%', icon: Eye, color: 'from-purple-500 to-purple-600' },
    { label: 'Engagement Rate', value: '9.4%', change: '+2.1%', icon: Heart, color: 'from-pink-500 to-pink-600' }
  ];

  const recentContent = [
    {
      id: '1',
      title: 'AI Tools for Content Creators',
      type: 'Video',
      views: 125420,
      likes: 8934,
      comments: 567,
      revenue: 1247,
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=300',
      status: 'Published',
      publishedAt: '2 hours ago'
    },
    {
      id: '2',
      title: 'Live: Q&A Session',
      type: 'Live Stream',
      views: 45230,
      likes: 3421,
      comments: 892,
      revenue: 567,
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=300',
      status: 'Live',
      publishedAt: 'Now'
    },
    {
      id: '3',
      title: 'Behind the Scenes: Studio Tour',
      type: 'Video',
      views: 89340,
      likes: 6789,
      comments: 234,
      revenue: 892,
      thumbnail: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=300',
      status: 'Published',
      publishedAt: '1 day ago'
    }
  ];

  const monetizationOptions = [
    {
      title: 'Subscriptions',
      description: 'Monthly recurring revenue from loyal fans',
      revenue: '$12,450',
      subscribers: 2847,
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Brand Partnerships',
      description: 'Sponsored content and collaborations',
      revenue: '$8,920',
      deals: 12,
      icon: Star,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'Digital Products',
      description: 'Courses, templates, and digital goods',
      revenue: '$3,477',
      sales: 156,
      icon: Gift,
      color: 'from-green-500 to-green-600'
    }
  ];

  const upcomingStreams = [
    {
      id: '1',
      title: 'Tech Review: Latest Gadgets',
      scheduledFor: 'Today, 8:00 PM',
      expectedViewers: 15000,
      category: 'Technology'
    },
    {
      id: '2',
      title: 'Creator Collaboration Stream',
      scheduledFor: 'Tomorrow, 3:00 PM',
      expectedViewers: 8500,
      category: 'Collaboration'
    }
  ];

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <span className="text-green-400 text-sm font-medium">{stat.change}</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-white/60 text-sm">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-3 gap-6">
        <button className="bg-gradient-to-r from-red-600 to-red-700 text-white p-6 rounded-2xl text-left hover:from-red-700 hover:to-red-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Video className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Go Live</span>
          </div>
          <p className="text-red-100">Start streaming instantly to your audience</p>
        </button>

        <button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-2xl text-left hover:from-blue-700 hover:to-blue-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Upload className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Upload Content</span>
          </div>
          <p className="text-blue-100">Share your latest video or post</p>
        </button>

        <button className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6 rounded-2xl text-left hover:from-purple-700 hover:to-purple-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Calendar className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Schedule Stream</span>
          </div>
          <p className="text-purple-100">Plan your next live session</p>
        </button>
      </div>

      {/* Recent Content */}
      <div>
        <h3 className="text-2xl font-bold text-white mb-6">Recent Content</h3>
        <div className="space-y-4">
          {recentContent.map((content) => (
            <div key={content.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center space-x-4">
                <img
                  src={content.thumbnail}
                  alt={content.title}
                  className="w-20 h-20 rounded-xl object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="text-lg font-semibold text-white">{content.title}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      content.status === 'Live' 
                        ? 'bg-red-500 text-white' 
                        : 'bg-green-500/20 text-green-400'
                    }`}>
                      {content.status}
                    </span>
                  </div>
                  <div className="flex items-center space-x-6 text-white/60 text-sm mb-2">
                    <span>{content.type}</span>
                    <span>{content.publishedAt}</span>
                  </div>
                  <div className="flex items-center space-x-6 text-sm">
                    <div className="flex items-center space-x-1 text-white/70">
                      <Eye className="h-4 w-4" />
                      <span>{content.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-white/70">
                      <Heart className="h-4 w-4" />
                      <span>{content.likes.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-white/70">
                      <MessageCircle className="h-4 w-4" />
                      <span>{content.comments}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-green-400">
                      <DollarSign className="h-4 w-4" />
                      <span>${content.revenue}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-white/60 hover:text-white transition-colors">
                    <Settings className="h-5 w-5" />
                  </button>
                  <button className="p-2 text-white/60 hover:text-white transition-colors">
                    <Share2 className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMonetization = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Monetization Dashboard</h3>
        <p className="text-white/70 text-lg">Track and optimize your revenue streams</p>
      </div>

      {/* Revenue Overview */}
      <div className="bg-gradient-to-br from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="text-center mb-6">
          <div className="text-4xl font-bold text-white mb-2">$24,847</div>
          <div className="text-green-400 text-lg">Total Monthly Revenue</div>
          <div className="text-white/60">+18.2% from last month</div>
        </div>
        <div className="grid md:grid-cols-3 gap-6">
          {monetizationOptions.map((option, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
              <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${option.color} mb-4`}>
                <option.icon className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-white mb-2">{option.title}</h4>
              <p className="text-white/70 text-sm mb-4">{option.description}</p>
              <div className="text-2xl font-bold text-green-400 mb-2">{option.revenue}</div>
              <div className="text-white/60 text-sm">
                {option.subscribers && `${option.subscribers} subscribers`}
                {option.deals && `${option.deals} active deals`}
                {option.sales && `${option.sales} sales this month`}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Upcoming Streams */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Scheduled Streams</h4>
        <div className="space-y-4">
          {upcomingStreams.map((stream) => (
            <div key={stream.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="text-lg font-semibold text-white mb-1">{stream.title}</h5>
                  <div className="flex items-center space-x-4 text-white/60 text-sm">
                    <span>{stream.scheduledFor}</span>
                    <span>Expected: {stream.expectedViewers.toLocaleString()} viewers</span>
                    <span className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full">{stream.category}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="bg-gradient-to-r from-red-600 to-red-700 text-white px-4 py-2 rounded-lg hover:from-red-700 hover:to-red-800 transition-colors">
                    Go Live
                  </button>
                  <button className="bg-white/10 border border-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/20 transition-colors">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Creator <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Hub</span>
          </h1>
          <p className="text-white/70 text-lg">Manage your content, audience, and revenue streams</p>
        </div>

        {/* Creator Stats Banner */}
        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <img
                src={state.user?.avatar}
                alt={state.user?.name}
                className="w-16 h-16 rounded-full border-4 border-purple-400"
              />
              <div>
                <h2 className="text-2xl font-bold text-white">{state.user?.name}</h2>
                <p className="text-purple-300">Creator Level {state.user?.level}</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">847K</div>
                <div className="text-white/60 text-sm">Followers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">$24.8K</div>
                <div className="text-white/60 text-sm">Monthly Revenue</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">9.4%</div>
                <div className="text-white/60 text-sm">Engagement</div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'monetization' && renderMonetization()}
          {activeTab === 'content' && (
            <div className="text-center py-12">
              <Video className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Content Management</h3>
              <p className="text-white/60">Upload, edit, and manage your content library</p>
            </div>
          )}
          {activeTab === 'audience' && (
            <div className="text-center py-12">
              <Users className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Audience Analytics</h3>
              <p className="text-white/60">Understand your audience demographics and behavior</p>
            </div>
          )}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <TrendingUp className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Advanced Analytics</h3>
              <p className="text-white/60">Deep insights into your content performance</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreatorHub;