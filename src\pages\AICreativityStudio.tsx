import React, { useState } from 'react';
import { Sparkles, Palette, Music, Video, FileText, Users, TrendingUp, DollarSign, Zap, Globe, Star, Play, Download, Share2, Heart, Eye, MessageCircle, Coins, Trophy, Target, Rocket, Brain, Wand2, Image, Mic, Camera, PenTool, Layers, Shuffle, Settings, Crown, Gem, CloudLightning as Lightning, Flame } from 'lucide-react';
import { useApp } from '../context/AppContext';

const AICreativityStudio: React.FC = () => {
  const [activeTab, setActiveTab] = useState('generator');
  const [selectedTool, setSelectedTool] = useState('text');
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<any>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [showNFTModal, setShowNFTModal] = useState(false);
  const [selectedCreation, setSelectedCreation] = useState<any>(null);
  const { state, dispatch } = useApp();

  const tabs = [
    { id: 'generator', name: 'AI Generator', icon: Sparkles },
    { id: 'collaborate', name: 'Collaborate', icon: Users },
    { id: 'marketplace', name: 'NFT Market', icon: Coins },
    { id: 'trends', name: 'Trends', icon: TrendingUp },
    { id: 'metaverse', name: 'Metaverse', icon: Globe },
    { id: 'monetize', name: 'Monetize', icon: DollarSign }
  ];

  const aiTools = [
    { 
      id: 'text', 
      name: 'Text Generator', 
      icon: FileText, 
      color: 'from-blue-500 to-blue-600',
      description: 'Generate articles, stories, scripts, and more',
      credits: 10
    },
    { 
      id: 'image', 
      name: 'Image Creator', 
      icon: Image, 
      color: 'from-purple-500 to-purple-600',
      description: 'Create stunning visuals and artwork',
      credits: 25
    },
    { 
      id: 'music', 
      name: 'Music Composer', 
      icon: Music, 
      color: 'from-green-500 to-green-600',
      description: 'Compose original music and soundtracks',
      credits: 50
    },
    { 
      id: 'video', 
      name: 'Video Producer', 
      icon: Video, 
      color: 'from-red-500 to-red-600',
      description: 'Generate and edit video content',
      credits: 100
    },
    { 
      id: 'voice', 
      name: 'Voice Synthesis', 
      icon: Mic, 
      color: 'from-orange-500 to-orange-600',
      description: 'Create realistic voice narrations',
      credits: 30
    },
    { 
      id: 'design', 
      name: 'Design Studio', 
      icon: PenTool, 
      color: 'from-pink-500 to-pink-600',
      description: 'Design logos, UI/UX, and graphics',
      credits: 40
    }
  ];

  const trendingCreations = [
    {
      id: '1',
      title: 'Cyberpunk City Landscape',
      type: 'Image',
      creator: 'DigitalArtist',
      views: 125420,
      likes: 8934,
      price: 0.5,
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      trending: true,
      rarity: 'Epic'
    },
    {
      id: '2',
      title: 'Ambient Space Journey',
      type: 'Music',
      creator: 'SoundWave',
      views: 89340,
      likes: 6789,
      price: 0.3,
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      trending: true,
      rarity: 'Rare'
    },
    {
      id: '3',
      title: 'AI-Generated Short Film',
      type: 'Video',
      creator: 'FilmMaker3000',
      views: 234567,
      likes: 15678,
      price: 2.5,
      thumbnail: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=400',
      trending: true,
      rarity: 'Legendary'
    }
  ];

  const collaborativeProjects = [
    {
      id: '1',
      title: 'Global Music Album',
      description: 'Collaborative album with artists worldwide',
      participants: 47,
      progress: 68,
      reward: 5000,
      deadline: '15 days',
      category: 'Music',
      difficulty: 'Advanced'
    },
    {
      id: '2',
      title: 'Virtual Art Gallery',
      description: 'Curated digital art exhibition',
      participants: 23,
      progress: 45,
      reward: 3200,
      deadline: '8 days',
      category: 'Visual Art',
      difficulty: 'Intermediate'
    },
    {
      id: '3',
      title: 'Interactive Story Game',
      description: 'Choose-your-adventure narrative',
      participants: 89,
      progress: 82,
      reward: 8500,
      deadline: '3 days',
      category: 'Interactive',
      difficulty: 'Expert'
    }
  ];

  const marketTrends = [
    { category: 'AI Art', growth: '+245%', volume: '$2.3M', icon: Palette },
    { category: 'Music NFTs', growth: '+189%', volume: '$1.8M', icon: Music },
    { category: 'Video Content', growth: '+156%', volume: '$3.1M', icon: Video },
    { category: 'Interactive Media', growth: '+298%', volume: '$1.2M', icon: Zap }
  ];

  const userStats = [
    { label: 'Creations', value: '247', icon: Sparkles, color: 'from-purple-500 to-purple-600' },
    { label: 'NFT Sales', value: '$12.4K', icon: Coins, color: 'from-green-500 to-green-600' },
    { label: 'Collaborations', value: '89', icon: Users, color: 'from-blue-500 to-blue-600' },
    { label: 'AI Credits', value: '2,450', icon: Zap, color: 'from-orange-500 to-orange-600' }
  ];

  const generateContent = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);

    // Simulate AI generation with realistic results
    setTimeout(() => {
      const mockResults = {
        text: {
          content: `# Generated Content\n\nBased on your prompt: "${prompt}"\n\nThis is a sample generated text that demonstrates the AI's capability to create engaging content. The AI has analyzed your requirements and produced this creative piece that aligns with your vision.\n\n## Key Features:\n- Creative storytelling\n- Engaging narrative\n- Professional quality\n- Customizable output`,
          wordCount: 156,
          readingTime: '2 min'
        },
        image: {
          url: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=800',
          dimensions: '1024x1024',
          style: 'Realistic',
          prompt: prompt
        },
        music: {
          url: '#',
          duration: '3:45',
          genre: 'Ambient',
          bpm: 120,
          key: 'C Major'
        },
        video: {
          url: '#',
          duration: '0:30',
          resolution: '1920x1080',
          fps: 30,
          format: 'MP4'
        },
        voice: {
          url: '#',
          duration: '1:23',
          voice: 'Natural Female',
          language: 'English',
          speed: 'Normal'
        },
        design: {
          url: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=800',
          type: 'Logo Design',
          format: 'SVG',
          colors: ['#6366f1', '#8b5cf6', '#ec4899']
        }
      };

      setGeneratedContent(mockResults[selectedTool as keyof typeof mockResults]);
      setIsGenerating(false);
      setShowEditor(true);

      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: `${aiTools.find(t => t.id === selectedTool)?.name} content generated successfully!`,
          type: 'success'
        }
      });
    }, 3000);
  };

  const saveCreation = () => {
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: 'Creation saved to your portfolio!',
        type: 'success'
      }
    });
    setShowEditor(false);
  };

  const mintAsNFT = () => {
    setShowNFTModal(true);
  };

  const shareCreation = () => {
    navigator.clipboard.writeText(window.location.href);
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: 'Link copied to clipboard!',
        type: 'success'
      }
    });
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Legendary': return 'from-yellow-500 to-orange-500';
      case 'Epic': return 'from-purple-500 to-pink-500';
      case 'Rare': return 'from-blue-500 to-cyan-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const renderGenerator = () => (
    <div className="space-y-8">
      {/* AI Tools Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {aiTools.map((tool) => (
          <div
            key={tool.id}
            onClick={() => setSelectedTool(tool.id)}
            className={`cursor-pointer bg-gradient-to-br backdrop-blur-sm border rounded-2xl p-6 transition-all duration-300 transform hover:scale-105 ${
              selectedTool === tool.id
                ? 'from-white/20 to-white/15 border-white/30 ring-2 ring-purple-500'
                : 'from-white/10 to-white/5 border-white/10 hover:from-white/15 hover:to-white/10'
            }`}
          >
            <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${tool.color} mb-4`}>
              <tool.icon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">{tool.name}</h3>
            <p className="text-white/70 text-sm mb-4">{tool.description}</p>
            <div className="flex items-center justify-between">
              <span className="text-orange-400 font-medium">{tool.credits} credits</span>
              <div className="flex items-center space-x-1">
                <Lightning className="h-4 w-4 text-yellow-400" />
                <span className="text-white/60 text-sm">AI Powered</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Generation Interface */}
      <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="text-center mb-8">
          <h3 className="text-3xl font-bold text-white mb-4">
            Create with AI <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Magic</span>
          </h3>
          <p className="text-white/70 text-lg">Transform your ideas into reality with our advanced AI tools</p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <label className="block text-white font-medium mb-3">
              Selected Tool: {aiTools.find(t => t.id === selectedTool)?.name}
            </label>
            <div className="relative">
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe what you want to create... Be as detailed as possible for better results."
                className="w-full bg-white/10 border border-white/20 rounded-xl p-4 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm resize-none h-32"
              />
              <div className="absolute bottom-3 right-3 text-white/40 text-sm">
                {prompt.length}/500
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">Style</label>
                <select className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option>Realistic</option>
                  <option>Artistic</option>
                  <option>Abstract</option>
                  <option>Futuristic</option>
                </select>
              </div>
              <div>
                <label className="block text-white/70 text-sm mb-2">Quality</label>
                <select className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                  <option>Standard</option>
                  <option>High</option>
                  <option>Ultra</option>
                </select>
              </div>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={generateContent}
              disabled={!prompt.trim() || isGenerating}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3 mx-auto"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Wand2 className="h-5 w-5" />
                  <span>Generate with AI</span>
                </>
              )}
            </button>
            <p className="text-white/60 text-sm mt-3">
              Cost: {aiTools.find(t => t.id === selectedTool)?.credits} credits
            </p>
          </div>
        </div>
      </div>

      {/* Recent Creations */}
      <div>
        <h3 className="text-2xl font-bold text-white mb-6">Your Recent Creations</h3>
        <div className="grid md:grid-cols-3 gap-6">
          {trendingCreations.slice(0, 3).map((creation) => (
            <div key={creation.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
              <div className="relative">
                <img
                  src={creation.thumbnail}
                  alt={creation.title}
                  className="w-full h-48 object-cover"
                />
                <div className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(creation.rarity)} text-white`}>
                  {creation.rarity}
                </div>
                <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                  {creation.type}
                </div>
              </div>
              <div className="p-4">
                <h4 className="text-lg font-semibold text-white mb-2">{creation.title}</h4>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-4 text-sm text-white/60">
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>{creation.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Heart className="h-4 w-4" />
                      <span>{creation.likes.toLocaleString()}</span>
                    </div>
                  </div>
                  <span className="text-green-400 font-medium">{creation.price} ETH</span>
                </div>
                <div className="flex space-x-2">
                  <button className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors text-sm">
                    Edit
                  </button>
                  <button className="flex-1 bg-white/10 border border-white/20 text-white py-2 rounded-lg hover:bg-white/20 transition-colors text-sm">
                    Share
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderCollaborate = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Global Creative Collaboration</h3>
        <p className="text-white/70 text-lg">Join creators worldwide to build amazing projects together</p>
      </div>

      {/* Active Projects */}
      <div className="space-y-6">
        {collaborativeProjects.map((project) => (
          <div key={project.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h4 className="text-xl font-semibold text-white">{project.title}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    project.difficulty === 'Expert' ? 'bg-red-500/20 text-red-400' :
                    project.difficulty === 'Advanced' ? 'bg-orange-500/20 text-orange-400' :
                    'bg-blue-500/20 text-blue-400'
                  }`}>
                    {project.difficulty}
                  </span>
                </div>
                <p className="text-white/70 mb-4">{project.description}</p>
                <div className="flex items-center space-x-6 text-sm text-white/60">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{project.participants} participants</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Target className="h-4 w-4" />
                    <span>{project.category}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <DollarSign className="h-4 w-4" />
                    <span>${project.reward.toLocaleString()} reward pool</span>
                  </div>
                </div>
              </div>
              <div className="lg:w-64">
                <div className="mb-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Progress</span>
                    <span className="text-white/70">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white/60 text-sm">Deadline: {project.deadline}</span>
                </div>
                <button className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors">
                  Join Project
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Collaboration Features */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-blue-600/20 to-blue-700/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
          <Globe className="h-12 w-12 text-blue-400 mx-auto mb-4" />
          <h4 className="text-lg font-semibold text-white mb-2">Real-time Collaboration</h4>
          <p className="text-white/70 text-sm">Work together in real-time with creators from around the world</p>
        </div>
        <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
          <Brain className="h-12 w-12 text-purple-400 mx-auto mb-4" />
          <h4 className="text-lg font-semibold text-white mb-2">AI-Powered Matching</h4>
          <p className="text-white/70 text-sm">Get matched with creators who complement your skills</p>
        </div>
        <div className="bg-gradient-to-br from-green-600/20 to-green-700/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
          <Trophy className="h-12 w-12 text-green-400 mx-auto mb-4" />
          <h4 className="text-lg font-semibold text-white mb-2">Shared Rewards</h4>
          <p className="text-white/70 text-sm">Earn rewards based on your contribution to collaborative projects</p>
        </div>
      </div>
    </div>
  );

  const renderMarketplace = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Creative NFT Marketplace</h3>
        <p className="text-white/70 text-lg">Discover, collect, and trade unique AI-generated creations</p>
      </div>

      {/* Trending NFTs */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {trendingCreations.map((creation) => (
          <div key={creation.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105">
            <div className="relative">
              <img
                src={creation.thumbnail}
                alt={creation.title}
                className="w-full h-48 object-cover"
              />
              {creation.trending && (
                <div className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                  <Flame className="h-3 w-3" />
                  <span>TRENDING</span>
                </div>
              )}
              <div className={`absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(creation.rarity)} text-white`}>
                {creation.rarity}
              </div>
            </div>
            <div className="p-4">
              <h4 className="text-lg font-semibold text-white mb-2">{creation.title}</h4>
              <p className="text-white/60 text-sm mb-3">by {creation.creator}</p>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 text-sm text-white/60">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{creation.views.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Heart className="h-4 w-4" />
                    <span>{creation.likes.toLocaleString()}</span>
                  </div>
                </div>
                <span className="text-green-400 font-bold">{creation.price} ETH</span>
              </div>
              <div className="flex space-x-2">
                <button className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors text-sm">
                  Buy Now
                </button>
                <button className="bg-white/10 border border-white/20 text-white p-2 rounded-lg hover:bg-white/20 transition-colors">
                  <Heart className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Market Stats */}
      <div className="grid md:grid-cols-4 gap-6">
        {marketTrends.map((trend, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
            <trend.icon className="h-8 w-8 text-purple-400 mx-auto mb-3" />
            <div className="text-lg font-semibold text-white mb-1">{trend.category}</div>
            <div className="text-green-400 font-bold mb-1">{trend.growth}</div>
            <div className="text-white/60 text-sm">{trend.volume} volume</div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTrends = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">AI Trend Prediction</h3>
        <p className="text-white/70 text-lg">Stay ahead with AI-powered market insights and trend analysis</p>
      </div>

      {/* Trend Analysis */}
      <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">This Week's Hot Trends</h4>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="bg-gradient-to-r from-red-500 to-orange-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-3">
              <Flame className="h-8 w-8 text-white" />
            </div>
            <h5 className="text-lg font-semibold text-white mb-2">Cyberpunk Art</h5>
            <p className="text-green-400 font-bold">+340% interest</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-3">
              <Music className="h-8 w-8 text-white" />
            </div>
            <h5 className="text-lg font-semibold text-white mb-2">Lo-fi Beats</h5>
            <p className="text-green-400 font-bold">+280% interest</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-3">
              <Video className="h-8 w-8 text-white" />
            </div>
            <h5 className="text-lg font-semibold text-white mb-2">Short Films</h5>
            <p className="text-green-400 font-bold">+195% interest</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-3">
              <Globe className="h-8 w-8 text-white" />
            </div>
            <h5 className="text-lg font-semibold text-white mb-2">Virtual Worlds</h5>
            <p className="text-green-400 font-bold">+425% interest</p>
          </div>
        </div>
      </div>

      {/* AI Predictions */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-4">AI Market Predictions</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white/70">Next Week's Top Category</span>
              <span className="text-purple-400 font-medium">Interactive Art</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">Predicted Growth</span>
              <span className="text-green-400 font-medium">+180%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">Best Time to Create</span>
              <span className="text-blue-400 font-medium">Next 3 days</span>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-4">Personalized Recommendations</h4>
          <div className="space-y-3">
            <div className="bg-blue-500/20 text-blue-400 p-3 rounded-lg text-sm">
              💡 Create cyberpunk-themed content for maximum engagement
            </div>
            <div className="bg-green-500/20 text-green-400 p-3 rounded-lg text-sm">
              🎵 Music NFTs are trending - consider audio content
            </div>
            <div className="bg-purple-500/20 text-purple-400 p-3 rounded-lg text-sm">
              🚀 Collaborate on virtual world projects for higher returns
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            AI Creativity <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Studio</span>
          </h1>
          <p className="text-white/70 text-lg">Transform your imagination into valuable digital assets with AI</p>
        </div>

        {/* User Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          {userStats.map((stat, index) => (
            <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
              <div className={`inline-flex p-3 rounded-xl bg-gradient-to-r ${stat.color} mb-3`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
              <div className="text-white/60 text-sm">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'generator' && renderGenerator()}
          {activeTab === 'collaborate' && renderCollaborate()}
          {activeTab === 'marketplace' && renderMarketplace()}
          {activeTab === 'trends' && renderTrends()}
          {activeTab === 'metaverse' && (
            <div className="text-center py-12">
              <Globe className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Metaverse Creation Studio</h3>
              <p className="text-white/60">Build immersive virtual experiences and worlds</p>
            </div>
          )}
          {activeTab === 'monetize' && (
            <div className="text-center py-12">
              <DollarSign className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Smart Monetization Engine</h3>
              <p className="text-white/60">AI-powered revenue optimization for your creations</p>
            </div>
          )}
        </div>
      </div>

      {/* Content Editor Modal */}
      {showEditor && generatedContent && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-2xl font-bold text-white">Generated Content Editor</h2>
              <button
                onClick={() => setShowEditor(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="grid lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  {selectedTool === 'text' && (
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Generated Text</h3>
                      <textarea
                        value={generatedContent.content}
                        onChange={(e) => setGeneratedContent({...generatedContent, content: e.target.value})}
                        className="w-full h-96 bg-white/10 border border-white/20 rounded-xl p-4 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                      />
                      <div className="flex items-center justify-between mt-4 text-sm text-white/60">
                        <span>Words: {generatedContent.wordCount}</span>
                        <span>Reading time: {generatedContent.readingTime}</span>
                      </div>
                    </div>
                  )}

                  {(selectedTool === 'image' || selectedTool === 'design') && (
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Generated {selectedTool === 'image' ? 'Image' : 'Design'}</h3>
                      <div className="bg-white/5 rounded-xl p-4">
                        <img
                          src={generatedContent.url}
                          alt="Generated content"
                          className="w-full rounded-lg mb-4"
                        />
                        <div className="grid grid-cols-2 gap-4 text-sm text-white/60">
                          <div>
                            <span className="text-white/80">Dimensions:</span> {generatedContent.dimensions || 'N/A'}
                          </div>
                          <div>
                            <span className="text-white/80">Style:</span> {generatedContent.style || generatedContent.type}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {(selectedTool === 'music' || selectedTool === 'voice') && (
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Generated {selectedTool === 'music' ? 'Music' : 'Voice'}</h3>
                      <div className="bg-white/5 rounded-xl p-6">
                        <div className="flex items-center justify-center h-48 bg-white/5 rounded-lg mb-4">
                          <div className="text-center">
                            <Play className="h-16 w-16 text-white/40 mx-auto mb-4" />
                            <p className="text-white/60">Audio Player</p>
                            <p className="text-white/40 text-sm">Duration: {generatedContent.duration}</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm text-white/60">
                          {selectedTool === 'music' && (
                            <>
                              <div><span className="text-white/80">Genre:</span> {generatedContent.genre}</div>
                              <div><span className="text-white/80">BPM:</span> {generatedContent.bpm}</div>
                              <div><span className="text-white/80">Key:</span> {generatedContent.key}</div>
                            </>
                          )}
                          {selectedTool === 'voice' && (
                            <>
                              <div><span className="text-white/80">Voice:</span> {generatedContent.voice}</div>
                              <div><span className="text-white/80">Language:</span> {generatedContent.language}</div>
                              <div><span className="text-white/80">Speed:</span> {generatedContent.speed}</div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedTool === 'video' && (
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Generated Video</h3>
                      <div className="bg-white/5 rounded-xl p-4">
                        <div className="aspect-video bg-white/5 rounded-lg flex items-center justify-center mb-4">
                          <div className="text-center">
                            <Play className="h-16 w-16 text-white/40 mx-auto mb-4" />
                            <p className="text-white/60">Video Player</p>
                            <p className="text-white/40 text-sm">Duration: {generatedContent.duration}</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm text-white/60">
                          <div><span className="text-white/80">Resolution:</span> {generatedContent.resolution}</div>
                          <div><span className="text-white/80">FPS:</span> {generatedContent.fps}</div>
                          <div><span className="text-white/80">Format:</span> {generatedContent.format}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="lg:col-span-1">
                  <div className="bg-white/5 rounded-xl p-6 sticky top-4">
                    <h4 className="text-lg font-semibold text-white mb-4">Actions</h4>
                    <div className="space-y-3">
                      <button
                        onClick={saveCreation}
                        className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-colors flex items-center justify-center space-x-2"
                      >
                        <Download className="h-5 w-5" />
                        <span>Save Creation</span>
                      </button>

                      <button
                        onClick={mintAsNFT}
                        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-xl hover:from-purple-700 hover:to-pink-700 transition-colors flex items-center justify-center space-x-2"
                      >
                        <Gem className="h-5 w-5" />
                        <span>Mint as NFT</span>
                      </button>

                      <button
                        onClick={shareCreation}
                        className="w-full bg-white/10 border border-white/20 text-white py-3 rounded-xl hover:bg-white/20 transition-colors flex items-center justify-center space-x-2"
                      >
                        <Share2 className="h-5 w-5" />
                        <span>Share</span>
                      </button>
                    </div>

                    <div className="mt-6 pt-6 border-t border-white/10">
                      <h5 className="text-white font-medium mb-3">Generation Details</h5>
                      <div className="space-y-2 text-sm text-white/60">
                        <div>Tool: {aiTools.find(t => t.id === selectedTool)?.name}</div>
                        <div>Prompt: "{prompt}"</div>
                        <div>Generated: {new Date().toLocaleString()}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* NFT Minting Modal */}
      {showNFTModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-2xl w-full">
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-2xl font-bold text-white">Mint as NFT</h2>
              <button
                onClick={() => setShowNFTModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-white font-medium mb-2">NFT Name</label>
                  <input
                    type="text"
                    placeholder="Enter NFT name"
                    className="w-full bg-white/10 border border-white/20 rounded-xl p-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>

                <div>
                  <label className="block text-white font-medium mb-2">Description</label>
                  <textarea
                    placeholder="Describe your NFT"
                    className="w-full bg-white/10 border border-white/20 rounded-xl p-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 resize-none"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white font-medium mb-2">Price (ETH)</label>
                    <input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      className="w-full bg-white/10 border border-white/20 rounded-xl p-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-white font-medium mb-2">Royalty (%)</label>
                    <input
                      type="number"
                      max="10"
                      placeholder="5"
                      className="w-full bg-white/10 border border-white/20 rounded-xl p-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-white font-medium mb-2">Collection</label>
                  <select className="w-full bg-white/10 border border-white/20 rounded-xl p-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option>AI Creations</option>
                    <option>Digital Art</option>
                    <option>Music Collection</option>
                    <option>Create New Collection</option>
                  </select>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={() => setShowNFTModal(false)}
                    className="flex-1 bg-white/10 border border-white/20 text-white py-3 rounded-xl hover:bg-white/20 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      setShowNFTModal(false);
                      dispatch({
                        type: 'ADD_NOTIFICATION',
                        payload: {
                          id: Date.now(),
                          message: 'NFT minting initiated! Check your wallet.',
                          type: 'success'
                        }
                      });
                    }}
                    className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-xl hover:from-purple-700 hover:to-pink-700 transition-colors"
                  >
                    Mint NFT
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AICreativityStudio;