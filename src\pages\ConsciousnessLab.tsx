import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Eye,
  Zap,
  Activity,
  Upload,
  Download,
  Copy,
  Merge,
  Split,
  RefreshCw,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  Settings,
  Monitor,
  Cpu,
  HardDrive,
  Wifi,
  Battery,
  Thermometer,
  Gauge,
  TrendingUp,
  BarChart3,
  PieChart,
  LineChart,
  Atom,
  Infinity,
  Sparkles,
  Star,
  Heart,
  Target
} from 'lucide-react';
import { useApp } from '../context/AppContext';

interface ConsciousnessProfile {
  id: string;
  name: string;
  type: 'human' | 'ai' | 'hybrid' | 'quantum';
  integrity: number;
  complexity: number;
  memories: number;
  skills: number;
  emotions: number;
  lastBackup: Date;
  status: 'active' | 'backed-up' | 'transferring' | 'archived';
}

interface TransferProcess {
  id: string;
  sourceProfile: string;
  targetMedium: string;
  progress: number;
  stage: string;
  fidelity: number;
  estimatedTime: number;
  status: 'preparing' | 'scanning' | 'transferring' | 'verifying' | 'completed' | 'failed';
}

const ConsciousnessLab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profiles');
  const [consciousnessProfiles, setConsciousnessProfiles] = useState<ConsciousnessProfile[]>([]);
  const [activeTransfers, setActiveTransfers] = useState<TransferProcess[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<ConsciousnessProfile | null>(null);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showBackupModal, setShowBackupModal] = useState(false);
  const [showMergeModal, setShowMergeModal] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { state, dispatch } = useApp();

  // Neural network visualization
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    const nodes: any[] = [];
    const connections: any[] = [];
    const nodeCount = 50;

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 4 + 2,
        activity: Math.random(),
        type: Math.random() > 0.7 ? 'memory' : Math.random() > 0.5 ? 'emotion' : 'logic'
      });
    }

    // Create connections
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        if (Math.random() > 0.85) {
          connections.push({
            from: i,
            to: j,
            strength: Math.random(),
            active: Math.random() > 0.5
          });
        }
      }
    }

    const animate = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw connections
      connections.forEach(conn => {
        const fromNode = nodes[conn.from];
        const toNode = nodes[conn.to];

        if (conn.active && Math.random() > 0.98) {
          conn.active = false;
        } else if (!conn.active && Math.random() > 0.995) {
          conn.active = true;
        }

        if (conn.active) {
          ctx.beginPath();
          ctx.moveTo(fromNode.x, fromNode.y);
          ctx.lineTo(toNode.x, toNode.y);
          ctx.strokeStyle = `rgba(100, 200, 255, ${conn.strength * 0.8})`;
          ctx.lineWidth = conn.strength * 2;
          ctx.stroke();
        }
      });

      // Update and draw nodes
      nodes.forEach(node => {
        // Update position
        node.x += node.vx;
        node.y += node.vy;

        // Bounce off edges
        if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
        if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

        // Update activity
        node.activity += (Math.random() - 0.5) * 0.1;
        node.activity = Math.max(0, Math.min(1, node.activity));

        // Draw node
        const colors = {
          memory: `rgba(255, 100, 100, ${node.activity})`,
          emotion: `rgba(100, 255, 100, ${node.activity})`,
          logic: `rgba(100, 100, 255, ${node.activity})`
        };

        ctx.beginPath();
        ctx.arc(node.x, node.y, node.size, 0, Math.PI * 2);
        ctx.fillStyle = colors[node.type as keyof typeof colors];
        ctx.fill();

        // Draw activity pulse
        if (node.activity > 0.7) {
          ctx.beginPath();
          ctx.arc(node.x, node.y, node.size * 2, 0, Math.PI * 2);
          ctx.strokeStyle = colors[node.type as keyof typeof colors];
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      });

      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    };
  }, []);

  // Load consciousness profiles
  useEffect(() => {
    const loadProfiles = async () => {
      // Mock data for now
      setConsciousnessProfiles([
        {
          id: '1',
          name: 'Primary Consciousness',
          type: 'human',
          integrity: 98.7,
          complexity: 847,
          memories: 2847392,
          skills: 1247,
          emotions: 89.3,
          lastBackup: new Date(Date.now() - 24 * 60 * 60 * 1000),
          status: 'active'
        },
        {
          id: '2',
          name: 'AI Assistant Alpha',
          type: 'ai',
          integrity: 99.9,
          complexity: 1247,
          memories: 5847392,
          skills: 2847,
          emotions: 67.8,
          lastBackup: new Date(Date.now() - 2 * 60 * 60 * 1000),
          status: 'backed-up'
        },
        {
          id: '3',
          name: 'Hybrid Entity Beta',
          type: 'hybrid',
          integrity: 94.2,
          complexity: 2847,
          memories: 8472639,
          skills: 3847,
          emotions: 92.1,
          lastBackup: new Date(Date.now() - 12 * 60 * 60 * 1000),
          status: 'active'
        }
      ]);

      setActiveTransfers([
        {
          id: '1',
          sourceProfile: 'Quantum Consciousness Zeta',
          targetMedium: 'Quantum Substrate',
          progress: 67,
          stage: 'Memory Transfer',
          fidelity: 99.2,
          estimatedTime: 847,
          status: 'transferring'
        }
      ]);
    };

    loadProfiles();
  }, []);

  const startConsciousnessScan = async () => {
    setIsScanning(true);
    setScanProgress(0);

    // Simulate scanning process
    const interval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsScanning(false);
          dispatch({
            type: 'ADD_NOTIFICATION',
            payload: {
              id: Date.now(),
              message: 'Consciousness scan completed successfully!',
              type: 'success'
            }
          });
          return 100;
        }
        return prev + Math.random() * 5;
      });
    }, 200);
  };

  const initiateTransfer = async (profileId: string, targetMedium: string) => {
    try {
      const newTransfer: TransferProcess = {
        id: `transfer_${Date.now()}`,
        sourceProfile: consciousnessProfiles.find(p => p.id === profileId)?.name || 'Unknown',
        targetMedium,
        progress: 0,
        stage: 'Preparing',
        fidelity: 0,
        estimatedTime: 3600,
        status: 'preparing'
      };

      setActiveTransfers([...activeTransfers, newTransfer]);
      setShowTransferModal(false);

      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Consciousness transfer initiated',
          type: 'info'
        }
      });
    } catch (error) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Failed to initiate transfer',
          type: 'error'
        }
      });
    }
  };

  const createBackup = async (profileId: string) => {
    try {
      const profile = consciousnessProfiles.find(p => p.id === profileId);
      if (profile) {
        profile.lastBackup = new Date();
        profile.status = 'backed-up';
        setConsciousnessProfiles([...consciousnessProfiles]);

        dispatch({
          type: 'ADD_NOTIFICATION',
          payload: {
            id: Date.now(),
            message: `Backup created for ${profile.name}`,
            type: 'success'
          }
        });
      }
    } catch (error) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Failed to create backup',
          type: 'error'
        }
      });
    }
  };

  const tabs = [
    { id: 'profiles', name: 'Consciousness Profiles', icon: Brain },
    { id: 'transfers', name: 'Active Transfers', icon: RefreshCw },
    { id: 'scanner', name: 'Neural Scanner', icon: Eye },
    { id: 'lab', name: 'Quantum Lab', icon: Atom },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'backed-up': return 'text-blue-400';
      case 'transferring': return 'text-yellow-400';
      case 'archived': return 'text-gray-400';
      default: return 'text-white';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'human': return 'from-blue-500 to-cyan-500';
      case 'ai': return 'from-purple-500 to-pink-500';
      case 'hybrid': return 'from-green-500 to-emerald-500';
      case 'quantum': return 'from-yellow-500 to-orange-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/50 backdrop-blur-sm border-b border-purple-500/30">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Brain className="h-10 w-10 text-purple-400" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  Quantum Consciousness Laboratory
                </h1>
                <p className="text-purple-300 text-sm">Advanced consciousness transfer and backup systems</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">99.7%</div>
                <div className="text-xs text-purple-300">System Integrity</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{consciousnessProfiles.length}</div>
                <div className="text-xs text-purple-300">Active Profiles</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{activeTransfers.length}</div>
                <div className="text-xs text-purple-300">Transfers</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-black/30 backdrop-blur-sm border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex space-x-1 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-6 py-4 text-sm font-medium transition-all duration-300 border-b-2 whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'text-purple-400 border-purple-400 bg-purple-500/10'
                      : 'text-purple-300 border-transparent hover:text-purple-200 hover:bg-purple-500/5'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {activeTab === 'profiles' && (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold text-white">Consciousness Profiles</h2>
              <div className="flex space-x-4">
                <button
                  onClick={() => setShowBackupModal(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-300"
                >
                  <Upload className="h-5 w-5 inline mr-2" />
                  Create Backup
                </button>
                <button
                  onClick={() => setShowTransferModal(true)}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-3 rounded-xl transition-all duration-300"
                >
                  <RefreshCw className="h-5 w-5 inline mr-2" />
                  Transfer
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {consciousnessProfiles.map((profile) => (
                <div
                  key={profile.id}
                  className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-3xl p-8 hover:from-white/15 hover:to-white/10 transition-all duration-300 cursor-pointer"
                  onClick={() => setSelectedProfile(profile)}
                >
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-4 rounded-2xl bg-gradient-to-r ${getTypeColor(profile.type)}`}>
                        <Brain className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white">{profile.name}</h3>
                        <p className="text-purple-300 capitalize">{profile.type} Consciousness</p>
                      </div>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(profile.status)} bg-current/20`}>
                      {profile.status}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">{profile.integrity}%</div>
                      <div className="text-xs text-purple-300">Integrity</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-400">{profile.complexity}</div>
                      <div className="text-xs text-purple-300">Complexity</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-400">{(profile.memories / 1000000).toFixed(1)}M</div>
                      <div className="text-xs text-purple-300">Memories</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-400">{profile.skills}</div>
                      <div className="text-xs text-purple-300">Skills</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-purple-300">Emotional Depth</span>
                      <span className="text-white">{profile.emotions}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-pink-500 to-red-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${profile.emotions}%` }}
                      ></div>
                    </div>

                    <div className="text-xs text-purple-300 mt-4">
                      Last backup: {profile.lastBackup.toLocaleDateString()}
                    </div>
                  </div>

                  <div className="flex space-x-2 mt-6">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        createBackup(profile.id);
                      }}
                      className="flex-1 bg-blue-600/20 border border-blue-500/30 text-blue-400 py-2 rounded-lg hover:bg-blue-600/30 transition-colors text-sm"
                    >
                      Backup
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedProfile(profile);
                        setShowTransferModal(true);
                      }}
                      className="flex-1 bg-purple-600/20 border border-purple-500/30 text-purple-400 py-2 rounded-lg hover:bg-purple-600/30 transition-colors text-sm"
                    >
                      Transfer
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'scanner' && (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Neural Consciousness Scanner</h2>
              <p className="text-purple-300 text-lg">Advanced quantum scanning for consciousness mapping</p>
            </div>

            {/* Neural Network Visualization */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">Live Neural Activity</h3>
              <div className="relative">
                <canvas
                  ref={canvasRef}
                  className="w-full h-96 rounded-2xl bg-black/50"
                />
                <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-white text-sm">
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span>Memory Centers</span>
                    </div>
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span>Emotional Processing</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Logic Centers</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Scanning Controls */}
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-3xl p-8">
                <h3 className="text-xl font-bold text-white mb-6">Scan Configuration</h3>

                <div className="space-y-6">
                  <div>
                    <label className="block text-purple-300 text-sm mb-2">Scan Depth</label>
                    <select className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white">
                      <option>Surface Level</option>
                      <option>Deep Scan</option>
                      <option>Quantum Level</option>
                      <option>Complete Mapping</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-purple-300 text-sm mb-2">Target Areas</label>
                    <div className="space-y-2">
                      {['Memory Centers', 'Emotional Processing', 'Logic Networks', 'Creative Regions', 'Subconscious'].map((area) => (
                        <label key={area} className="flex items-center space-x-3">
                          <input type="checkbox" className="rounded" defaultChecked />
                          <span className="text-white text-sm">{area}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={startConsciousnessScan}
                    disabled={isScanning}
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-colors disabled:opacity-50"
                  >
                    {isScanning ? 'Scanning...' : 'Start Neural Scan'}
                  </button>
                </div>
              </div>

              <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-3xl p-8">
                <h3 className="text-xl font-bold text-white mb-6">Scan Results</h3>

                {isScanning ? (
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-purple-300">Scanning Progress</span>
                      <span className="text-white">{scanProgress.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${scanProgress}%` }}
                      ></div>
                    </div>
                    <div className="text-center text-purple-300 text-sm mt-4">
                      Analyzing neural pathways...
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">98.7%</div>
                        <div className="text-xs text-purple-300">Neural Integrity</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">2.4M</div>
                        <div className="text-xs text-purple-300">Active Neurons</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">847K</div>
                        <div className="text-xs text-purple-300">Connections</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-400">94.2%</div>
                        <div className="text-xs text-purple-300">Coherence</div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-white font-semibold mb-3">Detected Patterns</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-purple-300">Memory Formation</span>
                          <span className="text-green-400">Optimal</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-purple-300">Emotional Processing</span>
                          <span className="text-blue-400">Enhanced</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-purple-300">Creative Networks</span>
                          <span className="text-purple-400">Highly Active</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-purple-300">Logic Centers</span>
                          <span className="text-yellow-400">Stable</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Add other tab content here */}
      </div>
    </div>
  );
};

export default ConsciousnessLab;