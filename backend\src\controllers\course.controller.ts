import { Request, Response } from 'express';
import { Course } from '../models/course.model';

// Get all courses with filtering and pagination
export const getCourses = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 12,
      category,
      level,
      minPrice,
      maxPrice,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      featured,
      instructor
    } = req.query;

    // Build filter object
    const filter: any = { isPublished: true };

    if (category) filter.category = category;
    if (level) filter.level = level;
    if (featured !== undefined) filter.isFeatured = featured === 'true';
    if (instructor) filter['instructor.id'] = instructor;

    // Price range filter
    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = Number(minPrice);
      if (maxPrice) filter.price.$lte = Number(maxPrice);
    }

    // Search filter
    if (search) {
      filter.$or = [
        { title: new RegExp(search as string, 'i') },
        { description: new RegExp(search as string, 'i') },
        { skills: { $in: [new RegExp(search as string, 'i')] } },
        { tags: { $in: [new RegExp(search as string, 'i')] } }
      ];
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (Number(page) - 1) * Number(limit);
    
    const [courses, total] = await Promise.all([
      Course.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .populate('instructor.id', 'name avatar profile.bio'),
      Course.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / Number(limit));

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          currentPage: Number(page),
          totalPages,
          totalItems: total,
          itemsPerPage: Number(limit),
          hasNextPage: Number(page) < totalPages,
          hasPrevPage: Number(page) > 1
        }
      }
    });
  } catch (error: any) {
    console.error('Get courses error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch courses',
      error: error.message
    });
  }
};

// Get single course by ID
export const getCourseById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const course = await Course.findById(id)
      .populate('instructor.id', 'name avatar profile.bio')
      .populate('reviews.userId', 'name avatar');

    if (!course || !course.isPublished) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    res.json({
      success: true,
      data: { course }
    });
  } catch (error: any) {
    console.error('Get course error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch course',
      error: error.message
    });
  }
};

// Create new course
export const createCourse = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user has creator role
    if (!req.user.roles.includes('creator') && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Creator role required to create courses'
      });
    }

    const courseData = {
      ...req.body,
      instructor: {
        id: req.user._id,
        name: req.user.name,
        avatar: req.user.avatar,
        bio: req.user.profile?.bio || '',
        rating: 0
      }
    };

    const course = new Course(courseData);
    await course.save();

    res.status(201).json({
      success: true,
      message: 'Course created successfully',
      data: { course }
    });
  } catch (error: any) {
    console.error('Create course error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create course',
      error: error.message
    });
  }
};

// Update course
export const updateCourse = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user owns the course or is admin
    if (course.instructor.id.toString() !== req.user._id.toString() && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this course'
      });
    }

    const updatedCourse = await Course.findByIdAndUpdate(
      id,
      { ...req.body, lastUpdated: new Date() },
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      message: 'Course updated successfully',
      data: { course: updatedCourse }
    });
  } catch (error: any) {
    console.error('Update course error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update course',
      error: error.message
    });
  }
};

// Delete course
export const deleteCourse = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user owns the course or is admin
    if (course.instructor.id.toString() !== req.user._id.toString() && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this course'
      });
    }

    await Course.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Course deleted successfully'
    });
  } catch (error: any) {
    console.error('Delete course error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete course',
      error: error.message
    });
  }
};

// Add course review
export const addCourseReview = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { rating, comment } = req.body;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user already reviewed this course
    const existingReview = course.reviews.find(
      review => review.userId.toString() === req.user!._id.toString()
    );

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this course'
      });
    }

    // Add new review
    const newReview = {
      userId: req.user._id,
      userName: req.user.name,
      userAvatar: req.user.avatar || '',
      rating: Number(rating),
      comment: String(comment),
      helpful: 0,
      createdAt: new Date()
    };

    course.reviews.push(newReview);

    // Update rating statistics
    const totalRatings = course.reviews.length;
    const ratingSum = course.reviews.reduce((sum, review) => sum + review.rating, 0);
    course.rating.average = ratingSum / totalRatings;
    course.rating.count = totalRatings;

    // Update rating distribution
    course.rating.distribution[rating as keyof typeof course.rating.distribution] += 1;

    await course.save();

    res.status(201).json({
      success: true,
      message: 'Review added successfully',
      data: { review: newReview }
    });
  } catch (error: any) {
    console.error('Add course review error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add review',
      error: error.message
    });
  }
};

// Enroll in course
export const enrollInCourse = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Here you would typically handle payment processing
    // For now, we'll just simulate enrollment

    // Update course enrollment stats
    course.enrollment.totalStudents += 1;
    course.enrollment.activeStudents += 1;
    await course.save();

    // Update user stats
    req.user.stats.coursesCompleted += 1;
    await req.user.save();

    res.json({
      success: true,
      message: 'Successfully enrolled in course',
      data: { courseId: id }
    });
  } catch (error: any) {
    console.error('Course enrollment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to enroll in course',
      error: error.message
    });
  }
};
