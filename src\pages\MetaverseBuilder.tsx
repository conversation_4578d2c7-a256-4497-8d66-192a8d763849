import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>ers,
  Globe,
  Users,
  Zap,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Pause,
  RotateCcw,
  Save,
  Share,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Sun,
  Moon,
  Cloud,
  Snowflake,
  Flame,
  Droplets,
  Wind,
  Mountain,
  Trees,
  Building,
  Car,
  Plane,
  Ship,
  Rocket,
  Atom,
  Infinity,
  Sparkles,
  Star,
  Heart,
  Crown,
  Shield,
  Sword,
  Wand2
} from 'lucide-react';
import { useApp } from '../context/AppContext';

interface WorldElement {
  id: string;
  type: 'terrain' | 'building' | 'vegetation' | 'water' | 'sky' | 'effect';
  name: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scale: { x: number; y: number; z: number };
  properties: any;
  visible: boolean;
}

interface WorldSettings {
  name: string;
  description: string;
  type: 'social' | 'gaming' | 'business' | 'educational' | 'artistic' | 'infinite';
  dimensions: { x: number; y: number; z: number; time?: number };
  physics: {
    gravity: number;
    friction: number;
    timeFlow: number;
    quantumMechanics: boolean;
  };
  environment: {
    lighting: string;
    weather: string;
    atmosphere: string;
    seasons: boolean;
    dayNightCycle: boolean;
  };
  economy: {
    currency: string;
    economicModel: string;
  };
  governance: {
    type: string;
    votingSystem: string;
  };
}

const MetaverseBuilder: React.FC = () => {
  const [worldSettings, setWorldSettings] = useState<WorldSettings>({
    name: 'New World',
    description: 'A magnificent virtual world',
    type: 'infinite',
    dimensions: { x: 10000, y: 1000, z: 10000, time: 1 },
    physics: {
      gravity: 9.81,
      friction: 0.5,
      timeFlow: 1,
      quantumMechanics: false
    },
    environment: {
      lighting: 'dynamic',
      weather: 'clear',
      atmosphere: 'earth-like',
      seasons: true,
      dayNightCycle: true
    },
    economy: {
      currency: 'NEURO',
      economicModel: 'mixed'
    },
    governance: {
      type: 'dao',
      votingSystem: 'quadratic'
    }
  });

  const [worldElements, setWorldElements] = useState<WorldElement[]>([]);
  const [selectedTool, setSelectedTool] = useState('terrain');
  const [selectedElement, setSelectedElement] = useState<WorldElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [showPhysics, setShowPhysics] = useState(false);
  const [cameraPosition, setCameraPosition] = useState({ x: 0, y: 100, z: 200 });
  const [timeOfDay, setTimeOfDay] = useState(12); // 0-24 hours
  const [weather, setWeather] = useState('clear');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { state, dispatch } = useApp();

  // 3D Visualization
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    const render = () => {
      // Clear canvas
      ctx.fillStyle = getBackgroundGradient();
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw grid if enabled
      if (showGrid) {
        drawGrid(ctx, canvas.width, canvas.height);
      }

      // Draw world elements
      worldElements.forEach(element => {
        if (element.visible) {
          drawElement(ctx, element, canvas.width, canvas.height);
        }
      });

      // Draw physics visualization if enabled
      if (showPhysics) {
        drawPhysicsVisualization(ctx, canvas.width, canvas.height);
      }

      requestAnimationFrame(render);
    };

    render();
  }, [worldElements, showGrid, showPhysics, timeOfDay, weather]);

  const getBackgroundGradient = () => {
    const hour = timeOfDay;
    if (hour >= 6 && hour < 18) {
      // Day
      return `linear-gradient(to bottom,
        hsl(${200 + hour * 2}, 70%, ${60 + hour}%),
        hsl(${180 + hour * 3}, 60%, ${80 - hour}%))`;
    } else {
      // Night
      return `linear-gradient(to bottom,
        hsl(${240}, 40%, ${10 + hour}%),
        hsl(${220}, 30%, ${5 + hour / 2}%))`;
    }
  };

  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    const gridSize = 50;
    for (let x = 0; x < width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    for (let y = 0; y < height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawElement = (ctx: CanvasRenderingContext2D, element: WorldElement, width: number, height: number) => {
    const { position, scale, type } = element;

    // Simple 2D representation of 3D elements
    const x = (position.x / 100) + width / 2;
    const y = height - (position.z / 100) - height / 4;
    const size = Math.max(scale.x, scale.z) * 20;

    ctx.save();
    ctx.translate(x, y);

    switch (type) {
      case 'terrain':
        ctx.fillStyle = '#4ade80';
        ctx.fillRect(-size/2, -size/2, size, size);
        break;
      case 'building':
        ctx.fillStyle = '#64748b';
        ctx.fillRect(-size/2, -size, size, size * 2);
        break;
      case 'vegetation':
        ctx.fillStyle = '#22c55e';
        ctx.beginPath();
        ctx.arc(0, 0, size/2, 0, Math.PI * 2);
        ctx.fill();
        break;
      case 'water':
        ctx.fillStyle = '#3b82f6';
        ctx.fillRect(-size/2, -size/2, size, size);
        break;
      case 'effect':
        ctx.fillStyle = '#a855f7';
        ctx.beginPath();
        ctx.arc(0, 0, size/3, 0, Math.PI * 2);
        ctx.fill();
        break;
    }

    ctx.restore();
  };

  const drawPhysicsVisualization = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Draw gravity arrows
    ctx.strokeStyle = '#ef4444';
    ctx.lineWidth = 2;

    for (let x = 100; x < width; x += 200) {
      ctx.beginPath();
      ctx.moveTo(x, 50);
      ctx.lineTo(x, 100);
      ctx.lineTo(x - 10, 90);
      ctx.moveTo(x, 100);
      ctx.lineTo(x + 10, 90);
      ctx.stroke();
    }
  };

  const tools = [
    { id: 'terrain', name: 'Terrain', icon: Mountain, color: 'green' },
    { id: 'building', name: 'Buildings', icon: Building, color: 'gray' },
    { id: 'vegetation', name: 'Plants', icon: Trees, color: 'emerald' },
    { id: 'water', name: 'Water', icon: Droplets, color: 'blue' },
    { id: 'sky', name: 'Sky', icon: Cloud, color: 'cyan' },
    { id: 'effect', name: 'Effects', icon: Sparkles, color: 'purple' }
  ];

  const weatherOptions = [
    { id: 'clear', name: 'Clear', icon: Sun },
    { id: 'cloudy', name: 'Cloudy', icon: Cloud },
    { id: 'rainy', name: 'Rainy', icon: Droplets },
    { id: 'snowy', name: 'Snowy', icon: Snowflake },
    { id: 'stormy', name: 'Stormy', icon: Zap },
    { id: 'windy', name: 'Windy', icon: Wind }
  ];

  const addElement = (type: string) => {
    const newElement: WorldElement = {
      id: `element_${Date.now()}`,
      type: type as any,
      name: `${type} ${worldElements.length + 1}`,
      position: {
        x: Math.random() * 1000 - 500,
        y: 0,
        z: Math.random() * 1000 - 500
      },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 },
      properties: {},
      visible: true
    };

    setWorldElements([...worldElements, newElement]);
    setSelectedElement(newElement);
  };

  const updateElement = (id: string, updates: Partial<WorldElement>) => {
    setWorldElements(elements =>
      elements.map(el => el.id === id ? { ...el, ...updates } : el)
    );

    if (selectedElement?.id === id) {
      setSelectedElement({ ...selectedElement, ...updates });
    }
  };

  const deleteElement = (id: string) => {
    setWorldElements(elements => elements.filter(el => el.id !== id));
    if (selectedElement?.id === id) {
      setSelectedElement(null);
    }
  };

  const saveWorld = async () => {
    try {
      // This would save the world to the backend
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: `World "${worldSettings.name}" saved successfully!`,
          type: 'success'
        }
      });
    } catch (error) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Failed to save world',
          type: 'error'
        }
      });
    }
  };

  const publishWorld = async () => {
    try {
      // This would publish the world to the metaverse
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: `World "${worldSettings.name}" published to the metaverse!`,
          type: 'success'
        }
      });
    } catch (error) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Failed to publish world',
          type: 'error'
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-black/50 backdrop-blur-sm border-b border-purple-500/30">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Layers className="h-8 w-8 text-purple-400" />
              <div>
                <h1 className="text-2xl font-bold text-white">Metaverse World Builder</h1>
                <p className="text-purple-300 text-sm">Create infinite virtual worlds</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsPlaying(!isPlaying)}
                className={`p-3 rounded-xl transition-all duration-300 ${
                  isPlaying
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </button>

              <button
                onClick={saveWorld}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-300"
              >
                <Save className="h-5 w-5 inline mr-2" />
                Save
              </button>

              <button
                onClick={publishWorld}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-3 rounded-xl transition-all duration-300"
              >
                <Share className="h-5 w-5 inline mr-2" />
                Publish
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-screen">
        {/* Left Sidebar - Tools */}
        <div className="w-80 bg-black/30 backdrop-blur-sm border-r border-purple-500/30 p-6 overflow-y-auto">
          <div className="space-y-6">
            {/* World Settings */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">World Settings</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">Name</label>
                  <input
                    type="text"
                    value={worldSettings.name}
                    onChange={(e) => setWorldSettings({...worldSettings, name: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">Type</label>
                  <select
                    value={worldSettings.type}
                    onChange={(e) => setWorldSettings({...worldSettings, type: e.target.value as any})}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                  >
                    <option value="social">Social</option>
                    <option value="gaming">Gaming</option>
                    <option value="business">Business</option>
                    <option value="educational">Educational</option>
                    <option value="artistic">Artistic</option>
                    <option value="infinite">Infinite</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Building Tools */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Building Tools</h3>

              <div className="grid grid-cols-2 gap-3">
                {tools.map((tool) => {
                  const Icon = tool.icon;
                  return (
                    <button
                      key={tool.id}
                      onClick={() => {
                        setSelectedTool(tool.id);
                        addElement(tool.id);
                      }}
                      className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                        selectedTool === tool.id
                          ? `border-${tool.color}-400 bg-${tool.color}-500/20`
                          : 'border-white/20 bg-white/5 hover:bg-white/10'
                      }`}
                    >
                      <Icon className={`h-6 w-6 mx-auto mb-2 ${
                        selectedTool === tool.id ? `text-${tool.color}-400` : 'text-white'
                      }`} />
                      <div className={`text-sm ${
                        selectedTool === tool.id ? `text-${tool.color}-400` : 'text-white'
                      }`}>
                        {tool.name}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Environment Controls */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Environment</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">
                    Time of Day: {timeOfDay}:00
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="24"
                    value={timeOfDay}
                    onChange={(e) => setTimeOfDay(Number(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">Weather</label>
                  <div className="grid grid-cols-3 gap-2">
                    {weatherOptions.map((option) => {
                      const Icon = option.icon;
                      return (
                        <button
                          key={option.id}
                          onClick={() => setWeather(option.id)}
                          className={`p-3 rounded-lg border transition-all duration-300 ${
                            weather === option.id
                              ? 'border-blue-400 bg-blue-500/20 text-blue-400'
                              : 'border-white/20 bg-white/5 text-white hover:bg-white/10'
                          }`}
                        >
                          <Icon className="h-5 w-5 mx-auto" />
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">
                    Gravity: {worldSettings.physics.gravity}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="20"
                    step="0.1"
                    value={worldSettings.physics.gravity}
                    onChange={(e) => setWorldSettings({
                      ...worldSettings,
                      physics: { ...worldSettings.physics, gravity: Number(e.target.value) }
                    })}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* View Controls */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">View Controls</h3>

              <div className="space-y-3">
                <button
                  onClick={() => setShowGrid(!showGrid)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-all duration-300 ${
                    showGrid ? 'bg-green-500/20 text-green-400' : 'bg-white/5 text-white'
                  }`}
                >
                  <span>Show Grid</span>
                  {showGrid ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />}
                </button>

                <button
                  onClick={() => setShowPhysics(!showPhysics)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-all duration-300 ${
                    showPhysics ? 'bg-blue-500/20 text-blue-400' : 'bg-white/5 text-white'
                  }`}
                >
                  <span>Show Physics</span>
                  <Atom className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 relative">
          <canvas
            ref={canvasRef}
            className="w-full h-full cursor-crosshair"
            style={{ background: getBackgroundGradient() }}
          />

          {/* Canvas Overlay Controls */}
          <div className="absolute top-4 right-4 space-y-2">
            <div className="bg-black/50 backdrop-blur-sm border border-white/20 rounded-lg p-3">
              <div className="text-white text-sm">
                Elements: {worldElements.length}
              </div>
              <div className="text-purple-300 text-xs">
                {worldSettings.name}
              </div>
            </div>
          </div>

          {/* Bottom Status Bar */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-black/50 backdrop-blur-sm border border-white/20 rounded-lg p-4">
              <div className="flex items-center justify-between text-sm text-white">
                <div className="flex items-center space-x-6">
                  <div>Camera: X:{cameraPosition.x} Y:{cameraPosition.y} Z:{cameraPosition.z}</div>
                  <div>Time: {timeOfDay}:00</div>
                  <div>Weather: {weather}</div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className={`flex items-center space-x-2 ${isPlaying ? 'text-green-400' : 'text-gray-400'}`}>
                    <div className="w-2 h-2 rounded-full bg-current animate-pulse"></div>
                    <span>{isPlaying ? 'Simulation Running' : 'Simulation Paused'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Properties */}
        {selectedElement && (
          <div className="w-80 bg-black/30 backdrop-blur-sm border-l border-purple-500/30 p-6 overflow-y-auto">
            <div className="space-y-6">
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Element Properties</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-purple-300 mb-2">Name</label>
                    <input
                      type="text"
                      value={selectedElement.name}
                      onChange={(e) => updateElement(selectedElement.id, { name: e.target.value })}
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-purple-300 mb-2">Position</label>
                    <div className="grid grid-cols-3 gap-2">
                      <input
                        type="number"
                        placeholder="X"
                        value={selectedElement.position.x}
                        onChange={(e) => updateElement(selectedElement.id, {
                          position: { ...selectedElement.position, x: Number(e.target.value) }
                        })}
                        className="bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white text-sm"
                      />
                      <input
                        type="number"
                        placeholder="Y"
                        value={selectedElement.position.y}
                        onChange={(e) => updateElement(selectedElement.id, {
                          position: { ...selectedElement.position, y: Number(e.target.value) }
                        })}
                        className="bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white text-sm"
                      />
                      <input
                        type="number"
                        placeholder="Z"
                        value={selectedElement.position.z}
                        onChange={(e) => updateElement(selectedElement.id, {
                          position: { ...selectedElement.position, z: Number(e.target.value) }
                        })}
                        className="bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-purple-300 mb-2">Scale</label>
                    <div className="grid grid-cols-3 gap-2">
                      <input
                        type="number"
                        placeholder="X"
                        step="0.1"
                        value={selectedElement.scale.x}
                        onChange={(e) => updateElement(selectedElement.id, {
                          scale: { ...selectedElement.scale, x: Number(e.target.value) }
                        })}
                        className="bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white text-sm"
                      />
                      <input
                        type="number"
                        placeholder="Y"
                        step="0.1"
                        value={selectedElement.scale.y}
                        onChange={(e) => updateElement(selectedElement.id, {
                          scale: { ...selectedElement.scale, y: Number(e.target.value) }
                        })}
                        className="bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white text-sm"
                      />
                      <input
                        type="number"
                        placeholder="Z"
                        step="0.1"
                        value={selectedElement.scale.z}
                        onChange={(e) => updateElement(selectedElement.id, {
                          scale: { ...selectedElement.scale, z: Number(e.target.value) }
                        })}
                        className="bg-white/10 border border-white/20 rounded-lg px-2 py-1 text-white text-sm"
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-purple-300">Visible</label>
                    <button
                      onClick={() => updateElement(selectedElement.id, { visible: !selectedElement.visible })}
                      className={`p-2 rounded-lg transition-all duration-300 ${
                        selectedElement.visible
                          ? 'bg-green-500/20 text-green-400'
                          : 'bg-red-500/20 text-red-400'
                      }`}
                    >
                      {selectedElement.visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </button>
                  </div>

                  <button
                    onClick={() => deleteElement(selectedElement.id)}
                    className="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg transition-all duration-300"
                  >
                    Delete Element
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MetaverseBuilder;