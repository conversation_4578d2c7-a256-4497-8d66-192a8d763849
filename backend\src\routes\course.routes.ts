import { Router } from 'express';
import {
  getCourses,
  getCourseById,
  createCourse,
  updateCourse,
  deleteCourse,
  addCourseReview,
  enrollInCourse
} from '../controllers/course.controller';
import { authenticateToken, requireCreator } from '../middleware/auth.middleware';
import { validate, courseValidation } from '../middleware/validation.middleware';

const router = Router();

// Public routes
router.get('/', getCourses);
router.get('/:id', getCourseById);

// Protected routes
router.post('/', authenticateToken, requireCreator, validate(courseValidation.create), createCourse);
router.put('/:id', authenticateToken, updateCourse);
router.delete('/:id', authenticateToken, deleteCourse);

// Enrollment and review routes
router.post('/:id/enroll', authenticateToken, enrollInCourse);
router.post('/:id/reviews', authenticateToken, addCourseReview);

export default router;
