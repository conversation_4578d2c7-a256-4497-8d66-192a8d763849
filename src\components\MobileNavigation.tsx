import React, { useState } from 'react';
import { 
  Home, 
  ShoppingBag, 
  BookOpen, 
  <PERSON>lette, 
  Brain, 
  Gamepad2, 
  Coins, 
  Zap,
  Menu,
  X,
  User,
  <PERSON>ting<PERSON>,
  LogOut
} from 'lucide-react';
import { useApp } from '../context/AppContext';

interface MobileNavigationProps {
  currentPage: string;
  onPageChange: (page: string) => void;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ currentPage, onPageChange }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { state, dispatch } = useApp();

  const mainNavItems = [
    { id: 'home', label: 'Home', icon: Home },
    { id: 'social-commerce', label: 'Shop', icon: ShoppingBag },
    { id: 'learning-platform', label: 'Learn', icon: BookOpen },
    { id: 'ai-creativity-studio', label: 'Create', icon: Palette },
    { id: 'quantum-ai', label: 'AI', icon: Brain }
  ];

  const secondaryNavItems = [
    { id: 'metaverse-hub', label: 'Metaverse', icon: Gamepad2 },
    { id: 'blockchain-ecosystem', label: 'Blockchain', icon: Coins },
    { id: 'neural-interface', label: 'Neural', icon: Zap },
    { id: 'fintech-services', label: 'Finance', icon: Coins },
    { id: 'health-fitness', label: 'Health', icon: Zap }
  ];

  const handleNavigation = (pageId: string) => {
    onPageChange(pageId);
    setIsMenuOpen(false);
  };

  const handleLogout = () => {
    dispatch({ type: 'LOGOUT' });
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-t border-white/10 z-40">
        <div className="flex items-center justify-around py-2">
          {mainNavItems.slice(0, 4).map((item) => {
            const Icon = item.icon;
            const isActive = currentPage === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.id)}
                className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                  isActive 
                    ? 'text-blue-400 bg-blue-400/10' 
                    : 'text-white/60 hover:text-white'
                }`}
              >
                <Icon className="h-5 w-5 mb-1" />
                <span className="text-xs">{item.label}</span>
              </button>
            );
          })}
          
          {/* Menu Button */}
          <button
            onClick={() => setIsMenuOpen(true)}
            className="flex flex-col items-center py-2 px-3 rounded-lg text-white/60 hover:text-white transition-colors"
          >
            <Menu className="h-5 w-5 mb-1" />
            <span className="text-xs">More</span>
          </button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
          <div className="absolute inset-y-0 right-0 w-80 bg-gray-900/95 backdrop-blur-sm border-l border-white/10">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div className="flex items-center space-x-3">
                {state.user?.avatar ? (
                  <img 
                    src={state.user.avatar} 
                    alt={state.user.name}
                    className="w-10 h-10 rounded-full"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-white" />
                  </div>
                )}
                <div>
                  <h3 className="text-white font-medium">{state.user?.name || 'Guest'}</h3>
                  <p className="text-white/60 text-sm">Level {state.user?.level || 1}</p>
                </div>
              </div>
              <button
                onClick={() => setIsMenuOpen(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Navigation Items */}
            <div className="p-6 space-y-6">
              {/* Main Navigation */}
              <div>
                <h4 className="text-white/80 text-sm font-medium mb-3">Main</h4>
                <div className="space-y-2">
                  {mainNavItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = currentPage === item.id;
                    
                    return (
                      <button
                        key={item.id}
                        onClick={() => handleNavigation(item.id)}
                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                          isActive 
                            ? 'text-blue-400 bg-blue-400/10' 
                            : 'text-white/70 hover:text-white hover:bg-white/5'
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                        <span>{item.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Secondary Navigation */}
              <div>
                <h4 className="text-white/80 text-sm font-medium mb-3">Explore</h4>
                <div className="space-y-2">
                  {secondaryNavItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = currentPage === item.id;
                    
                    return (
                      <button
                        key={item.id}
                        onClick={() => handleNavigation(item.id)}
                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                          isActive 
                            ? 'text-blue-400 bg-blue-400/10' 
                            : 'text-white/70 hover:text-white hover:bg-white/5'
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                        <span>{item.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* User Actions */}
              {state.isAuthenticated && (
                <div>
                  <h4 className="text-white/80 text-sm font-medium mb-3">Account</h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => handleNavigation('profile')}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/5 transition-colors"
                    >
                      <User className="h-5 w-5" />
                      <span>Profile</span>
                    </button>
                    <button
                      onClick={() => handleNavigation('settings')}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-white/70 hover:text-white hover:bg-white/5 transition-colors"
                    >
                      <Settings className="h-5 w-5" />
                      <span>Settings</span>
                    </button>
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-red-400 hover:text-red-300 hover:bg-red-400/10 transition-colors"
                    >
                      <LogOut className="h-5 w-5" />
                      <span>Logout</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-white/10">
              <div className="text-center text-white/60 text-sm">
                <p>NeuroSphere v1.0</p>
                <p className="mt-1">The Future is Now</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Header */}
      <div className="md:hidden fixed top-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-b border-white/10 z-30">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-lg flex items-center justify-center">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-white font-bold text-lg">NeuroSphere</h1>
          </div>
          
          <div className="flex items-center space-x-3">
            {state.isAuthenticated && (
              <>
                <div className="text-right">
                  <p className="text-white text-sm font-medium">{state.user?.points || 0}</p>
                  <p className="text-white/60 text-xs">Points</p>
                </div>
                <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">{state.user?.level || 1}</span>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Spacer for fixed header */}
      <div className="md:hidden h-16"></div>
    </>
  );
};

export default MobileNavigation;
