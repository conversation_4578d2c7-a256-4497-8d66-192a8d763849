import React, { useState } from 'react';
import { 
  GraduationCap, 
  BookOpen, 
  Play, 
  Clock, 
  Star, 
  Users, 
  Award, 
  TrendingUp,
  Search,
  Filter,
  CheckCircle,
  Lock,
  Zap
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const LearningPlatform: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const { state, dispatch } = useApp();

  const categories = [
    { id: 'all', name: 'All Courses', icon: BookOpen },
    { id: 'ai', name: 'AI & Machine Learning', icon: Zap },
    { id: 'business', name: 'Business', icon: TrendingUp },
    { id: 'design', name: 'Design', icon: Star },
    { id: 'tech', name: 'Technology', icon: GraduationCap }
  ];

  const courses = [
    {
      id: '1',
      title: 'Complete AI & Machine Learning Bootcamp',
      instructor: 'Dr. <PERSON>',
      instructorAvatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 299,
      originalPrice: 499,
      rating: 4.9,
      students: 15420,
      duration: '42 hours',
      lessons: 156,
      level: 'Beginner to Advanced',
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'ai',
      progress: 0,
      isEnrolled: false,
      skills: ['Python', 'TensorFlow', 'Neural Networks', 'Deep Learning'],
      description: 'Master AI and ML from scratch with hands-on projects and real-world applications.'
    },
    {
      id: '2',
      title: 'Digital Marketing Mastery',
      instructor: 'Mark Johnson',
      instructorAvatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 199,
      originalPrice: 299,
      rating: 4.8,
      students: 8934,
      duration: '28 hours',
      lessons: 89,
      level: 'Intermediate',
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'business',
      progress: 65,
      isEnrolled: true,
      skills: ['SEO', 'Social Media', 'Analytics', 'Content Marketing'],
      description: 'Complete guide to digital marketing strategies and tools for modern businesses.'
    },
    {
      id: '3',
      title: 'UI/UX Design Fundamentals',
      instructor: 'Emma Wilson',
      instructorAvatar: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 149,
      originalPrice: 249,
      rating: 4.7,
      students: 12567,
      duration: '35 hours',
      lessons: 124,
      level: 'Beginner',
      thumbnail: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'design',
      progress: 0,
      isEnrolled: false,
      skills: ['Figma', 'Prototyping', 'User Research', 'Design Systems'],
      description: 'Learn to create beautiful and functional user interfaces and experiences.'
    },
    {
      id: '4',
      title: 'Full Stack Web Development',
      instructor: 'Alex Rodriguez',
      instructorAvatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 399,
      originalPrice: 599,
      rating: 4.9,
      students: 23456,
      duration: '68 hours',
      lessons: 234,
      level: 'Beginner to Advanced',
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'tech',
      progress: 25,
      isEnrolled: true,
      skills: ['React', 'Node.js', 'MongoDB', 'TypeScript'],
      description: 'Become a full-stack developer with modern technologies and best practices.'
    }
  ];

  const myLearning = courses.filter(course => course.isEnrolled);
  const achievements = [
    { title: 'First Course Completed', icon: Award, earned: true },
    { title: 'Week Streak', icon: Zap, earned: true },
    { title: 'Skill Master', icon: Star, earned: false },
    { title: 'Top Learner', icon: TrendingUp, earned: false }
  ];

  const enrollInCourse = (course: any) => {
    dispatch({ type: 'ENROLL_COURSE', payload: course });
    dispatch({ 
      type: 'ADD_NOTIFICATION', 
      payload: { 
        id: Date.now(), 
        message: `Successfully enrolled in ${course.title}!`, 
        type: 'success' 
      } 
    });
  };

  const filteredCourses = courses.filter(course => 
    (activeCategory === 'all' || course.category === activeCategory) &&
    (searchQuery === '' || course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     course.instructor.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Learning <span className="bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text text-transparent">Platform</span>
          </h1>
          <p className="text-white/70 text-lg">Expand your skills with AI-powered personalized learning</p>
        </div>

        {/* Learning Stats */}
        <div className="bg-gradient-to-r from-orange-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 mb-8">
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">{myLearning.length}</div>
              <div className="text-white/60">Enrolled Courses</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400 mb-2">
                {Math.round(myLearning.reduce((sum, course) => sum + course.progress, 0) / myLearning.length) || 0}%
              </div>
              <div className="text-white/60">Average Progress</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-400 mb-2">47</div>
              <div className="text-white/60">Hours Learned</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">12</div>
              <div className="text-white/60">Certificates</div>
            </div>
          </div>
        </div>

        {/* My Learning Section */}
        {myLearning.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-white mb-6">Continue Learning</h2>
            <div className="grid md:grid-cols-2 gap-6">
              {myLearning.map((course) => (
                <div key={course.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
                  <div className="flex">
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="w-32 h-32 object-cover"
                    />
                    <div className="flex-1 p-6">
                      <h3 className="text-lg font-semibold text-white mb-2">{course.title}</h3>
                      <p className="text-white/70 text-sm mb-3">by {course.instructor}</p>
                      <div className="mb-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-white/70">Progress</span>
                          <span className="text-white/70">{course.progress}%</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-orange-500 to-pink-500 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${course.progress}%` }}
                          ></div>
                        </div>
                      </div>
                      <button className="bg-gradient-to-r from-orange-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-orange-700 hover:to-pink-700 transition-colors flex items-center space-x-2">
                        <Play className="h-4 w-4" />
                        <span>Continue</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
            <input
              type="text"
              placeholder="Search courses, instructors, skills..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
          <button className="flex items-center space-x-2 bg-white/10 border border-white/20 rounded-xl px-6 py-3 text-white hover:bg-white/20 transition-colors backdrop-blur-sm">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Categories */}
        <div className="flex space-x-4 mb-8 overflow-x-auto pb-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeCategory === category.id
                  ? 'bg-gradient-to-r from-orange-600 to-pink-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <category.icon className="h-5 w-5" />
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Courses Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredCourses.map((course) => (
            <div key={course.id} className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105">
              <div className="relative">
                <img
                  src={course.thumbnail}
                  alt={course.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                  {course.level}
                </div>
                <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>{course.duration}</span>
                </div>
                {course.isEnrolled && (
                  <div className="absolute bottom-3 left-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                    <CheckCircle className="h-3 w-3" />
                    <span>Enrolled</span>
                  </div>
                )}
              </div>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-2">{course.title}</h3>
                <div className="flex items-center space-x-2 mb-3">
                  <img
                    src={course.instructorAvatar}
                    alt={course.instructor}
                    className="w-6 h-6 rounded-full"
                  />
                  <span className="text-white/70 text-sm">{course.instructor}</span>
                </div>
                <p className="text-white/60 text-sm mb-4">{course.description}</p>
                <div className="flex items-center space-x-4 mb-4 text-sm text-white/70">
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{course.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{course.students.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{course.lessons} lessons</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mb-4">
                  {course.skills.slice(0, 3).map((skill, index) => (
                    <span key={index} className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs">
                      {skill}
                    </span>
                  ))}
                  {course.skills.length > 3 && (
                    <span className="text-white/60 text-xs">+{course.skills.length - 3} more</span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl font-bold text-white">${course.price}</span>
                    <span className="text-white/60 line-through text-sm">${course.originalPrice}</span>
                  </div>
                  {course.isEnrolled ? (
                    <button className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors flex items-center space-x-2">
                      <Play className="h-4 w-4" />
                      <span>Continue</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => enrollInCourse(course)}
                      className="bg-gradient-to-r from-orange-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-orange-700 hover:to-pink-700 transition-colors"
                    >
                      Enroll Now
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Achievements */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Your Achievements</h2>
          <div className="grid md:grid-cols-4 gap-4">
            {achievements.map((achievement, index) => (
              <div key={index} className={`bg-gradient-to-br backdrop-blur-sm border rounded-2xl p-6 text-center transition-all duration-300 ${
                achievement.earned 
                  ? 'from-yellow-600/20 to-orange-600/20 border-yellow-500/30' 
                  : 'from-white/5 to-white/5 border-white/10'
              }`}>
                <div className={`inline-flex p-4 rounded-2xl mb-4 ${
                  achievement.earned 
                    ? 'bg-gradient-to-r from-yellow-500 to-orange-500' 
                    : 'bg-white/10'
                }`}>
                  <achievement.icon className={`h-8 w-8 ${
                    achievement.earned ? 'text-white' : 'text-white/40'
                  }`} />
                </div>
                <h3 className={`text-lg font-semibold mb-2 ${
                  achievement.earned ? 'text-white' : 'text-white/60'
                }`}>
                  {achievement.title}
                </h3>
                {achievement.earned ? (
                  <span className="text-yellow-400 text-sm">Earned!</span>
                ) : (
                  <span className="text-white/40 text-sm">Not earned yet</span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LearningPlatform;