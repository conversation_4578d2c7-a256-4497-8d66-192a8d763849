import React, { useState, useEffect } from 'react';
import {
  GraduationCap,
  BookOpen,
  Play,
  Clock,
  Star,
  Users,
  Award,
  TrendingUp,
  Search,
  Filter,
  CheckCircle,
  Lock,
  Zap,
  X,
  Download,
  FileText,
  Video,
  Quiz,
  Certificate,
  Heart,
  Share2,
  MessageCircle,
  ChevronRight,
  PlayCircle,
  Pause
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import api from '../services/api';

interface Course {
  id: string;
  title: string;
  instructor: string;
  instructorAvatar: string;
  instructorBio?: string;
  price: number;
  originalPrice: number;
  rating: number;
  students: number;
  duration: string;
  lessons: number;
  level: string;
  thumbnail: string;
  category: string;
  progress: number;
  isEnrolled: boolean;
  skills: string[];
  description: string;
  fullDescription?: string;
  whatYouWillLearn?: string[];
  requirements?: string[];
  curriculum?: Array<{
    id: string;
    title: string;
    lessons: Array<{
      id: string;
      title: string;
      duration: string;
      type: 'video' | 'quiz' | 'assignment' | 'reading';
      isCompleted: boolean;
      isLocked: boolean;
    }>;
  }>;
}

const LearningPlatform: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [showCourseModal, setShowCourseModal] = useState(false);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [currentLesson, setCurrentLesson] = useState<any>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [enrolledCourses, setEnrolledCourses] = useState<string[]>([]);
  const { state, dispatch } = useApp();

  // Load courses from API
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const response = await api.getCourses({
          category: activeCategory === 'all' ? undefined : activeCategory,
          search: searchQuery,
          limit: 20
        });
        if (response.success) {
          setCourses(response.data.courses || []);
        }
      } catch (error) {
        console.error('Failed to fetch courses:', error);
        // Fallback to mock data
        setCourses(mockCourses);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [activeCategory, searchQuery]);

  const categories = [
    { id: 'all', name: 'All Courses', icon: BookOpen },
    { id: 'ai', name: 'AI & Machine Learning', icon: Zap },
    { id: 'business', name: 'Business', icon: TrendingUp },
    { id: 'design', name: 'Design', icon: Star },
    { id: 'tech', name: 'Technology', icon: GraduationCap }
  ];

  // Mock courses data (fallback)
  const mockCourses: Course[] = [
    {
      id: '1',
      title: 'Complete AI & Machine Learning Bootcamp',
      instructor: 'Dr. Sarah Chen',
      instructorAvatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      instructorBio: 'PhD in Computer Science, 10+ years in AI research',
      price: 299,
      originalPrice: 499,
      rating: 4.9,
      students: 15420,
      duration: '42 hours',
      lessons: 156,
      level: 'Beginner to Advanced',
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'ai',
      progress: 0,
      isEnrolled: false,
      skills: ['Python', 'TensorFlow', 'Neural Networks', 'Deep Learning'],
      description: 'Master AI and ML from scratch with hands-on projects and real-world applications.',
      fullDescription: 'This comprehensive course covers everything from basic machine learning concepts to advanced deep learning techniques. You\'ll work on real-world projects and build a portfolio of AI applications.',
      whatYouWillLearn: [
        'Build neural networks from scratch',
        'Implement machine learning algorithms',
        'Work with TensorFlow and PyTorch',
        'Deploy AI models to production',
        'Understand deep learning architectures'
      ],
      requirements: [
        'Basic Python programming knowledge',
        'High school level mathematics',
        'Computer with internet connection'
      ],
      curriculum: [
        {
          id: 'section-1',
          title: 'Introduction to AI & ML',
          lessons: [
            { id: 'lesson-1-1', title: 'What is Artificial Intelligence?', duration: '15:30', type: 'video', isCompleted: false, isLocked: false },
            { id: 'lesson-1-2', title: 'Machine Learning Fundamentals', duration: '22:45', type: 'video', isCompleted: false, isLocked: false },
            { id: 'lesson-1-3', title: 'Quiz: AI Basics', duration: '10:00', type: 'quiz', isCompleted: false, isLocked: true }
          ]
        },
        {
          id: 'section-2',
          title: 'Python for Machine Learning',
          lessons: [
            { id: 'lesson-2-1', title: 'NumPy and Pandas', duration: '35:20', type: 'video', isCompleted: false, isLocked: true },
            { id: 'lesson-2-2', title: 'Data Visualization', duration: '28:15', type: 'video', isCompleted: false, isLocked: true },
            { id: 'lesson-2-3', title: 'Assignment: Data Analysis', duration: '60:00', type: 'assignment', isCompleted: false, isLocked: true }
          ]
        }
      ]
    },
    {
      id: '2',
      title: 'Digital Marketing Mastery',
      instructor: 'Mark Johnson',
      instructorAvatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 199,
      originalPrice: 299,
      rating: 4.8,
      students: 8934,
      duration: '28 hours',
      lessons: 89,
      level: 'Intermediate',
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'business',
      progress: 65,
      isEnrolled: true,
      skills: ['SEO', 'Social Media', 'Analytics', 'Content Marketing'],
      description: 'Complete guide to digital marketing strategies and tools for modern businesses.'
    },
    {
      id: '3',
      title: 'UI/UX Design Fundamentals',
      instructor: 'Emma Wilson',
      instructorAvatar: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 149,
      originalPrice: 249,
      rating: 4.7,
      students: 12567,
      duration: '35 hours',
      lessons: 124,
      level: 'Beginner',
      thumbnail: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'design',
      progress: 0,
      isEnrolled: false,
      skills: ['Figma', 'Prototyping', 'User Research', 'Design Systems'],
      description: 'Learn to create beautiful and functional user interfaces and experiences.'
    },
    {
      id: '4',
      title: 'Full Stack Web Development',
      instructor: 'Alex Rodriguez',
      instructorAvatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: 399,
      originalPrice: 599,
      rating: 4.9,
      students: 23456,
      duration: '68 hours',
      lessons: 234,
      level: 'Beginner to Advanced',
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'tech',
      progress: 25,
      isEnrolled: true,
      skills: ['React', 'Node.js', 'MongoDB', 'TypeScript'],
      description: 'Become a full-stack developer with modern technologies and best practices.'
    }
  ];

  const myLearning = courses.filter(course => course.isEnrolled);
  const achievements = [
    { title: 'First Course Completed', icon: Award, earned: true },
    { title: 'Week Streak', icon: Zap, earned: true },
    { title: 'Skill Master', icon: Star, earned: false },
    { title: 'Top Learner', icon: TrendingUp, earned: false }
  ];

  const enrollInCourse = async (course: Course) => {
    try {
      const response = await api.enrollInCourse(course.id);
      if (response.success) {
        setEnrolledCourses(prev => [...prev, course.id]);
        dispatch({ type: 'ENROLL_COURSE', payload: course });
        dispatch({
          type: 'ADD_NOTIFICATION',
          payload: {
            id: Date.now(),
            message: `Successfully enrolled in ${course.title}!`,
            type: 'success'
          }
        });
      }
    } catch (error) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Failed to enroll in course. Please try again.',
          type: 'error'
        }
      });
    }
  };

  const openCourseDetails = (course: Course) => {
    setSelectedCourse(course);
    setShowCourseModal(true);
  };

  const startLesson = (lesson: any) => {
    setCurrentLesson(lesson);
    setShowVideoPlayer(true);
  };

  // Course Detail Modal Component
  const CourseDetailModal = () => {
    if (!selectedCourse) return null;

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="relative">
            <img
              src={selectedCourse.thumbnail}
              alt={selectedCourse.title}
              className="w-full h-64 object-cover"
            />
            <button
              onClick={() => setShowCourseModal(false)}
              className="absolute top-4 right-4 bg-black/60 text-white p-2 rounded-full hover:bg-black/80 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
            <div className="absolute bottom-4 left-4 right-4">
              <h1 className="text-3xl font-bold text-white mb-2">{selectedCourse.title}</h1>
              <div className="flex items-center space-x-4 text-white/80">
                <div className="flex items-center space-x-1">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span>{selectedCourse.rating}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-5 w-5" />
                  <span>{selectedCourse.students.toLocaleString()} students</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-5 w-5" />
                  <span>{selectedCourse.duration}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-8">
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-white mb-4">About this course</h2>
                  <p className="text-white/70 leading-relaxed">
                    {selectedCourse.fullDescription || selectedCourse.description}
                  </p>
                </div>

                {selectedCourse.whatYouWillLearn && (
                  <div className="mb-8">
                    <h3 className="text-xl font-bold text-white mb-4">What you'll learn</h3>
                    <div className="grid md:grid-cols-2 gap-3">
                      {selectedCourse.whatYouWillLearn.map((item, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                          <span className="text-white/80">{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedCourse.curriculum && (
                  <div className="mb-8">
                    <h3 className="text-xl font-bold text-white mb-4">Course curriculum</h3>
                    <div className="space-y-4">
                      {selectedCourse.curriculum.map((section) => (
                        <div key={section.id} className="bg-white/5 rounded-xl p-4">
                          <h4 className="text-lg font-semibold text-white mb-3">{section.title}</h4>
                          <div className="space-y-2">
                            {section.lessons.map((lesson) => (
                              <div key={lesson.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                                <div className="flex items-center space-x-3">
                                  {lesson.type === 'video' && <Video className="h-4 w-4 text-blue-400" />}
                                  {lesson.type === 'quiz' && <Quiz className="h-4 w-4 text-purple-400" />}
                                  {lesson.type === 'assignment' && <FileText className="h-4 w-4 text-orange-400" />}
                                  {lesson.type === 'reading' && <BookOpen className="h-4 w-4 text-green-400" />}
                                  <span className={`${lesson.isLocked ? 'text-white/40' : 'text-white/80'}`}>
                                    {lesson.title}
                                  </span>
                                  {lesson.isCompleted && <CheckCircle className="h-4 w-4 text-green-400" />}
                                  {lesson.isLocked && <Lock className="h-4 w-4 text-white/40" />}
                                </div>
                                <div className="flex items-center space-x-3">
                                  <span className="text-white/60 text-sm">{lesson.duration}</span>
                                  {!lesson.isLocked && (
                                    <button
                                      onClick={() => startLesson(lesson)}
                                      className="text-blue-400 hover:text-blue-300 transition-colors"
                                    >
                                      <PlayCircle className="h-5 w-5" />
                                    </button>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="lg:col-span-1">
                <div className="bg-white/5 rounded-xl p-6 sticky top-4">
                  <div className="text-center mb-6">
                    <div className="text-3xl font-bold text-white mb-2">${selectedCourse.price}</div>
                    <div className="text-white/60 line-through">${selectedCourse.originalPrice}</div>
                    <div className="text-green-400 text-sm font-medium">
                      {Math.round((1 - selectedCourse.price / selectedCourse.originalPrice) * 100)}% OFF
                    </div>
                  </div>

                  {selectedCourse.isEnrolled ? (
                    <button className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 rounded-xl font-medium hover:from-green-700 hover:to-green-800 transition-colors flex items-center justify-center space-x-2">
                      <Play className="h-5 w-5" />
                      <span>Continue Learning</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => enrollInCourse(selectedCourse)}
                      className="w-full bg-gradient-to-r from-orange-600 to-pink-600 text-white py-3 rounded-xl font-medium hover:from-orange-700 hover:to-pink-700 transition-colors"
                    >
                      Enroll Now
                    </button>
                  )}

                  <div className="mt-6 space-y-4">
                    <div className="flex items-center justify-between text-white/80">
                      <span>Instructor</span>
                      <div className="flex items-center space-x-2">
                        <img
                          src={selectedCourse.instructorAvatar}
                          alt={selectedCourse.instructor}
                          className="w-6 h-6 rounded-full"
                        />
                        <span>{selectedCourse.instructor}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-white/80">
                      <span>Duration</span>
                      <span>{selectedCourse.duration}</span>
                    </div>
                    <div className="flex items-center justify-between text-white/80">
                      <span>Lessons</span>
                      <span>{selectedCourse.lessons}</span>
                    </div>
                    <div className="flex items-center justify-between text-white/80">
                      <span>Level</span>
                      <span>{selectedCourse.level}</span>
                    </div>
                    <div className="flex items-center justify-between text-white/80">
                      <span>Certificate</span>
                      <span className="text-green-400">Yes</span>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-white/10">
                    <h4 className="text-white font-medium mb-3">Skills you'll gain</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedCourse.skills.map((skill, index) => (
                        <span key={index} className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Video Player Modal Component
  const VideoPlayerModal = () => {
    if (!currentLesson) return null;

    return (
      <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
        <div className="w-full h-full max-w-6xl mx-auto flex flex-col">
          <div className="flex items-center justify-between p-4 bg-black/80">
            <h2 className="text-white text-xl font-semibold">{currentLesson.title}</h2>
            <button
              onClick={() => setShowVideoPlayer(false)}
              className="text-white hover:text-gray-300 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="flex-1 bg-black flex items-center justify-center">
            <div className="text-white text-center">
              <PlayCircle className="h-24 w-24 mx-auto mb-4 opacity-50" />
              <p className="text-xl">Video Player</p>
              <p className="text-gray-400 mt-2">Duration: {currentLesson.duration}</p>
            </div>
          </div>
          <div className="p-4 bg-black/80 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="text-white hover:text-gray-300 transition-colors">
                <Pause className="h-6 w-6" />
              </button>
              <span className="text-white">0:00 / {currentLesson.duration}</span>
            </div>
            <div className="flex items-center space-x-4">
              <button className="text-white hover:text-gray-300 transition-colors">
                <Download className="h-5 w-5" />
              </button>
              <button className="text-white hover:text-gray-300 transition-colors">
                <Share2 className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const filteredCourses = courses.filter(course => 
    (activeCategory === 'all' || course.category === activeCategory) &&
    (searchQuery === '' || course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     course.instructor.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Learning <span className="bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text text-transparent">Platform</span>
          </h1>
          <p className="text-white/70 text-lg">Expand your skills with AI-powered personalized learning</p>
        </div>

        {/* Learning Stats */}
        <div className="bg-gradient-to-r from-orange-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 mb-8">
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">{myLearning.length}</div>
              <div className="text-white/60">Enrolled Courses</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400 mb-2">
                {Math.round(myLearning.reduce((sum, course) => sum + course.progress, 0) / myLearning.length) || 0}%
              </div>
              <div className="text-white/60">Average Progress</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-400 mb-2">47</div>
              <div className="text-white/60">Hours Learned</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">12</div>
              <div className="text-white/60">Certificates</div>
            </div>
          </div>
        </div>

        {/* My Learning Section */}
        {myLearning.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-white mb-6">Continue Learning</h2>
            <div className="grid md:grid-cols-2 gap-6">
              {myLearning.map((course) => (
                <div key={course.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
                  <div className="flex">
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="w-32 h-32 object-cover"
                    />
                    <div className="flex-1 p-6">
                      <h3 className="text-lg font-semibold text-white mb-2">{course.title}</h3>
                      <p className="text-white/70 text-sm mb-3">by {course.instructor}</p>
                      <div className="mb-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-white/70">Progress</span>
                          <span className="text-white/70">{course.progress}%</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-orange-500 to-pink-500 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${course.progress}%` }}
                          ></div>
                        </div>
                      </div>
                      <button className="bg-gradient-to-r from-orange-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-orange-700 hover:to-pink-700 transition-colors flex items-center space-x-2">
                        <Play className="h-4 w-4" />
                        <span>Continue</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
            <input
              type="text"
              placeholder="Search courses, instructors, skills..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
          <button className="flex items-center space-x-2 bg-white/10 border border-white/20 rounded-xl px-6 py-3 text-white hover:bg-white/20 transition-colors backdrop-blur-sm">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Categories */}
        <div className="flex space-x-4 mb-8 overflow-x-auto pb-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeCategory === category.id
                  ? 'bg-gradient-to-r from-orange-600 to-pink-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <category.icon className="h-5 w-5" />
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Courses Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredCourses.map((course) => (
            <div
              key={course.id}
              onClick={() => openCourseDetails(course)}
              className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105 cursor-pointer"
            >
              <div className="relative">
                <img
                  src={course.thumbnail}
                  alt={course.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 left-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                  {course.level}
                </div>
                <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>{course.duration}</span>
                </div>
                {course.isEnrolled && (
                  <div className="absolute bottom-3 left-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                    <CheckCircle className="h-3 w-3" />
                    <span>Enrolled</span>
                  </div>
                )}
              </div>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-2">{course.title}</h3>
                <div className="flex items-center space-x-2 mb-3">
                  <img
                    src={course.instructorAvatar}
                    alt={course.instructor}
                    className="w-6 h-6 rounded-full"
                  />
                  <span className="text-white/70 text-sm">{course.instructor}</span>
                </div>
                <p className="text-white/60 text-sm mb-4">{course.description}</p>
                <div className="flex items-center space-x-4 mb-4 text-sm text-white/70">
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{course.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{course.students.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{course.lessons} lessons</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mb-4">
                  {course.skills.slice(0, 3).map((skill, index) => (
                    <span key={index} className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs">
                      {skill}
                    </span>
                  ))}
                  {course.skills.length > 3 && (
                    <span className="text-white/60 text-xs">+{course.skills.length - 3} more</span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl font-bold text-white">${course.price}</span>
                    <span className="text-white/60 line-through text-sm">${course.originalPrice}</span>
                  </div>
                  {course.isEnrolled ? (
                    <button className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors flex items-center space-x-2">
                      <Play className="h-4 w-4" />
                      <span>Continue</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => enrollInCourse(course)}
                      className="bg-gradient-to-r from-orange-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-orange-700 hover:to-pink-700 transition-colors"
                    >
                      Enroll Now
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Achievements */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Your Achievements</h2>
          <div className="grid md:grid-cols-4 gap-4">
            {achievements.map((achievement, index) => (
              <div key={index} className={`bg-gradient-to-br backdrop-blur-sm border rounded-2xl p-6 text-center transition-all duration-300 ${
                achievement.earned 
                  ? 'from-yellow-600/20 to-orange-600/20 border-yellow-500/30' 
                  : 'from-white/5 to-white/5 border-white/10'
              }`}>
                <div className={`inline-flex p-4 rounded-2xl mb-4 ${
                  achievement.earned 
                    ? 'bg-gradient-to-r from-yellow-500 to-orange-500' 
                    : 'bg-white/10'
                }`}>
                  <achievement.icon className={`h-8 w-8 ${
                    achievement.earned ? 'text-white' : 'text-white/40'
                  }`} />
                </div>
                <h3 className={`text-lg font-semibold mb-2 ${
                  achievement.earned ? 'text-white' : 'text-white/60'
                }`}>
                  {achievement.title}
                </h3>
                {achievement.earned ? (
                  <span className="text-yellow-400 text-sm">Earned!</span>
                ) : (
                  <span className="text-white/40 text-sm">Not earned yet</span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showCourseModal && <CourseDetailModal />}
      {showVideoPlayer && <VideoPlayerModal />}
    </div>
  );
};

export default LearningPlatform;