import React, { useState, useEffect } from 'react';
import { <PERSON>, Mic, MicOff, <PERSON>Off, Users, Eye, Heart, MessageCircle, Share2, Settings, Monitor, Smartphone, Tv, Globe, Star, Gift, Zap, Crown, Gem, Play, Pause, Square, Camera } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface StreamData {
  id: string;
  title: string;
  streamer: string;
  viewers: number;
  likes: number;
  category: string;
  thumbnail: string;
  isLive: boolean;
  duration?: string;
  tags: string[];
  revenue: number;
}

interface StreamSettings {
  quality: '720p' | '1080p' | '4K' | '8K';
  bitrate: number;
  fps: number;
  audioQuality: 'standard' | 'high' | 'studio';
  platform: string[];
  monetization: boolean;
  aiEnhancement: boolean;
  neuralSync: boolean;
}

const LiveStreaming: React.FC = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamSettings, setStreamSettings] = useState<StreamSettings>({
    quality: '1080p',
    bitrate: 6000,
    fps: 60,
    audioQuality: 'high',
    platform: ['NeuroSphere', 'YouTube', 'Twitch'],
    monetization: true,
    aiEnhancement: true,
    neuralSync: false
  });
  const [viewers, setViewers] = useState(0);
  const [revenue, setRevenue] = useState(0);
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const { state, dispatch } = useApp();

  const featuredStreams: StreamData[] = [
    {
      id: '1',
      title: 'AI Art Creation Live - Neural Painting Session',
      streamer: 'DigitalDaVinci',
      viewers: 15420,
      likes: 8934,
      category: 'Art & Creativity',
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      isLive: true,
      tags: ['AI Art', 'Neural Interface', 'Creative'],
      revenue: 2847
    },
    {
      id: '2',
      title: 'Quantum Computing Explained - Live Lab Session',
      streamer: 'QuantumGuru',
      viewers: 12567,
      likes: 6789,
      category: 'Science & Tech',
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      isLive: true,
      tags: ['Quantum', 'Science', 'Education'],
      revenue: 1923
    },
    {
      id: '3',
      title: 'Metaverse World Building - Creating Virtual Realms',
      streamer: 'WorldArchitect',
      viewers: 23456,
      likes: 12345,
      category: 'Gaming & Virtual Worlds',
      thumbnail: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=400',
      isLive: true,
      tags: ['Metaverse', 'VR', 'Building'],
      revenue: 4567
    }
  ];

  useEffect(() => {
    if (isStreaming) {
      const interval = setInterval(() => {
        setViewers(prev => prev + Math.floor(Math.random() * 10));
        setRevenue(prev => prev + Math.random() * 5);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  const startStream = () => {
    setIsStreaming(true);
    setViewers(1);
    setRevenue(0);
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: 'Live stream started! Broadcasting to multiple platforms.',
        type: 'success'
      }
    });
  };

  const stopStream = () => {
    setIsStreaming(false);
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: `Stream ended! Total viewers: ${viewers}, Revenue: $${revenue.toFixed(2)}`,
        type: 'info'
      }
    });
  };

  const StreamControlPanel = () => (
    <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
      <h3 className="text-xl font-bold text-white mb-6">Stream Control Center</h3>
      
      {/* Stream Status */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <div className="bg-black/40 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-white mb-1">{viewers.toLocaleString()}</div>
          <div className="text-white/60 text-sm">Live Viewers</div>
        </div>
        <div className="bg-black/40 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-green-400 mb-1">${revenue.toFixed(2)}</div>
          <div className="text-white/60 text-sm">Revenue</div>
        </div>
        <div className="bg-black/40 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-blue-400 mb-1">{streamSettings.quality}</div>
          <div className="text-white/60 text-sm">Quality</div>
        </div>
        <div className="bg-black/40 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-purple-400 mb-1">{streamSettings.platform.length}</div>
          <div className="text-white/60 text-sm">Platforms</div>
        </div>
      </div>

      {/* Stream Controls */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        {!isStreaming ? (
          <button
            onClick={startStream}
            className="bg-gradient-to-r from-red-600 to-red-700 text-white px-8 py-4 rounded-xl font-semibold hover:from-red-700 hover:to-red-800 transition-colors flex items-center space-x-2"
          >
            <Play className="h-6 w-6" />
            <span>Go Live</span>
          </button>
        ) : (
          <button
            onClick={stopStream}
            className="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-8 py-4 rounded-xl font-semibold hover:from-gray-700 hover:to-gray-800 transition-colors flex items-center space-x-2"
          >
            <Square className="h-6 w-6" />
            <span>End Stream</span>
          </button>
        )}
        
        <button className="bg-white/10 hover:bg-white/20 text-white p-4 rounded-xl transition-colors">
          <Video className="h-6 w-6" />
        </button>
        <button className="bg-white/10 hover:bg-white/20 text-white p-4 rounded-xl transition-colors">
          <Mic className="h-6 w-6" />
        </button>
        <button className="bg-white/10 hover:bg-white/20 text-white p-4 rounded-xl transition-colors">
          <Monitor className="h-6 w-6" />
        </button>
        <button className="bg-white/10 hover:bg-white/20 text-white p-4 rounded-xl transition-colors">
          <Settings className="h-6 w-6" />
        </button>
      </div>

      {/* AI Enhancement Features */}
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">AI Enhancements</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/80">Auto-Framing</span>
              <div className="bg-blue-600 w-12 h-6 rounded-full p-1">
                <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80">Noise Reduction</span>
              <div className="bg-blue-600 w-12 h-6 rounded-full p-1">
                <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80">Real-time Translation</span>
              <div className="bg-blue-600 w-12 h-6 rounded-full p-1">
                <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80">Neural Sync Mode</span>
              <div className="bg-purple-600 w-12 h-6 rounded-full p-1">
                <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">Monetization</h4>
          <div className="space-y-3">
            <div className="bg-green-500/20 text-green-400 p-3 rounded-lg">
              <div className="font-semibold mb-1">Super Chat: $45.67</div>
              <div className="text-sm">From premium donations</div>
            </div>
            <div className="bg-blue-500/20 text-blue-400 p-3 rounded-lg">
              <div className="font-semibold mb-1">NFT Sales: $123.45</div>
              <div className="text-sm">Live-minted content</div>
            </div>
            <div className="bg-purple-500/20 text-purple-400 p-3 rounded-lg">
              <div className="font-semibold mb-1">Subscriptions: $67.89</div>
              <div className="text-sm">New subscribers</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Live <span className="bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">Streaming</span>
          </h1>
          <p className="text-white/70 text-lg">Broadcast to the world with AI-enhanced streaming technology</p>
        </div>

        {/* Stream Control Panel */}
        <div className="mb-8">
          <StreamControlPanel />
        </div>

        {/* Featured Live Streams */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-6">Featured Live Streams</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {featuredStreams.map((stream) => (
              <div key={stream.id} className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
                <div className="relative">
                  <img
                    src={stream.thumbnail}
                    alt={stream.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>LIVE</span>
                  </div>
                  <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{stream.viewers.toLocaleString()}</span>
                  </div>
                  <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="w-full bg-gradient-to-r from-red-600 to-pink-600 text-white py-2 rounded-lg font-medium hover:from-red-700 hover:to-pink-700 transition-colors">
                      Watch Stream
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold mb-2 line-clamp-2">{stream.title}</h3>
                  <p className="text-white/70 text-sm mb-3">{stream.streamer}</p>
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                      {stream.category}
                    </span>
                    <div className="flex items-center space-x-3 text-white/60 text-sm">
                      <div className="flex items-center space-x-1">
                        <Heart className="h-4 w-4" />
                        <span>{stream.likes.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Zap className="h-4 w-4" />
                        <span>${stream.revenue}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {stream.tags.map((tag, index) => (
                      <span key={index} className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Streaming Analytics */}
        <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
          <h3 className="text-2xl font-bold text-white mb-6 text-center">Your Streaming Analytics</h3>
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">2.3M</div>
              <div className="text-white/60">Total Views</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">$12.4K</div>
              <div className="text-white/60">Total Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">847K</div>
              <div className="text-white/60">Followers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">156</div>
              <div className="text-white/60">Streams</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveStreaming;