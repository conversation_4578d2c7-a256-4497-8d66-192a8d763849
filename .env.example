# Frontend Environment Variables

# API Configuration
VITE_API_BASE_URL=http://localhost:4000/api
VITE_WS_URL=http://localhost:4000

# App Configuration
VITE_APP_NAME=NeuroSphere
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=The Ultimate Super App Platform

# Feature Flags
VITE_ENABLE_WEBSOCKET=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PAYMENTS=true

# External Services
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
VITE_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
VITE_SENTRY_DSN=your_sentry_dsn

# Social Media Integration
VITE_TWITTER_API_KEY=your_twitter_api_key
VITE_FACEBOOK_APP_ID=your_facebook_app_id
VITE_GOOGLE_CLIENT_ID=your_google_client_id

# Blockchain Configuration
VITE_ETHEREUM_NETWORK=mainnet
VITE_POLYGON_NETWORK=polygon
VITE_WALLET_CONNECT_PROJECT_ID=your_wallet_connect_project_id

# File Upload Configuration
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,audio/mpeg

# Development Configuration
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug
