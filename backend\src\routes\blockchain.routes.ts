import { Router } from 'express';
import {
  getWalletInfo,
  connectWallet,
  disconnectWallet,
  getDeFiProtocols,
  getProtocolDetails,
  stakeTokens,
  unstakeTokens,
  claimRewards,
  getStakingHistory,
  getDAOProposals,
  voteOnProposal,
  createProposal,
  getLaunchpadProjects,
  participateInLaunchpad,
  getCryptoPrices,
  getPortfolio,
  getTransactionHistory,
  swapTokens,
  addLiquidity,
  removeLiquidity,
  getYieldFarms,
  joinYieldFarm,
  exitYieldFarm
} from '../controllers/blockchain.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { validateBlockchainTransaction } from '../middleware/validation.middleware';

const router = Router();

// All blockchain routes require authentication
router.use(authenticateToken);

// Wallet Management
router.get('/wallet', getWalletInfo);
router.post('/wallet/connect', connectWallet);
router.post('/wallet/disconnect', disconnectWallet);

// DeFi Protocols
router.get('/defi/protocols', getDeFiProtocols);
router.get('/defi/protocols/:id', getProtocolDetails);

// Staking
router.post('/staking/stake', validateBlockchainTransaction, stakeTokens);
router.post('/staking/unstake', validateBlockchainTransaction, unstakeTokens);
router.post('/staking/claim-rewards', claimRewards);
router.get('/staking/history', getStakingHistory);

// DAO Governance
router.get('/dao/proposals', getDAOProposals);
router.post('/dao/proposals', createProposal);
router.post('/dao/proposals/:id/vote', voteOnProposal);

// Launchpad
router.get('/launchpad/projects', getLaunchpadProjects);
router.post('/launchpad/participate/:id', participateInLaunchpad);

// Trading & Portfolio
router.get('/prices', getCryptoPrices);
router.get('/portfolio', getPortfolio);
router.get('/transactions', getTransactionHistory);
router.post('/swap', validateBlockchainTransaction, swapTokens);

// Liquidity & Yield Farming
router.post('/liquidity/add', validateBlockchainTransaction, addLiquidity);
router.post('/liquidity/remove', validateBlockchainTransaction, removeLiquidity);
router.get('/yield-farms', getYieldFarms);
router.post('/yield-farms/:id/join', joinYieldFarm);
router.post('/yield-farms/:id/exit', exitYieldFarm);

export default router;
