import React, { useState } from 'react';
import { 
  Heart, 
  Activity, 
  Moon, 
  Zap, 
  Target, 
  Calendar,
  TrendingUp,
  Award,
  Clock,
  User,
  Phone,
  MessageCircle,
  Plus,
  Play,
  CheckCircle
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const HealthWellness: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { state } = useApp();

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: Activity },
    { id: 'fitness', name: 'Fitness', icon: Zap },
    { id: 'nutrition', name: 'Nutrition', icon: Target },
    { id: 'mental', name: 'Mental Health', icon: Heart },
    { id: 'telemedicine', name: 'Telemedicine', icon: User }
  ];

  const healthMetrics = [
    { 
      label: 'Steps Today', 
      value: state.healthData?.steps || 8420, 
      target: 10000, 
      unit: 'steps',
      icon: Activity, 
      color: 'from-blue-500 to-blue-600',
      change: '+12%'
    },
    { 
      label: 'Heart Rate', 
      value: state.healthData?.heartRate || 72, 
      target: 80, 
      unit: 'bpm',
      icon: Heart, 
      color: 'from-red-500 to-red-600',
      change: 'Normal'
    },
    { 
      label: 'Sleep Quality', 
      value: state.healthData?.sleep || 7.5, 
      target: 8, 
      unit: 'hours',
      icon: Moon, 
      color: 'from-purple-500 to-purple-600',
      change: '+0.5h'
    },
    { 
      label: 'Calories Burned', 
      value: 2340, 
      target: 2500, 
      unit: 'cal',
      icon: Zap, 
      color: 'from-orange-500 to-orange-600',
      change: '+8%'
    }
  ];

  const workouts = [
    {
      id: '1',
      name: 'Morning HIIT',
      duration: 25,
      calories: 320,
      type: 'Cardio',
      completed: true,
      thumbnail: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=300'
    },
    {
      id: '2',
      name: 'Strength Training',
      duration: 45,
      calories: 280,
      type: 'Strength',
      completed: false,
      thumbnail: 'https://images.pexels.com/photos/1552252/pexels-photo-1552252.jpeg?auto=compress&cs=tinysrgb&w=300'
    },
    {
      id: '3',
      name: 'Yoga Flow',
      duration: 30,
      calories: 150,
      type: 'Flexibility',
      completed: false,
      thumbnail: 'https://images.pexels.com/photos/3822622/pexels-photo-3822622.jpeg?auto=compress&cs=tinysrgb&w=300'
    }
  ];

  const mentalHealthActivities = [
    {
      id: '1',
      name: 'Mindfulness Meditation',
      duration: 10,
      type: 'Meditation',
      completed: true,
      icon: Heart
    },
    {
      id: '2',
      name: 'Gratitude Journal',
      duration: 5,
      type: 'Journaling',
      completed: true,
      icon: MessageCircle
    },
    {
      id: '3',
      name: 'Breathing Exercise',
      duration: 15,
      type: 'Breathing',
      completed: false,
      icon: Activity
    }
  ];

  const doctors = [
    {
      id: '1',
      name: 'Dr. Sarah Johnson',
      specialty: 'General Medicine',
      rating: 4.9,
      nextAvailable: 'Today, 2:00 PM',
      avatar: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2'
    },
    {
      id: '2',
      name: 'Dr. Michael Chen',
      specialty: 'Cardiology',
      rating: 4.8,
      nextAvailable: 'Tomorrow, 10:00 AM',
      avatar: 'https://images.pexels.com/photos/5327656/pexels-photo-5327656.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2'
    },
    {
      id: '3',
      name: 'Dr. Emily Rodriguez',
      specialty: 'Mental Health',
      rating: 4.9,
      nextAvailable: 'Today, 4:30 PM',
      avatar: 'https://images.pexels.com/photos/5327921/pexels-photo-5327921.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2'
    }
  ];

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Health Metrics */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {healthMetrics.map((metric, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${metric.color}`}>
                <metric.icon className="h-6 w-6 text-white" />
              </div>
              <span className="text-green-400 text-sm font-medium">{metric.change}</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {metric.value.toLocaleString()} {metric.unit}
            </div>
            <div className="text-white/60 text-sm mb-3">{metric.label}</div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className={`bg-gradient-to-r ${metric.color} h-2 rounded-full transition-all duration-300`}
                style={{ width: `${Math.min((metric.value / metric.target) * 100, 100)}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Today's Activities */}
      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Today's Workouts</h3>
          <div className="space-y-4">
            {workouts.map((workout) => (
              <div key={workout.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-4">
                <div className="flex items-center space-x-4">
                  <img
                    src={workout.thumbnail}
                    alt={workout.name}
                    className="w-16 h-16 rounded-xl object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-lg font-semibold text-white">{workout.name}</h4>
                      {workout.completed && (
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-white/60 text-sm">
                      <span>{workout.duration} min</span>
                      <span>{workout.calories} cal</span>
                      <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                        {workout.type}
                      </span>
                    </div>
                  </div>
                  <button className={`p-3 rounded-xl transition-colors ${
                    workout.completed 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}>
                    {workout.completed ? <CheckCircle className="h-5 w-5" /> : <Play className="h-5 w-5" />}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Mental Wellness</h3>
          <div className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 mb-6">
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-white mb-2">Great</div>
              <div className="text-purple-300">Today's Mood</div>
            </div>
            <div className="flex justify-center space-x-2 mb-4">
              {['😢', '😐', '🙂', '😊', '😄'].map((emoji, index) => (
                <button
                  key={index}
                  className={`text-2xl p-2 rounded-lg transition-colors ${
                    index === 3 ? 'bg-purple-500/30' : 'hover:bg-white/10'
                  }`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
          <div className="space-y-3">
            {mentalHealthActivities.map((activity) => (
              <div key={activity.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      activity.completed ? 'bg-green-500/20' : 'bg-purple-500/20'
                    }`}>
                      <activity.icon className={`h-5 w-5 ${
                        activity.completed ? 'text-green-400' : 'text-purple-400'
                      }`} />
                    </div>
                    <div>
                      <div className="text-white font-medium">{activity.name}</div>
                      <div className="text-white/60 text-sm">{activity.duration} min • {activity.type}</div>
                    </div>
                  </div>
                  {activity.completed ? (
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  ) : (
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-lg transition-colors">
                      <Play className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Health Insights */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">AI Health Insights</h3>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-gradient-to-r from-green-500 to-green-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Improving Trend</h4>
            <p className="text-white/70 text-sm">Your sleep quality has improved by 15% this week</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Target className="h-6 w-6 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Goal Achievement</h4>
            <p className="text-white/70 text-sm">You're 84% towards your weekly step goal</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Award className="h-6 w-6 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Recommendation</h4>
            <p className="text-white/70 text-sm">Try 10 minutes of meditation before bed for better sleep</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTelemedicine = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Telemedicine Services</h3>
        <p className="text-white/70 text-lg">Connect with healthcare professionals from anywhere</p>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <button className="bg-gradient-to-r from-red-600 to-red-700 text-white p-6 rounded-2xl text-left hover:from-red-700 hover:to-red-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Phone className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Emergency Call</span>
          </div>
          <p className="text-red-100">24/7 emergency medical consultation</p>
        </button>

        <button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-2xl text-left hover:from-blue-700 hover:to-blue-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Calendar className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Book Appointment</span>
          </div>
          <p className="text-blue-100">Schedule with your preferred doctor</p>
        </button>

        <button className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-2xl text-left hover:from-green-700 hover:to-green-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <MessageCircle className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Chat Support</span>
          </div>
          <p className="text-green-100">Get instant health advice via chat</p>
        </button>
      </div>

      {/* Available Doctors */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Available Doctors</h4>
        <div className="grid md:grid-cols-3 gap-6">
          {doctors.map((doctor) => (
            <div key={doctor.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="text-center mb-4">
                <img
                  src={doctor.avatar}
                  alt={doctor.name}
                  className="w-20 h-20 rounded-full mx-auto mb-3 border-4 border-blue-400"
                />
                <h5 className="text-lg font-semibold text-white">{doctor.name}</h5>
                <p className="text-blue-400 text-sm">{doctor.specialty}</p>
              </div>
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">Rating</span>
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-400 text-sm">{doctor.rating}</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className="text-yellow-400 text-xs">★</span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">Next Available</span>
                  <span className="text-green-400 text-sm">{doctor.nextAvailable}</span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-colors text-sm">
                  Book Now
                </button>
                <button className="flex-1 bg-white/10 border border-white/20 text-white py-2 rounded-lg hover:bg-white/20 transition-colors text-sm">
                  View Profile
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Consultations */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Recent Consultations</h4>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b border-white/10">
              <div className="flex items-center space-x-3">
                <img
                  src={doctors[0].avatar}
                  alt={doctors[0].name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="text-white font-medium">General Checkup</div>
                  <div className="text-white/60 text-sm">with {doctors[0].name} • 2 days ago</div>
                </div>
              </div>
              <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                View Report
              </button>
            </div>
            <div className="flex items-center justify-between py-3 border-b border-white/10">
              <div className="flex items-center space-x-3">
                <img
                  src={doctors[2].avatar}
                  alt={doctors[2].name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="text-white font-medium">Mental Health Session</div>
                  <div className="text-white/60 text-sm">with {doctors[2].name} • 1 week ago</div>
                </div>
              </div>
              <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                View Notes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Health & <span className="bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">Wellness</span>
          </h1>
          <p className="text-white/70 text-lg">Your comprehensive health and wellness companion</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-red-600 to-pink-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'telemedicine' && renderTelemedicine()}
          {activeTab === 'fitness' && (
            <div className="text-center py-12">
              <Zap className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Fitness Tracking</h3>
              <p className="text-white/60">Track workouts, set goals, and monitor progress</p>
            </div>
          )}
          {activeTab === 'nutrition' && (
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Nutrition Planning</h3>
              <p className="text-white/60">AI-powered meal planning and nutrition tracking</p>
            </div>
          )}
          {activeTab === 'mental' && (
            <div className="text-center py-12">
              <Heart className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Mental Health</h3>
              <p className="text-white/60">Meditation, mood tracking, and mental wellness tools</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HealthWellness;