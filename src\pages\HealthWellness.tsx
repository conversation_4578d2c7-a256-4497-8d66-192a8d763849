import React, { useState } from 'react';
import { 
  Heart, 
  Activity, 
  Moon, 
  Zap, 
  Target, 
  Calendar,
  TrendingUp,
  Award,
  Clock,
  User,
  Phone,
  MessageCircle,
  Plus,
  Play,
  CheckCircle
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const HealthWellness: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedWorkout, setSelectedWorkout] = useState<any>(null);
  const [showWorkoutModal, setShowWorkoutModal] = useState(false);
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<any>(null);
  const [currentMood, setCurrentMood] = useState(3);
  const { state, dispatch } = useApp();

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: Activity },
    { id: 'fitness', name: 'Fitness', icon: Zap },
    { id: 'nutrition', name: 'Nutrition', icon: Target },
    { id: 'mental', name: 'Mental Health', icon: Heart },
    { id: 'telemedicine', name: 'Telemedicine', icon: User }
  ];

  const healthMetrics = [
    { 
      label: 'Steps Today', 
      value: state.healthData?.steps || 8420, 
      target: 10000, 
      unit: 'steps',
      icon: Activity, 
      color: 'from-blue-500 to-blue-600',
      change: '+12%'
    },
    { 
      label: 'Heart Rate', 
      value: state.healthData?.heartRate || 72, 
      target: 80, 
      unit: 'bpm',
      icon: Heart, 
      color: 'from-red-500 to-red-600',
      change: 'Normal'
    },
    { 
      label: 'Sleep Quality', 
      value: state.healthData?.sleep || 7.5, 
      target: 8, 
      unit: 'hours',
      icon: Moon, 
      color: 'from-purple-500 to-purple-600',
      change: '+0.5h'
    },
    { 
      label: 'Calories Burned', 
      value: 2340, 
      target: 2500, 
      unit: 'cal',
      icon: Zap, 
      color: 'from-orange-500 to-orange-600',
      change: '+8%'
    }
  ];

  const workouts = [
    {
      id: '1',
      name: 'Morning HIIT',
      duration: 25,
      calories: 320,
      type: 'Cardio',
      completed: true,
      thumbnail: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=300'
    },
    {
      id: '2',
      name: 'Strength Training',
      duration: 45,
      calories: 280,
      type: 'Strength',
      completed: false,
      thumbnail: 'https://images.pexels.com/photos/1552252/pexels-photo-1552252.jpeg?auto=compress&cs=tinysrgb&w=300'
    },
    {
      id: '3',
      name: 'Yoga Flow',
      duration: 30,
      calories: 150,
      type: 'Flexibility',
      completed: false,
      thumbnail: 'https://images.pexels.com/photos/3822622/pexels-photo-3822622.jpeg?auto=compress&cs=tinysrgb&w=300'
    }
  ];

  const mentalHealthActivities = [
    {
      id: '1',
      name: 'Mindfulness Meditation',
      duration: 10,
      type: 'Meditation',
      completed: true,
      icon: Heart
    },
    {
      id: '2',
      name: 'Gratitude Journal',
      duration: 5,
      type: 'Journaling',
      completed: true,
      icon: MessageCircle
    },
    {
      id: '3',
      name: 'Breathing Exercise',
      duration: 15,
      type: 'Breathing',
      completed: false,
      icon: Activity
    }
  ];

  const doctors = [
    {
      id: '1',
      name: 'Dr. Sarah Johnson',
      specialty: 'General Medicine',
      rating: 4.9,
      nextAvailable: 'Today, 2:00 PM',
      avatar: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: '$150',
      experience: '15 years',
      languages: ['English', 'Spanish']
    },
    {
      id: '2',
      name: 'Dr. Michael Chen',
      specialty: 'Cardiology',
      rating: 4.8,
      nextAvailable: 'Tomorrow, 10:00 AM',
      avatar: 'https://images.pexels.com/photos/5327656/pexels-photo-5327656.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: '$200',
      experience: '20 years',
      languages: ['English', 'Mandarin']
    },
    {
      id: '3',
      name: 'Dr. Emily Rodriguez',
      specialty: 'Mental Health',
      rating: 4.9,
      nextAvailable: 'Today, 4:30 PM',
      avatar: 'https://images.pexels.com/photos/5327921/pexels-photo-5327921.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
      price: '$180',
      experience: '12 years',
      languages: ['English', 'Spanish', 'French']
    }
  ];

  const nutritionPlan = [
    {
      meal: 'Breakfast',
      time: '8:00 AM',
      calories: 450,
      items: ['Oatmeal with berries', 'Greek yogurt', 'Green tea'],
      completed: true
    },
    {
      meal: 'Lunch',
      time: '12:30 PM',
      calories: 650,
      items: ['Grilled chicken salad', 'Quinoa', 'Avocado'],
      completed: true
    },
    {
      meal: 'Dinner',
      time: '7:00 PM',
      calories: 580,
      items: ['Salmon', 'Steamed vegetables', 'Brown rice'],
      completed: false
    },
    {
      meal: 'Snack',
      time: '3:00 PM',
      calories: 200,
      items: ['Mixed nuts', 'Apple'],
      completed: false
    }
  ];

  const fitnessGoals = [
    { name: 'Daily Steps', current: 8420, target: 10000, unit: 'steps' },
    { name: 'Weekly Workouts', current: 4, target: 5, unit: 'sessions' },
    { name: 'Water Intake', current: 6, target: 8, unit: 'glasses' },
    { name: 'Sleep Hours', current: 7.5, target: 8, unit: 'hours' }
  ];

  const startWorkout = (workout: any) => {
    setSelectedWorkout(workout);
    setShowWorkoutModal(true);
  };

  const bookAppointment = (doctor: any) => {
    setSelectedDoctor(doctor);
    setShowAppointmentModal(true);
  };

  const completeWorkout = (workoutId: string) => {
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: 'Workout completed! Great job!',
        type: 'success'
      }
    });
  };

  const updateMood = (moodIndex: number) => {
    setCurrentMood(moodIndex);
    const moods = ['Very Sad', 'Sad', 'Neutral', 'Happy', 'Very Happy'];
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: `Mood updated to ${moods[moodIndex]}`,
        type: 'info'
      }
    });
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Health Metrics */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {healthMetrics.map((metric, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${metric.color}`}>
                <metric.icon className="h-6 w-6 text-white" />
              </div>
              <span className="text-green-400 text-sm font-medium">{metric.change}</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {metric.value.toLocaleString()} {metric.unit}
            </div>
            <div className="text-white/60 text-sm mb-3">{metric.label}</div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className={`bg-gradient-to-r ${metric.color} h-2 rounded-full transition-all duration-300`}
                style={{ width: `${Math.min((metric.value / metric.target) * 100, 100)}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Today's Activities */}
      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Today's Workouts</h3>
          <div className="space-y-4">
            {workouts.map((workout) => (
              <div key={workout.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-4">
                <div className="flex items-center space-x-4">
                  <img
                    src={workout.thumbnail}
                    alt={workout.name}
                    className="w-16 h-16 rounded-xl object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-lg font-semibold text-white">{workout.name}</h4>
                      {workout.completed && (
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-white/60 text-sm">
                      <span>{workout.duration} min</span>
                      <span>{workout.calories} cal</span>
                      <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                        {workout.type}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => workout.completed ? null : startWorkout(workout)}
                    className={`p-3 rounded-xl transition-colors ${
                      workout.completed
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {workout.completed ? <CheckCircle className="h-5 w-5" /> : <Play className="h-5 w-5" />}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Mental Wellness</h3>
          <div className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6 mb-6">
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-white mb-2">Great</div>
              <div className="text-purple-300">Today's Mood</div>
            </div>
            <div className="flex justify-center space-x-2 mb-4">
              {['😢', '😐', '🙂', '😊', '😄'].map((emoji, index) => (
                <button
                  key={index}
                  onClick={() => updateMood(index)}
                  className={`text-2xl p-2 rounded-lg transition-colors ${
                    index === currentMood ? 'bg-purple-500/30' : 'hover:bg-white/10'
                  }`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
          <div className="space-y-3">
            {mentalHealthActivities.map((activity) => (
              <div key={activity.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      activity.completed ? 'bg-green-500/20' : 'bg-purple-500/20'
                    }`}>
                      <activity.icon className={`h-5 w-5 ${
                        activity.completed ? 'text-green-400' : 'text-purple-400'
                      }`} />
                    </div>
                    <div>
                      <div className="text-white font-medium">{activity.name}</div>
                      <div className="text-white/60 text-sm">{activity.duration} min • {activity.type}</div>
                    </div>
                  </div>
                  {activity.completed ? (
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  ) : (
                    <button className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-lg transition-colors">
                      <Play className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Health Insights */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">AI Health Insights</h3>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-gradient-to-r from-green-500 to-green-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Improving Trend</h4>
            <p className="text-white/70 text-sm">Your sleep quality has improved by 15% this week</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Target className="h-6 w-6 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Goal Achievement</h4>
            <p className="text-white/70 text-sm">You're 84% towards your weekly step goal</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Award className="h-6 w-6 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Recommendation</h4>
            <p className="text-white/70 text-sm">Try 10 minutes of meditation before bed for better sleep</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTelemedicine = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Telemedicine Services</h3>
        <p className="text-white/70 text-lg">Connect with healthcare professionals from anywhere</p>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <button className="bg-gradient-to-r from-red-600 to-red-700 text-white p-6 rounded-2xl text-left hover:from-red-700 hover:to-red-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Phone className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Emergency Call</span>
          </div>
          <p className="text-red-100">24/7 emergency medical consultation</p>
        </button>

        <button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-2xl text-left hover:from-blue-700 hover:to-blue-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <Calendar className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Book Appointment</span>
          </div>
          <p className="text-blue-100">Schedule with your preferred doctor</p>
        </button>

        <button className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-2xl text-left hover:from-green-700 hover:to-green-800 transition-colors group">
          <div className="flex items-center space-x-3 mb-3">
            <MessageCircle className="h-8 w-8 group-hover:scale-110 transition-transform" />
            <span className="text-xl font-semibold">Chat Support</span>
          </div>
          <p className="text-green-100">Get instant health advice via chat</p>
        </button>
      </div>

      {/* Available Doctors */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Available Doctors</h4>
        <div className="grid md:grid-cols-3 gap-6">
          {doctors.map((doctor) => (
            <div key={doctor.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="text-center mb-4">
                <img
                  src={doctor.avatar}
                  alt={doctor.name}
                  className="w-20 h-20 rounded-full mx-auto mb-3 border-4 border-blue-400"
                />
                <h5 className="text-lg font-semibold text-white">{doctor.name}</h5>
                <p className="text-blue-400 text-sm">{doctor.specialty}</p>
              </div>
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">Rating</span>
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-400 text-sm">{doctor.rating}</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className="text-yellow-400 text-xs">★</span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">Next Available</span>
                  <span className="text-green-400 text-sm">{doctor.nextAvailable}</span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => bookAppointment(doctor)}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-colors text-sm"
                >
                  Book Now
                </button>
                <button className="flex-1 bg-white/10 border border-white/20 text-white py-2 rounded-lg hover:bg-white/20 transition-colors text-sm">
                  View Profile
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Consultations */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Recent Consultations</h4>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b border-white/10">
              <div className="flex items-center space-x-3">
                <img
                  src={doctors[0].avatar}
                  alt={doctors[0].name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="text-white font-medium">General Checkup</div>
                  <div className="text-white/60 text-sm">with {doctors[0].name} • 2 days ago</div>
                </div>
              </div>
              <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                View Report
              </button>
            </div>
            <div className="flex items-center justify-between py-3 border-b border-white/10">
              <div className="flex items-center space-x-3">
                <img
                  src={doctors[2].avatar}
                  alt={doctors[2].name}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="text-white font-medium">Mental Health Session</div>
                  <div className="text-white/60 text-sm">with {doctors[2].name} • 1 week ago</div>
                </div>
              </div>
              <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                View Notes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Health & <span className="bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">Wellness</span>
          </h1>
          <p className="text-white/70 text-lg">Your comprehensive health and wellness companion</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-red-600 to-pink-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'telemedicine' && renderTelemedicine()}
          {activeTab === 'fitness' && (
            <div className="space-y-8">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-4">Fitness Tracking</h3>
                <p className="text-white/70 text-lg">Track workouts, set goals, and monitor progress</p>
              </div>

              {/* Fitness Goals */}
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {fitnessGoals.map((goal, index) => (
                  <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                    <h4 className="text-lg font-semibold text-white mb-3">{goal.name}</h4>
                    <div className="text-2xl font-bold text-white mb-2">
                      {goal.current.toLocaleString()} / {goal.target.toLocaleString()}
                    </div>
                    <div className="text-white/60 text-sm mb-3">{goal.unit}</div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min((goal.current / goal.target) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <div className="text-right text-white/60 text-sm mt-1">
                      {Math.round((goal.current / goal.target) * 100)}%
                    </div>
                  </div>
                ))}
              </div>

              {/* Workout Library */}
              <div>
                <h4 className="text-2xl font-bold text-white mb-6">Workout Library</h4>
                <div className="grid md:grid-cols-3 gap-6">
                  {workouts.map((workout) => (
                    <div key={workout.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
                      <img
                        src={workout.thumbnail}
                        alt={workout.name}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="text-lg font-semibold text-white">{workout.name}</h5>
                          {workout.completed && (
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-white/60 text-sm mb-4">
                          <span>{workout.duration} min</span>
                          <span>{workout.calories} cal</span>
                          <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                            {workout.type}
                          </span>
                        </div>
                        <button
                          onClick={() => startWorkout(workout)}
                          className={`w-full py-2 rounded-lg transition-colors ${
                            workout.completed
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'
                          }`}
                        >
                          {workout.completed ? 'Completed' : 'Start Workout'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          {activeTab === 'nutrition' && (
            <div className="space-y-8">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-4">Nutrition Planning</h3>
                <p className="text-white/70 text-lg">AI-powered meal planning and nutrition tracking</p>
              </div>

              {/* Daily Nutrition Overview */}
              <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
                <h4 className="text-2xl font-bold text-white mb-6 text-center">Today's Nutrition</h4>
                <div className="grid md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">1,880</div>
                    <div className="text-white/60">Calories Consumed</div>
                    <div className="text-green-400 text-sm">Goal: 2,000</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-400 mb-2">120g</div>
                    <div className="text-white/60">Protein</div>
                    <div className="text-green-400 text-sm">Goal: 150g</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-400 mb-2">45g</div>
                    <div className="text-white/60">Fat</div>
                    <div className="text-green-400 text-sm">Goal: 65g</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-400 mb-2">180g</div>
                    <div className="text-white/60">Carbs</div>
                    <div className="text-green-400 text-sm">Goal: 200g</div>
                  </div>
                </div>
              </div>

              {/* Meal Plan */}
              <div>
                <h4 className="text-2xl font-bold text-white mb-6">Today's Meal Plan</h4>
                <div className="space-y-4">
                  {nutritionPlan.map((meal, index) => (
                    <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h5 className="text-lg font-semibold text-white">{meal.meal}</h5>
                            <span className="text-white/60 text-sm">{meal.time}</span>
                            {meal.completed && (
                              <CheckCircle className="h-5 w-5 text-green-400" />
                            )}
                          </div>
                          <div className="text-white/60 text-sm mb-3">{meal.calories} calories</div>
                          <div className="flex flex-wrap gap-2">
                            {meal.items.map((item, itemIndex) => (
                              <span key={itemIndex} className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs">
                                {item}
                              </span>
                            ))}
                          </div>
                        </div>
                        <button
                          className={`ml-4 px-4 py-2 rounded-lg transition-colors ${
                            meal.completed
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-blue-600 hover:bg-blue-700 text-white'
                          }`}
                        >
                          {meal.completed ? 'Logged' : 'Log Meal'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Nutrition Insights */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                  <h5 className="text-lg font-semibold text-white mb-4">Weekly Progress</h5>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-white/70">Average Calories</span>
                      <span className="text-white">1,950/day</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Protein Goal Met</span>
                      <span className="text-green-400">5/7 days</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Water Intake</span>
                      <span className="text-blue-400">6.5 glasses/day</span>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                  <h5 className="text-lg font-semibold text-white mb-4">AI Recommendations</h5>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                      <span className="text-white/80">Add more leafy greens to increase fiber intake</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                      <span className="text-white/80">Consider omega-3 rich foods like salmon</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                      <span className="text-white/80">Increase water intake by 2 glasses daily</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {activeTab === 'mental' && (
            <div className="text-center py-12">
              <Heart className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Mental Health</h3>
              <p className="text-white/60">Meditation, mood tracking, and mental wellness tools</p>
            </div>
          )}
        </div>
      </div>

      {/* Workout Modal */}
      {showWorkoutModal && selectedWorkout && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-2xl w-full">
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white">{selectedWorkout.name}</h2>
              <button
                onClick={() => setShowWorkoutModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <img
                    src={selectedWorkout.thumbnail}
                    alt={selectedWorkout.name}
                    className="w-full h-48 object-cover rounded-xl mb-4"
                  />
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-white/70">Duration:</span>
                      <span className="text-white">{selectedWorkout.duration} minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Calories:</span>
                      <span className="text-white">{selectedWorkout.calories} cal</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Type:</span>
                      <span className="text-white">{selectedWorkout.type}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Workout Plan</h3>
                  <div className="space-y-3 mb-6">
                    <div className="bg-white/5 rounded-lg p-3">
                      <div className="text-white font-medium">Warm-up</div>
                      <div className="text-white/60 text-sm">5 minutes</div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3">
                      <div className="text-white font-medium">Main Exercise</div>
                      <div className="text-white/60 text-sm">{selectedWorkout.duration - 10} minutes</div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3">
                      <div className="text-white font-medium">Cool-down</div>
                      <div className="text-white/60 text-sm">5 minutes</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <button
                      onClick={() => {
                        completeWorkout(selectedWorkout.id);
                        setShowWorkoutModal(false);
                      }}
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 rounded-xl hover:from-green-700 hover:to-green-800 transition-colors"
                    >
                      Start Workout
                    </button>
                    <button
                      onClick={() => setShowWorkoutModal(false)}
                      className="w-full bg-white/10 border border-white/20 text-white py-3 rounded-xl hover:bg-white/20 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Appointment Modal */}
      {showAppointmentModal && selectedDoctor && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-2xl w-full">
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white">Book Appointment</h2>
              <button
                onClick={() => setShowAppointmentModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="text-center">
                  <img
                    src={selectedDoctor.avatar}
                    alt={selectedDoctor.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-blue-400"
                  />
                  <h3 className="text-lg font-semibold text-white">{selectedDoctor.name}</h3>
                  <p className="text-blue-400 text-sm mb-4">{selectedDoctor.specialty}</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-white/70">Experience:</span>
                      <span className="text-white">{selectedDoctor.experience}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Price:</span>
                      <span className="text-white">{selectedDoctor.price}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Rating:</span>
                      <span className="text-yellow-400">{selectedDoctor.rating} ⭐</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-4">Select Date & Time</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-white/70 text-sm mb-2">Preferred Date</label>
                      <input
                        type="date"
                        className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-white/70 text-sm mb-2">Preferred Time</label>
                      <select className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>9:00 AM</option>
                        <option>10:00 AM</option>
                        <option>11:00 AM</option>
                        <option>2:00 PM</option>
                        <option>3:00 PM</option>
                        <option>4:00 PM</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-white/70 text-sm mb-2">Reason for Visit</label>
                      <textarea
                        placeholder="Describe your symptoms or reason for consultation..."
                        className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 h-20 resize-none"
                      />
                    </div>
                  </div>

                  <div className="flex space-x-3 mt-6">
                    <button
                      onClick={() => {
                        setShowAppointmentModal(false);
                        dispatch({
                          type: 'ADD_NOTIFICATION',
                          payload: {
                            id: Date.now(),
                            message: `Appointment booked with ${selectedDoctor.name}!`,
                            type: 'success'
                          }
                        });
                      }}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-colors"
                    >
                      Book Appointment
                    </button>
                    <button
                      onClick={() => setShowAppointmentModal(false)}
                      className="flex-1 bg-white/10 border border-white/20 text-white py-3 rounded-xl hover:bg-white/20 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HealthWellness;