// API Base Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000/api';

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors?: any[];
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('authToken');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication methods
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('authToken', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('authToken');
  }

  // Auth endpoints
  async register(userData: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials: { email: string; password: string }) {
    const response = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response;
  }

  async logout() {
    const response = await this.request('/auth/logout', { method: 'POST' });
    this.clearToken();
    return response;
  }

  async getProfile() {
    return this.request('/auth/profile');
  }

  async updateProfile(profileData: any) {
    return this.request('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) {
    return this.request('/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    });
  }

  // Product endpoints
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/products${queryString}`);
  }

  async getProduct(id: string) {
    return this.request(`/products/${id}`);
  }

  async createProduct(productData: any) {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(id: string, productData: any) {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id: string) {
    return this.request(`/products/${id}`, { method: 'DELETE' });
  }

  async addProductReview(productId: string, reviewData: { rating: number; comment: string }) {
    return this.request(`/products/${productId}/reviews`, {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  }

  // Course endpoints
  async getCourses(params?: {
    page?: number;
    limit?: number;
    category?: string;
    level?: string;
    search?: string;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/courses${queryString}`);
  }

  async getCourse(id: string) {
    return this.request(`/courses/${id}`);
  }

  async createCourse(courseData: any) {
    return this.request('/courses', {
      method: 'POST',
      body: JSON.stringify(courseData),
    });
  }

  async enrollInCourse(courseId: string) {
    return this.request(`/courses/${courseId}/enroll`, { method: 'POST' });
  }

  async addCourseReview(courseId: string, reviewData: { rating: number; comment: string }) {
    return this.request(`/courses/${courseId}/reviews`, {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  }

  // Order endpoints
  async getOrders(params?: { page?: number; limit?: number; status?: string }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/orders${queryString}`);
  }

  async getOrder(id: string) {
    return this.request(`/orders/${id}`);
  }

  async createOrder(orderData: any) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async cancelOrder(id: string) {
    return this.request(`/orders/${id}/cancel`, { method: 'PUT' });
  }

  // NFT endpoints
  async getNFTs(params?: {
    page?: number;
    limit?: number;
    category?: string;
    blockchain?: string;
    isForSale?: boolean;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/nfts${queryString}`);
  }

  async getNFT(id: string) {
    return this.request(`/nfts/${id}`);
  }

  async createNFT(nftData: any) {
    return this.request('/nfts', {
      method: 'POST',
      body: JSON.stringify(nftData),
    });
  }

  async transferNFT(id: string, transferData: { newOwnerId: string; price?: number }) {
    return this.request(`/nfts/${id}/transfer`, {
      method: 'POST',
      body: JSON.stringify(transferData),
    });
  }

  async likeNFT(id: string) {
    return this.request(`/nfts/${id}/like`, { method: 'POST' });
  }

  // Analytics endpoints
  async getDashboard() {
    return this.request('/analytics/dashboard');
  }

  async getSalesAnalytics(period?: string) {
    const queryString = period ? `?period=${period}` : '';
    return this.request(`/analytics/sales${queryString}`);
  }

  async getCourseAnalytics() {
    return this.request('/analytics/courses');
  }

  async getNFTAnalytics() {
    return this.request('/analytics/nfts');
  }

  // User endpoints
  async getUsers(params?: { page?: number; search?: string; role?: string }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/users${queryString}`);
  }

  async getUser(id: string) {
    return this.request(`/users/${id}`);
  }

  async getUserStats(id?: string) {
    const endpoint = id ? `/users/${id}/stats` : '/users/stats';
    return this.request(endpoint);
  }

  async followUser(id: string) {
    return this.request(`/users/${id}/follow`, { method: 'POST' });
  }

  // Notification endpoints
  async getUserNotifications(params?: {
    page?: number;
    limit?: number;
    type?: string;
    category?: string;
    read?: boolean;
    priority?: string;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/notifications${queryString}`);
  }

  async markNotificationAsRead(id: string) {
    return this.request(`/notifications/${id}/read`, { method: 'PUT' });
  }

  async markAllNotificationsAsRead() {
    return this.request('/notifications/read-all', { method: 'PUT' });
  }

  async deleteNotification(id: string) {
    return this.request(`/notifications/${id}`, { method: 'DELETE' });
  }

  // Payment endpoints
  async createPaymentIntent(paymentData: {
    amount: number;
    currency?: string;
    orderId?: string;
    metadata?: any;
  }) {
    return this.request('/payments/create-intent', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }

  async confirmPayment(paymentData: {
    paymentIntentId: string;
    orderId?: string;
  }) {
    return this.request('/payments/confirm', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }

  async getPaymentHistory(params?: { page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/payments/history${queryString}`);
  }

  async createSubscription(subscriptionData: {
    priceId: string;
    paymentMethodId?: string;
  }) {
    return this.request('/payments/create-subscription', {
      method: 'POST',
      body: JSON.stringify(subscriptionData),
    });
  }

  // File upload
  async uploadFile(file: File, type: 'image' | 'video' | 'document' = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return this.request('/upload/single', {
      method: 'POST',
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
      body: formData,
    });
  }

  async uploadMultipleFiles(files: File[], type: 'image' | 'video' | 'document' = 'image') {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    formData.append('type', type);

    return this.request('/upload/multiple', {
      method: 'POST',
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
      body: formData,
    });
  }

  // Health endpoints
  async getHealthDashboard() {
    return this.request('/health/dashboard');
  }

  async updateHealthMetrics(metrics: any) {
    return this.request('/health/metrics', {
      method: 'PUT',
      body: JSON.stringify(metrics),
    });
  }

  async getWorkouts(params?: { type?: string; difficulty?: string; page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/health/workouts${queryString}`);
  }

  async createWorkout(workoutData: any) {
    return this.request('/health/workouts', {
      method: 'POST',
      body: JSON.stringify(workoutData),
    });
  }

  async completeWorkout(id: string, data?: { rating?: number; notes?: string }) {
    return this.request(`/health/workouts/${id}/complete`, {
      method: 'PUT',
      body: JSON.stringify(data || {}),
    });
  }

  async getNutritionPlan(date?: string) {
    const queryString = date ? `?date=${date}` : '';
    return this.request(`/health/nutrition${queryString}`);
  }

  async updateNutritionPlan(data: any) {
    return this.request('/health/nutrition', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async logMeal(mealData: any) {
    return this.request('/health/nutrition/log-meal', {
      method: 'POST',
      body: JSON.stringify(mealData),
    });
  }

  async getMentalHealthData(days?: number) {
    const queryString = days ? `?days=${days}` : '';
    return this.request(`/health/mental-health${queryString}`);
  }

  async updateMoodTracking(moodData: any) {
    return this.request('/health/mood-tracking', {
      method: 'PUT',
      body: JSON.stringify(moodData),
    });
  }

  async getDoctors(params?: { specialty?: string; page?: number; limit?: number; search?: string }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/health/doctors${queryString}`);
  }

  async bookAppointment(appointmentData: any) {
    return this.request('/health/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
  }

  async getAppointments(params?: { status?: string; page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/health/appointments${queryString}`);
  }

  async cancelAppointment(id: string) {
    return this.request(`/health/appointments/${id}`, {
      method: 'DELETE',
    });
  }

  async getFitnessGoals() {
    return this.request('/health/fitness-goals');
  }

  async updateFitnessGoals(goalsData: any) {
    return this.request('/health/fitness-goals', {
      method: 'PUT',
      body: JSON.stringify(goalsData),
    });
  }

  async getHealthInsights() {
    return this.request('/health/insights');
  }

  // AI Studio endpoints
  async generateAIContent(data: { type: string; prompt: string; parameters?: any }) {
    return this.request('/ai-studio/generate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getAIGenerationHistory(params?: { page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/ai-studio/generation-history${queryString}`);
  }

  async getAICreations(params?: { type?: string; page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/ai-studio/creations${queryString}`);
  }

  async saveAICreation(creationData: any) {
    return this.request('/ai-studio/creations', {
      method: 'POST',
      body: JSON.stringify(creationData),
    });
  }

  async deleteAICreation(id: string) {
    return this.request(`/ai-studio/creations/${id}`, {
      method: 'DELETE',
    });
  }

  async shareAICreation(id: string) {
    return this.request(`/ai-studio/creations/${id}/share`, {
      method: 'POST',
    });
  }

  async mintCreationAsNFT(id: string, nftData: any) {
    return this.request(`/ai-studio/creations/${id}/mint-nft`, {
      method: 'POST',
      body: JSON.stringify(nftData),
    });
  }

  async getAITools() {
    return this.request('/ai-studio/tools');
  }

  async getAICredits() {
    return this.request('/ai-studio/credits');
  }

  async purchaseAICredits(creditData: any) {
    return this.request('/ai-studio/credits/purchase', {
      method: 'POST',
      body: JSON.stringify(creditData),
    });
  }

  async getAICollaborations() {
    return this.request('/ai-studio/collaborations');
  }

  async createAICollaboration(collaborationData: any) {
    return this.request('/ai-studio/collaborations', {
      method: 'POST',
      body: JSON.stringify(collaborationData),
    });
  }

  async joinAICollaboration(id: string) {
    return this.request(`/ai-studio/collaborations/${id}/join`, {
      method: 'POST',
    });
  }

  async getAIMarketplace(params?: { type?: string; category?: string; page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/ai-studio/marketplace${queryString}`);
  }

  async sellAICreation(creationData: any) {
    return this.request('/ai-studio/marketplace/sell', {
      method: 'POST',
      body: JSON.stringify(creationData),
    });
  }

  async buyAICreation(id: string) {
    return this.request(`/ai-studio/marketplace/buy/${id}`, {
      method: 'POST',
    });
  }

  async getTrendingAIContent() {
    return this.request('/ai-studio/trending');
  }

  async getAIInsights() {
    return this.request('/ai-studio/insights');
  }

  // Blockchain endpoints
  async getWalletInfo() {
    return this.request('/blockchain/wallet');
  }

  async connectWallet(walletData: any) {
    return this.request('/blockchain/wallet/connect', {
      method: 'POST',
      body: JSON.stringify(walletData),
    });
  }

  async disconnectWallet() {
    return this.request('/blockchain/wallet/disconnect', {
      method: 'POST',
    });
  }

  async getDeFiProtocols() {
    return this.request('/blockchain/defi/protocols');
  }

  async getProtocolDetails(id: string) {
    return this.request(`/blockchain/defi/protocols/${id}`);
  }

  async stakeTokens(stakeData: any) {
    return this.request('/blockchain/staking/stake', {
      method: 'POST',
      body: JSON.stringify(stakeData),
    });
  }

  async unstakeTokens(unstakeData: any) {
    return this.request('/blockchain/staking/unstake', {
      method: 'POST',
      body: JSON.stringify(unstakeData),
    });
  }

  async claimStakingRewards() {
    return this.request('/blockchain/staking/claim-rewards', {
      method: 'POST',
    });
  }

  async getStakingHistory() {
    return this.request('/blockchain/staking/history');
  }

  async getDAOProposals() {
    return this.request('/blockchain/dao/proposals');
  }

  async createDAOProposal(proposalData: any) {
    return this.request('/blockchain/dao/proposals', {
      method: 'POST',
      body: JSON.stringify(proposalData),
    });
  }

  async voteOnProposal(id: string, voteData: any) {
    return this.request(`/blockchain/dao/proposals/${id}/vote`, {
      method: 'POST',
      body: JSON.stringify(voteData),
    });
  }

  async getLaunchpadProjects() {
    return this.request('/blockchain/launchpad/projects');
  }

  async participateInLaunchpad(id: string, participationData: any) {
    return this.request(`/blockchain/launchpad/participate/${id}`, {
      method: 'POST',
      body: JSON.stringify(participationData),
    });
  }

  async getCryptoPrices() {
    return this.request('/blockchain/prices');
  }

  async getPortfolio() {
    return this.request('/blockchain/portfolio');
  }

  async getTransactionHistory() {
    return this.request('/blockchain/transactions');
  }

  async swapTokens(swapData: any) {
    return this.request('/blockchain/swap', {
      method: 'POST',
      body: JSON.stringify(swapData),
    });
  }

  async addLiquidity(liquidityData: any) {
    return this.request('/blockchain/liquidity/add', {
      method: 'POST',
      body: JSON.stringify(liquidityData),
    });
  }

  async removeLiquidity(liquidityData: any) {
    return this.request('/blockchain/liquidity/remove', {
      method: 'POST',
      body: JSON.stringify(liquidityData),
    });
  }

  async getYieldFarms() {
    return this.request('/blockchain/yield-farms');
  }

  async joinYieldFarm(id: string, farmData: any) {
    return this.request(`/blockchain/yield-farms/${id}/join`, {
      method: 'POST',
      body: JSON.stringify(farmData),
    });
  }

  async exitYieldFarm(id: string) {
    return this.request(`/blockchain/yield-farms/${id}/exit`, {
      method: 'POST',
    });
  }
}

// Create and export API instance
export const api = new ApiService(API_BASE_URL);
export default api;
