// API Base Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000/api';

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors?: any[];
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('authToken');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication methods
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('authToken', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('authToken');
  }

  // Auth endpoints
  async register(userData: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials: { email: string; password: string }) {
    const response = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response;
  }

  async logout() {
    const response = await this.request('/auth/logout', { method: 'POST' });
    this.clearToken();
    return response;
  }

  async getProfile() {
    return this.request('/auth/profile');
  }

  async updateProfile(profileData: any) {
    return this.request('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) {
    return this.request('/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    });
  }

  // Product endpoints
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/products${queryString}`);
  }

  async getProduct(id: string) {
    return this.request(`/products/${id}`);
  }

  async createProduct(productData: any) {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(id: string, productData: any) {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id: string) {
    return this.request(`/products/${id}`, { method: 'DELETE' });
  }

  async addProductReview(productId: string, reviewData: { rating: number; comment: string }) {
    return this.request(`/products/${productId}/reviews`, {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  }

  // Course endpoints
  async getCourses(params?: {
    page?: number;
    limit?: number;
    category?: string;
    level?: string;
    search?: string;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/courses${queryString}`);
  }

  async getCourse(id: string) {
    return this.request(`/courses/${id}`);
  }

  async createCourse(courseData: any) {
    return this.request('/courses', {
      method: 'POST',
      body: JSON.stringify(courseData),
    });
  }

  async enrollInCourse(courseId: string) {
    return this.request(`/courses/${courseId}/enroll`, { method: 'POST' });
  }

  async addCourseReview(courseId: string, reviewData: { rating: number; comment: string }) {
    return this.request(`/courses/${courseId}/reviews`, {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  }

  // Order endpoints
  async getOrders(params?: { page?: number; limit?: number; status?: string }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/orders${queryString}`);
  }

  async getOrder(id: string) {
    return this.request(`/orders/${id}`);
  }

  async createOrder(orderData: any) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async cancelOrder(id: string) {
    return this.request(`/orders/${id}/cancel`, { method: 'PUT' });
  }

  // NFT endpoints
  async getNFTs(params?: {
    page?: number;
    limit?: number;
    category?: string;
    blockchain?: string;
    isForSale?: boolean;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/nfts${queryString}`);
  }

  async getNFT(id: string) {
    return this.request(`/nfts/${id}`);
  }

  async createNFT(nftData: any) {
    return this.request('/nfts', {
      method: 'POST',
      body: JSON.stringify(nftData),
    });
  }

  async transferNFT(id: string, transferData: { newOwnerId: string; price?: number }) {
    return this.request(`/nfts/${id}/transfer`, {
      method: 'POST',
      body: JSON.stringify(transferData),
    });
  }

  async likeNFT(id: string) {
    return this.request(`/nfts/${id}/like`, { method: 'POST' });
  }

  // Analytics endpoints
  async getDashboard() {
    return this.request('/analytics/dashboard');
  }

  async getSalesAnalytics(period?: string) {
    const queryString = period ? `?period=${period}` : '';
    return this.request(`/analytics/sales${queryString}`);
  }

  async getCourseAnalytics() {
    return this.request('/analytics/courses');
  }

  async getNFTAnalytics() {
    return this.request('/analytics/nfts');
  }

  // User endpoints
  async getUsers(params?: { page?: number; search?: string; role?: string }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/users${queryString}`);
  }

  async getUser(id: string) {
    return this.request(`/users/${id}`);
  }

  async getUserStats(id?: string) {
    const endpoint = id ? `/users/${id}/stats` : '/users/stats';
    return this.request(endpoint);
  }

  async followUser(id: string) {
    return this.request(`/users/${id}/follow`, { method: 'POST' });
  }

  // Notification endpoints
  async getUserNotifications(params?: {
    page?: number;
    limit?: number;
    type?: string;
    category?: string;
    read?: boolean;
    priority?: string;
  }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/notifications${queryString}`);
  }

  async markNotificationAsRead(id: string) {
    return this.request(`/notifications/${id}/read`, { method: 'PUT' });
  }

  async markAllNotificationsAsRead() {
    return this.request('/notifications/read-all', { method: 'PUT' });
  }

  async deleteNotification(id: string) {
    return this.request(`/notifications/${id}`, { method: 'DELETE' });
  }

  // Payment endpoints
  async createPaymentIntent(paymentData: {
    amount: number;
    currency?: string;
    orderId?: string;
    metadata?: any;
  }) {
    return this.request('/payments/create-intent', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }

  async confirmPayment(paymentData: {
    paymentIntentId: string;
    orderId?: string;
  }) {
    return this.request('/payments/confirm', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }

  async getPaymentHistory(params?: { page?: number; limit?: number }) {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    return this.request(`/payments/history${queryString}`);
  }

  async createSubscription(subscriptionData: {
    priceId: string;
    paymentMethodId?: string;
  }) {
    return this.request('/payments/create-subscription', {
      method: 'POST',
      body: JSON.stringify(subscriptionData),
    });
  }

  // File upload
  async uploadFile(file: File, type: 'image' | 'video' | 'document' = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return this.request('/upload/single', {
      method: 'POST',
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
      body: formData,
    });
  }

  async uploadMultipleFiles(files: File[], type: 'image' | 'video' | 'document' = 'image') {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    formData.append('type', type);

    return this.request('/upload/multiple', {
      method: 'POST',
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
      body: formData,
    });
  }
}

// Create and export API instance
export const api = new ApiService(API_BASE_URL);
export default api;
