import React, { useEffect, useState } from 'react';
import { Brain, Atom, Zap, Globe, Sparkles } from 'lucide-react';

interface QuantumLoaderProps {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

const QuantumLoader: React.FC<QuantumLoaderProps> = ({ 
  isLoading, 
  message = "Initializing Quantum Consciousness...", 
  progress = 0 
}) => {
  const [currentProgress, setCurrentProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState(0);

  const loadingStages = [
    { icon: Brain, message: "Activating Neural Networks...", color: "from-purple-500 to-pink-500" },
    { icon: Atom, message: "Calibrating Quantum Processors...", color: "from-blue-500 to-cyan-500" },
    { icon: Zap, message: "Synchronizing Consciousness...", color: "from-green-500 to-emerald-500" },
    { icon: Globe, message: "Connecting to Metaverse...", color: "from-orange-500 to-red-500" },
    { icon: Spark<PERSON>, message: "Reality Synthesis Complete!", color: "from-yellow-500 to-orange-500" }
  ];

  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setCurrentProgress(prev => {
          const newProgress = Math.min(prev + Math.random() * 10, 100);
          const newStage = Math.floor((newProgress / 100) * loadingStages.length);
          setLoadingStage(Math.min(newStage, loadingStages.length - 1));
          return newProgress;
        });
      }, 200);

      return () => clearInterval(interval);
    }
  }, [isLoading]);

  if (!isLoading) return null;

  const currentStageData = loadingStages[loadingStage];
  const IconComponent = currentStageData.icon;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center">
        {/* Quantum Animation */}
        <div className="relative mb-8">
          <div className="w-32 h-32 mx-auto relative">
            {/* Outer Ring */}
            <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full animate-spin"></div>
            
            {/* Middle Ring */}
            <div className="absolute inset-2 border-4 border-blue-500/30 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '2s' }}></div>
            
            {/* Inner Ring */}
            <div className="absolute inset-4 border-4 border-pink-500/30 rounded-full animate-spin" style={{ animationDuration: '1.5s' }}></div>
            
            {/* Center Icon */}
            <div className={`absolute inset-8 bg-gradient-to-r ${currentStageData.color} rounded-full flex items-center justify-center animate-pulse`}>
              <IconComponent className="h-8 w-8 text-white" />
            </div>

            {/* Quantum Particles */}
            {Array.from({ length: 8 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white rounded-full animate-ping"
                style={{
                  top: `${50 + 40 * Math.sin((i * Math.PI * 2) / 8)}%`,
                  left: `${50 + 40 * Math.cos((i * Math.PI * 2) / 8)}%`,
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '2s'
                }}
              />
            ))}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-80 mx-auto mb-6">
          <div className="flex justify-between text-white/70 text-sm mb-2">
            <span>Progress</span>
            <span>{Math.round(currentProgress)}%</span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-3 overflow-hidden">
            <div 
              className={`h-full bg-gradient-to-r ${currentStageData.color} transition-all duration-300 ease-out relative`}
              style={{ width: `${currentProgress}%` }}
            >
              <div className="absolute inset-0 bg-white/30 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Loading Message */}
        <div className="text-white text-xl font-semibold mb-2">
          {currentStageData.message}
        </div>
        <div className="text-white/60 text-sm">
          Quantum consciousness initialization in progress...
        </div>

        {/* Loading Stages Indicator */}
        <div className="flex justify-center space-x-3 mt-8">
          {loadingStages.map((stage, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index <= loadingStage 
                  ? `bg-gradient-to-r ${stage.color}` 
                  : 'bg-white/20'
              }`}
            />
          ))}
        </div>

        {/* Quantum Effects */}
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-purple-400 rounded-full animate-ping"
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuantumLoader;