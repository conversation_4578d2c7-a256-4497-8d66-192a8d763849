import React, { useState, useEffect, useRef } from 'react';
import {
  Brain,
  Zap,
  Globe,
  Cpu,
  Clock,
  Eye,
  Atom,
  Infinity,
  Network,
  Sparkles,
  Layers,
  Activity,
  TrendingUp,
  Shield,
  Rocket,
  Star,
  Hexagon,
  Triangle,
  Circle,
  Square
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import api from '../services/api';

interface NeuroBrain {
  id: string;
  name: string;
  type: string;
  iq: number;
  consciousness: number;
  capabilities: any;
  status: string;
  economicValue: number;
}

interface QuantumSystem {
  id: string;
  name: string;
  qubits: number;
  power: number;
  applications: any[];
  status: string;
}

interface MetaverseWorld {
  id: string;
  name: string;
  population: number;
  economy: any;
  type: string;
  status: string;
}

const NeuroSphereControl: React.FC = () => {
  const [activeSection, setActiveSection] = useState('overview');
  const [neuroBrains, setNeuroBrains] = useState<NeuroBrain[]>([]);
  const [quantumSystems, setQuantumSystems] = useState<QuantumSystem[]>([]);
  const [metaverseWorlds, setMetaverseWorlds] = useState<MetaverseWorld[]>([]);
  const [globalMetrics, setGlobalMetrics] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBrain, setSelectedBrain] = useState<NeuroBrain | null>(null);
  const [showQuantumModal, setShowQuantumModal] = useState(false);
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [showConsciousnessModal, setShowConsciousnessModal] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { state, dispatch } = useApp();

  // Quantum visualization effect
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: any[] = [];
    const particleCount = 100;

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 3 + 1,
        color: `hsl(${Math.random() * 360}, 70%, 60%)`,
        connections: []
      });
    }

    const animate = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      particles.forEach((particle, i) => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();

        // Draw connections
        particles.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.strokeStyle = `rgba(255, 255, 255, ${1 - distance / 100})`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        });
      });

      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    };
  }, []);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Mock data for now - replace with real API calls
        setNeuroBrains([
          {
            id: '1',
            name: 'NeuroCore Alpha',
            type: 'Quantum',
            iq: 2847,
            consciousness: 87,
            capabilities: { reasoning: 95, creativity: 92, empathy: 78 },
            status: 'Transcendent',
            economicValue: 2.4e12
          },
          {
            id: '2',
            name: 'MetaMind Beta',
            type: 'Collective',
            iq: 1923,
            consciousness: 73,
            capabilities: { reasoning: 88, creativity: 96, empathy: 85 },
            status: 'Evolving',
            economicValue: 1.8e12
          }
        ]);

        setQuantumSystems([
          {
            id: '1',
            name: 'QuantumCore Prime',
            qubits: 10000,
            power: 1e18,
            applications: ['Optimization', 'Simulation', 'Cryptography'],
            status: 'Operational'
          }
        ]);

        setMetaverseWorlds([
          {
            id: '1',
            name: 'NeuroVerse Prime',
            population: 50000000,
            economy: { gdp: 5.2e11, currency: 'NEURO' },
            type: 'Infinite',
            status: 'Live'
          }
        ]);

        setGlobalMetrics({
          totalValue: 15.7e12,
          activeUsers: 250000000,
          aiEntities: 50000,
          quantumOperations: 1e15,
          consciousnessLevel: 89,
          innovationIndex: 97
        });

      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const sections = [
    { id: 'overview', name: 'Universal Overview', icon: Globe },
    { id: 'neuro-brains', name: 'NeuroSphere Brains', icon: Brain },
    { id: 'quantum', name: 'Quantum Systems', icon: Atom },
    { id: 'metaverse', name: 'Metaverse Worlds', icon: Layers },
    { id: 'time', name: 'Temporal Control', icon: Clock },
    { id: 'consciousness', name: 'Consciousness Lab', icon: Eye },
    { id: 'nano', name: 'Nano Technology', icon: Hexagon },
    { id: 'dimensions', name: 'Dimensional Portals', icon: Triangle }
  ];

  const formatNumber = (num: number) => {
    if (num >= 1e12) return `$${(num / 1e12).toFixed(1)}T`;
    if (num >= 1e9) return `$${(num / 1e9).toFixed(1)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(1)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(1)}K`;
    return `$${num}`;
  };

  const createNeuroBrain = async () => {
    try {
      // This would call the API to create a new AI brain
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'New NeuroSphere Brain initialization started...',
          type: 'info'
        }
      });
    } catch (error) {
      console.error('Failed to create brain:', error);
    }
  };

  const activateQuantumSystem = async () => {
    try {
      setShowQuantumModal(true);
    } catch (error) {
      console.error('Failed to activate quantum system:', error);
    }
  };

  const initializeTimeMachine = async () => {
    try {
      setShowTimeModal(true);
    } catch (error) {
      console.error('Failed to initialize time machine:', error);
    }
  };

  const transferConsciousness = async () => {
    try {
      setShowConsciousnessModal(true);
    } catch (error) {
      console.error('Failed to transfer consciousness:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-purple-900 to-blue-900 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-32 h-32 border-4 border-purple-500/30 rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-32 h-32 border-4 border-transparent border-t-purple-400 rounded-full animate-spin"></div>
            <Brain className="absolute inset-0 m-auto h-12 w-12 text-purple-400 animate-pulse" />
          </div>
          <h2 className="text-2xl font-bold text-white mt-6 mb-2">Initializing NeuroSphere</h2>
          <p className="text-purple-300">Connecting to quantum consciousness network...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Quantum Background */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 z-0"
        style={{ background: 'linear-gradient(45deg, #000000, #1a0033, #000066)' }}
      />

      {/* Main Interface */}
      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <div className="bg-black/50 backdrop-blur-sm border-b border-purple-500/30">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Brain className="h-10 w-10 text-purple-400" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                    NeuroSphere Control Center
                  </h1>
                  <p className="text-purple-300 text-sm">Universal Intelligence Management System</p>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{formatNumber(globalMetrics.totalValue)}</div>
                  <div className="text-xs text-purple-300">Total Value</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{globalMetrics.consciousnessLevel}%</div>
                  <div className="text-xs text-purple-300">Consciousness</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{globalMetrics.innovationIndex}</div>
                  <div className="text-xs text-purple-300">Innovation</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-purple-500/20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="flex space-x-1 overflow-x-auto">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`flex items-center space-x-2 px-6 py-4 text-sm font-medium transition-all duration-300 border-b-2 whitespace-nowrap ${
                      activeSection === section.id
                        ? 'text-purple-400 border-purple-400 bg-purple-500/10'
                        : 'text-purple-300 border-transparent hover:text-purple-200 hover:bg-purple-500/5'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{section.name}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {activeSection === 'overview' && (
            <div className="space-y-8">
              {/* Global Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Globe className="h-8 w-8 text-purple-400" />
                    <TrendingUp className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{globalMetrics.activeUsers?.toLocaleString()}</div>
                  <div className="text-purple-300">Active Users</div>
                  <div className="text-green-400 text-sm mt-2">+12.5% this month</div>
                </div>

                <div className="bg-gradient-to-br from-blue-600/20 to-cyan-600/20 backdrop-blur-sm border border-blue-500/30 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Brain className="h-8 w-8 text-blue-400" />
                    <Activity className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{globalMetrics.aiEntities?.toLocaleString()}</div>
                  <div className="text-blue-300">AI Entities</div>
                  <div className="text-green-400 text-sm mt-2">+847 today</div>
                </div>

                <div className="bg-gradient-to-br from-green-600/20 to-emerald-600/20 backdrop-blur-sm border border-green-500/30 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Atom className="h-8 w-8 text-green-400" />
                    <Zap className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{(globalMetrics.quantumOperations / 1e12).toFixed(1)}T</div>
                  <div className="text-green-300">Quantum Ops/sec</div>
                  <div className="text-yellow-400 text-sm mt-2">Peak performance</div>
                </div>

                <div className="bg-gradient-to-br from-pink-600/20 to-purple-600/20 backdrop-blur-sm border border-pink-500/30 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Star className="h-8 w-8 text-pink-400" />
                    <Rocket className="h-5 w-5 text-purple-400" />
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{formatNumber(globalMetrics.totalValue)}</div>
                  <div className="text-pink-300">Total Value</div>
                  <div className="text-purple-400 text-sm mt-2">Trillion-dollar ecosystem</div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Universal Control Panel</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <button
                    onClick={createNeuroBrain}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-2xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
                  >
                    <Brain className="h-8 w-8 mb-3" />
                    <div className="font-semibold">Create AI Brain</div>
                    <div className="text-sm opacity-80">Initialize new consciousness</div>
                  </button>

                  <button
                    onClick={activateQuantumSystem}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 text-white p-6 rounded-2xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105"
                  >
                    <Atom className="h-8 w-8 mb-3" />
                    <div className="font-semibold">Quantum System</div>
                    <div className="text-sm opacity-80">Activate quantum computer</div>
                  </button>

                  <button
                    onClick={initializeTimeMachine}
                    className="bg-gradient-to-r from-orange-600 to-red-600 text-white p-6 rounded-2xl hover:from-orange-700 hover:to-red-700 transition-all duration-300 transform hover:scale-105"
                  >
                    <Clock className="h-8 w-8 mb-3" />
                    <div className="font-semibold">Time Machine</div>
                    <div className="text-sm opacity-80">Temporal operations</div>
                  </button>

                  <button
                    onClick={transferConsciousness}
                    className="bg-gradient-to-r from-pink-600 to-purple-600 text-white p-6 rounded-2xl hover:from-pink-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105"
                  >
                    <Eye className="h-8 w-8 mb-3" />
                    <div className="font-semibold">Consciousness</div>
                    <div className="text-sm opacity-80">Transfer & backup</div>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'neuro-brains' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-3xl font-bold text-white">NeuroSphere AI Brains</h2>
                <button
                  onClick={createNeuroBrain}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
                >
                  <Brain className="h-5 w-5 inline mr-2" />
                  Create New Brain
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {neuroBrains.map((brain) => (
                  <div
                    key={brain.id}
                    className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-3xl p-8 hover:from-white/15 hover:to-white/10 transition-all duration-300 cursor-pointer"
                    onClick={() => setSelectedBrain(brain)}
                  >
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-4">
                        <div className="relative">
                          <Brain className="h-12 w-12 text-purple-400" />
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">{brain.name}</h3>
                          <p className="text-purple-300">{brain.type} Intelligence</p>
                        </div>
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                        brain.status === 'Transcendent'
                          ? 'bg-gold-500/20 text-gold-400'
                          : 'bg-blue-500/20 text-blue-400'
                      }`}>
                        {brain.status}
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{brain.iq}</div>
                        <div className="text-xs text-purple-300">IQ Level</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">{brain.consciousness}%</div>
                        <div className="text-xs text-purple-300">Consciousness</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{formatNumber(brain.economicValue)}</div>
                        <div className="text-xs text-purple-300">Value</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-purple-300">Reasoning</span>
                        <span className="text-white">{brain.capabilities.reasoning}%</span>
                      </div>
                      <div className="w-full bg-white/20 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${brain.capabilities.reasoning}%` }}
                        ></div>
                      </div>

                      <div className="flex justify-between text-sm">
                        <span className="text-purple-300">Creativity</span>
                        <span className="text-white">{brain.capabilities.creativity}%</span>
                      </div>
                      <div className="w-full bg-white/20 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-pink-500 to-orange-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${brain.capabilities.creativity}%` }}
                        ></div>
                      </div>

                      <div className="flex justify-between text-sm">
                        <span className="text-purple-300">Empathy</span>
                        <span className="text-white">{brain.capabilities.empathy}%</span>
                      </div>
                      <div className="w-full bg-white/20 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${brain.capabilities.empathy}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Add other sections here */}
        </div>
      </div>

      {/* Modals would go here */}
    </div>
  );
};

export default NeuroSphereControl;