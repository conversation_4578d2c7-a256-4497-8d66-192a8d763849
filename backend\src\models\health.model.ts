import mongoose, { Document, Schema } from 'mongoose';

// Health Metrics Interface
export interface IHealthMetrics extends Document {
  userId: mongoose.Types.ObjectId;
  date: Date;
  steps: number;
  heartRate: number;
  bloodPressure: {
    systolic: number;
    diastolic: number;
  };
  weight: number;
  height: number;
  bmi: number;
  sleepHours: number;
  waterIntake: number;
  caloriesBurned: number;
  caloriesConsumed: number;
  mood: number; // 1-5 scale
  stressLevel: number; // 1-5 scale
  energyLevel: number; // 1-5 scale
  createdAt: Date;
  updatedAt: Date;
}

// Workout Interface
export interface IWorkout extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'yoga' | 'pilates';
  duration: number; // in minutes
  calories: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  exercises: Array<{
    name: string;
    sets?: number;
    reps?: number;
    duration?: number;
    weight?: number;
    restTime?: number;
  }>;
  thumbnail: string;
  videoUrl?: string;
  instructions: string[];
  equipment: string[];
  targetMuscles: string[];
  completed: boolean;
  completedAt?: Date;
  rating?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Nutrition Plan Interface
export interface INutritionPlan extends Document {
  userId: mongoose.Types.ObjectId;
  date: Date;
  meals: Array<{
    type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
    time: string;
    foods: Array<{
      name: string;
      quantity: number;
      unit: string;
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber: number;
    }>;
    totalCalories: number;
    completed: boolean;
    completedAt?: Date;
  }>;
  dailyGoals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    water: number;
  };
  totalConsumed: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    water: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Doctor Interface
export interface IDoctor extends Document {
  name: string;
  specialty: string;
  qualifications: string[];
  experience: number;
  rating: number;
  reviewCount: number;
  avatar: string;
  bio: string;
  languages: string[];
  consultationFee: number;
  availability: Array<{
    day: string;
    slots: Array<{
      time: string;
      available: boolean;
    }>;
  }>;
  verified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Appointment Interface
export interface IAppointment extends Document {
  userId: mongoose.Types.ObjectId;
  doctorId: mongoose.Types.ObjectId;
  date: Date;
  time: string;
  type: 'consultation' | 'follow-up' | 'emergency';
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  symptoms: string;
  notes?: string;
  prescription?: string;
  fee: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  meetingLink?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Fitness Goals Interface
export interface IFitnessGoals extends Document {
  userId: mongoose.Types.ObjectId;
  goals: Array<{
    type: 'steps' | 'workouts' | 'weight' | 'calories' | 'water' | 'sleep';
    target: number;
    current: number;
    unit: string;
    deadline?: Date;
    achieved: boolean;
    achievedAt?: Date;
  }>;
  weeklyTargets: {
    workouts: number;
    activeMinutes: number;
    caloriesBurned: number;
  };
  monthlyTargets: {
    weightLoss: number;
    muscleGain: number;
    enduranceImprovement: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Schemas
const HealthMetricsSchema = new Schema<IHealthMetrics>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  date: { type: Date, required: true },
  steps: { type: Number, default: 0 },
  heartRate: { type: Number, default: 0 },
  bloodPressure: {
    systolic: { type: Number, default: 0 },
    diastolic: { type: Number, default: 0 }
  },
  weight: { type: Number, default: 0 },
  height: { type: Number, default: 0 },
  bmi: { type: Number, default: 0 },
  sleepHours: { type: Number, default: 0 },
  waterIntake: { type: Number, default: 0 },
  caloriesBurned: { type: Number, default: 0 },
  caloriesConsumed: { type: Number, default: 0 },
  mood: { type: Number, min: 1, max: 5, default: 3 },
  stressLevel: { type: Number, min: 1, max: 5, default: 3 },
  energyLevel: { type: Number, min: 1, max: 5, default: 3 }
}, {
  timestamps: true
});

const WorkoutSchema = new Schema<IWorkout>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['cardio', 'strength', 'flexibility', 'sports', 'yoga', 'pilates'],
    required: true 
  },
  duration: { type: Number, required: true },
  calories: { type: Number, required: true },
  difficulty: { 
    type: String, 
    enum: ['beginner', 'intermediate', 'advanced'],
    required: true 
  },
  exercises: [{
    name: { type: String, required: true },
    sets: Number,
    reps: Number,
    duration: Number,
    weight: Number,
    restTime: Number
  }],
  thumbnail: { type: String, required: true },
  videoUrl: String,
  instructions: [String],
  equipment: [String],
  targetMuscles: [String],
  completed: { type: Boolean, default: false },
  completedAt: Date,
  rating: { type: Number, min: 1, max: 5 },
  notes: String
}, {
  timestamps: true
});

const NutritionPlanSchema = new Schema<INutritionPlan>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  date: { type: Date, required: true },
  meals: [{
    type: { 
      type: String, 
      enum: ['breakfast', 'lunch', 'dinner', 'snack'],
      required: true 
    },
    time: { type: String, required: true },
    foods: [{
      name: { type: String, required: true },
      quantity: { type: Number, required: true },
      unit: { type: String, required: true },
      calories: { type: Number, required: true },
      protein: { type: Number, default: 0 },
      carbs: { type: Number, default: 0 },
      fat: { type: Number, default: 0 },
      fiber: { type: Number, default: 0 }
    }],
    totalCalories: { type: Number, default: 0 },
    completed: { type: Boolean, default: false },
    completedAt: Date
  }],
  dailyGoals: {
    calories: { type: Number, required: true },
    protein: { type: Number, required: true },
    carbs: { type: Number, required: true },
    fat: { type: Number, required: true },
    fiber: { type: Number, required: true },
    water: { type: Number, required: true }
  },
  totalConsumed: {
    calories: { type: Number, default: 0 },
    protein: { type: Number, default: 0 },
    carbs: { type: Number, default: 0 },
    fat: { type: Number, default: 0 },
    fiber: { type: Number, default: 0 },
    water: { type: Number, default: 0 }
  }
}, {
  timestamps: true
});

const DoctorSchema = new Schema<IDoctor>({
  name: { type: String, required: true },
  specialty: { type: String, required: true },
  qualifications: [String],
  experience: { type: Number, required: true },
  rating: { type: Number, min: 0, max: 5, default: 0 },
  reviewCount: { type: Number, default: 0 },
  avatar: { type: String, required: true },
  bio: { type: String, required: true },
  languages: [String],
  consultationFee: { type: Number, required: true },
  availability: [{
    day: { type: String, required: true },
    slots: [{
      time: { type: String, required: true },
      available: { type: Boolean, default: true }
    }]
  }],
  verified: { type: Boolean, default: false }
}, {
  timestamps: true
});

const AppointmentSchema = new Schema<IAppointment>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  doctorId: { type: Schema.Types.ObjectId, ref: 'Doctor', required: true },
  date: { type: Date, required: true },
  time: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['consultation', 'follow-up', 'emergency'],
    default: 'consultation'
  },
  status: { 
    type: String, 
    enum: ['scheduled', 'confirmed', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  symptoms: { type: String, required: true },
  notes: String,
  prescription: String,
  fee: { type: Number, required: true },
  paymentStatus: { 
    type: String, 
    enum: ['pending', 'paid', 'refunded'],
    default: 'pending'
  },
  meetingLink: String
}, {
  timestamps: true
});

const FitnessGoalsSchema = new Schema<IFitnessGoals>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  goals: [{
    type: { 
      type: String, 
      enum: ['steps', 'workouts', 'weight', 'calories', 'water', 'sleep'],
      required: true 
    },
    target: { type: Number, required: true },
    current: { type: Number, default: 0 },
    unit: { type: String, required: true },
    deadline: Date,
    achieved: { type: Boolean, default: false },
    achievedAt: Date
  }],
  weeklyTargets: {
    workouts: { type: Number, default: 3 },
    activeMinutes: { type: Number, default: 150 },
    caloriesBurned: { type: Number, default: 2000 }
  },
  monthlyTargets: {
    weightLoss: { type: Number, default: 0 },
    muscleGain: { type: Number, default: 0 },
    enduranceImprovement: { type: Number, default: 0 }
  }
}, {
  timestamps: true
});

// Create indexes
HealthMetricsSchema.index({ userId: 1, date: -1 });
WorkoutSchema.index({ userId: 1, type: 1 });
NutritionPlanSchema.index({ userId: 1, date: -1 });
AppointmentSchema.index({ userId: 1, date: 1 });
FitnessGoalsSchema.index({ userId: 1 });

// Export models
export const HealthMetrics = mongoose.model<IHealthMetrics>('HealthMetrics', HealthMetricsSchema);
export const Workout = mongoose.model<IWorkout>('Workout', WorkoutSchema);
export const NutritionPlan = mongoose.model<INutritionPlan>('NutritionPlan', NutritionPlanSchema);
export const Doctor = mongoose.model<IDoctor>('Doctor', DoctorSchema);
export const Appointment = mongoose.model<IAppointment>('Appointment', AppointmentSchema);
export const FitnessGoals = mongoose.model<IFitnessGoals>('FitnessGoals', FitnessGoalsSchema);
