import { Request, Response } from 'express';
import { 
  HealthMetrics, 
  Workout, 
  NutritionPlan, 
  Doctor, 
  Appointment, 
  FitnessGoals 
} from '../models/health.model';
import { AuthRequest } from '../middleware/auth.middleware';

// Health Dashboard
export const getHealthDashboard = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get today's health metrics
    const todayMetrics = await HealthMetrics.findOne({
      userId,
      date: { $gte: today }
    });

    // Get fitness goals
    const fitnessGoals = await FitnessGoals.findOne({ userId });

    // Get recent workouts
    const recentWorkouts = await Workout.find({ userId })
      .sort({ createdAt: -1 })
      .limit(5);

    // Get today's nutrition plan
    const todayNutrition = await NutritionPlan.findOne({
      userId,
      date: { $gte: today }
    });

    // Get upcoming appointments
    const upcomingAppointments = await Appointment.find({
      userId,
      date: { $gte: new Date() },
      status: { $in: ['scheduled', 'confirmed'] }
    })
    .populate('doctorId')
    .sort({ date: 1 })
    .limit(3);

    // Calculate weekly stats
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    const weeklyMetrics = await HealthMetrics.find({
      userId,
      date: { $gte: weekAgo }
    });

    const weeklyStats = {
      averageSteps: weeklyMetrics.reduce((sum, m) => sum + m.steps, 0) / weeklyMetrics.length || 0,
      averageSleep: weeklyMetrics.reduce((sum, m) => sum + m.sleepHours, 0) / weeklyMetrics.length || 0,
      totalWorkouts: recentWorkouts.filter(w => w.completedAt && w.completedAt >= weekAgo).length,
      averageMood: weeklyMetrics.reduce((sum, m) => sum + m.mood, 0) / weeklyMetrics.length || 3
    };

    res.json({
      success: true,
      data: {
        todayMetrics,
        fitnessGoals,
        recentWorkouts,
        todayNutrition,
        upcomingAppointments,
        weeklyStats
      }
    });
  } catch (error) {
    console.error('Health dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch health dashboard'
    });
  }
};

// Update Health Metrics
export const updateHealthMetrics = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { date, ...metrics } = req.body;
    
    const targetDate = date ? new Date(date) : new Date();
    targetDate.setHours(0, 0, 0, 0);

    // Calculate BMI if height and weight are provided
    if (metrics.height && metrics.weight) {
      const heightInMeters = metrics.height / 100;
      metrics.bmi = metrics.weight / (heightInMeters * heightInMeters);
    }

    const healthMetrics = await HealthMetrics.findOneAndUpdate(
      { userId, date: targetDate },
      { ...metrics, userId, date: targetDate },
      { upsert: true, new: true }
    );

    res.json({
      success: true,
      data: healthMetrics,
      message: 'Health metrics updated successfully'
    });
  } catch (error) {
    console.error('Update health metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update health metrics'
    });
  }
};

// Get Workouts
export const getWorkouts = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { type, difficulty, page = 1, limit = 10 } = req.query;

    const filter: any = { userId };
    if (type) filter.type = type;
    if (difficulty) filter.difficulty = difficulty;

    const workouts = await Workout.find(filter)
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await Workout.countDocuments(filter);

    res.json({
      success: true,
      data: {
        workouts,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get workouts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch workouts'
    });
  }
};

// Create Workout
export const createWorkout = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const workoutData = { ...req.body, userId };

    const workout = new Workout(workoutData);
    await workout.save();

    res.status(201).json({
      success: true,
      data: workout,
      message: 'Workout created successfully'
    });
  } catch (error) {
    console.error('Create workout error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create workout'
    });
  }
};

// Complete Workout
export const completeWorkout = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;
    const { rating, notes } = req.body;

    const workout = await Workout.findOneAndUpdate(
      { _id: id, userId },
      { 
        completed: true, 
        completedAt: new Date(),
        rating,
        notes
      },
      { new: true }
    );

    if (!workout) {
      return res.status(404).json({
        success: false,
        message: 'Workout not found'
      });
    }

    // Update today's health metrics with calories burned
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    await HealthMetrics.findOneAndUpdate(
      { userId, date: today },
      { 
        $inc: { caloriesBurned: workout.calories },
        userId,
        date: today
      },
      { upsert: true }
    );

    res.json({
      success: true,
      data: workout,
      message: 'Workout completed successfully'
    });
  } catch (error) {
    console.error('Complete workout error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete workout'
    });
  }
};

// Get Nutrition Plan
export const getNutritionPlan = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { date } = req.query;
    
    const targetDate = date ? new Date(date as string) : new Date();
    targetDate.setHours(0, 0, 0, 0);

    let nutritionPlan = await NutritionPlan.findOne({
      userId,
      date: targetDate
    });

    // Create default plan if none exists
    if (!nutritionPlan) {
      nutritionPlan = new NutritionPlan({
        userId,
        date: targetDate,
        meals: [
          {
            type: 'breakfast',
            time: '08:00',
            foods: [],
            totalCalories: 0,
            completed: false
          },
          {
            type: 'lunch',
            time: '12:30',
            foods: [],
            totalCalories: 0,
            completed: false
          },
          {
            type: 'dinner',
            time: '19:00',
            foods: [],
            totalCalories: 0,
            completed: false
          },
          {
            type: 'snack',
            time: '15:00',
            foods: [],
            totalCalories: 0,
            completed: false
          }
        ],
        dailyGoals: {
          calories: 2000,
          protein: 150,
          carbs: 200,
          fat: 65,
          fiber: 25,
          water: 8
        },
        totalConsumed: {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          water: 0
        }
      });
      await nutritionPlan.save();
    }

    res.json({
      success: true,
      data: nutritionPlan
    });
  } catch (error) {
    console.error('Get nutrition plan error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch nutrition plan'
    });
  }
};

// Update Nutrition Plan
export const updateNutritionPlan = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { date, dailyGoals } = req.body;
    
    const targetDate = date ? new Date(date) : new Date();
    targetDate.setHours(0, 0, 0, 0);

    const nutritionPlan = await NutritionPlan.findOneAndUpdate(
      { userId, date: targetDate },
      { dailyGoals },
      { new: true }
    );

    if (!nutritionPlan) {
      return res.status(404).json({
        success: false,
        message: 'Nutrition plan not found'
      });
    }

    res.json({
      success: true,
      data: nutritionPlan,
      message: 'Nutrition plan updated successfully'
    });
  } catch (error) {
    console.error('Update nutrition plan error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update nutrition plan'
    });
  }
};

// Log Meal
export const logMeal = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { date, mealType, foods } = req.body;
    
    const targetDate = date ? new Date(date) : new Date();
    targetDate.setHours(0, 0, 0, 0);

    const nutritionPlan = await NutritionPlan.findOne({
      userId,
      date: targetDate
    });

    if (!nutritionPlan) {
      return res.status(404).json({
        success: false,
        message: 'Nutrition plan not found'
      });
    }

    // Find and update the meal
    const mealIndex = nutritionPlan.meals.findIndex(meal => meal.type === mealType);
    if (mealIndex === -1) {
      return res.status(400).json({
        success: false,
        message: 'Invalid meal type'
      });
    }

    // Calculate total calories for the meal
    const totalCalories = foods.reduce((sum: number, food: any) => sum + food.calories, 0);

    nutritionPlan.meals[mealIndex].foods = foods;
    nutritionPlan.meals[mealIndex].totalCalories = totalCalories;
    nutritionPlan.meals[mealIndex].completed = true;
    nutritionPlan.meals[mealIndex].completedAt = new Date();

    // Recalculate total consumed
    nutritionPlan.totalConsumed = nutritionPlan.meals.reduce((total, meal) => {
      meal.foods.forEach(food => {
        total.calories += food.calories;
        total.protein += food.protein;
        total.carbs += food.carbs;
        total.fat += food.fat;
        total.fiber += food.fiber;
      });
      return total;
    }, {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
      water: nutritionPlan.totalConsumed.water
    });

    await nutritionPlan.save();

    res.json({
      success: true,
      data: nutritionPlan,
      message: 'Meal logged successfully'
    });
  } catch (error) {
    console.error('Log meal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to log meal'
    });
  }
};

// Get Mental Health Data
export const getMentalHealthData = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { days = 30 } = req.query;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - Number(days));

    const mentalHealthData = await HealthMetrics.find({
      userId,
      date: { $gte: startDate }
    })
    .select('date mood stressLevel energyLevel')
    .sort({ date: 1 });

    const averages = {
      mood: mentalHealthData.reduce((sum, data) => sum + data.mood, 0) / mentalHealthData.length || 3,
      stress: mentalHealthData.reduce((sum, data) => sum + data.stressLevel, 0) / mentalHealthData.length || 3,
      energy: mentalHealthData.reduce((sum, data) => sum + data.energyLevel, 0) / mentalHealthData.length || 3
    };

    res.json({
      success: true,
      data: {
        dailyData: mentalHealthData,
        averages,
        trends: {
          moodTrend: calculateTrend(mentalHealthData.map(d => d.mood)),
          stressTrend: calculateTrend(mentalHealthData.map(d => d.stressLevel)),
          energyTrend: calculateTrend(mentalHealthData.map(d => d.energyLevel))
        }
      }
    });
  } catch (error) {
    console.error('Get mental health data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch mental health data'
    });
  }
};

// Update Mood Tracking
export const updateMoodTracking = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { mood, stressLevel, energyLevel, notes } = req.body;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const healthMetrics = await HealthMetrics.findOneAndUpdate(
      { userId, date: today },
      { 
        mood, 
        stressLevel, 
        energyLevel,
        userId,
        date: today
      },
      { upsert: true, new: true }
    );

    res.json({
      success: true,
      data: healthMetrics,
      message: 'Mood tracking updated successfully'
    });
  } catch (error) {
    console.error('Update mood tracking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update mood tracking'
    });
  }
};

// Get Doctors
export const getDoctors = async (req: Request, res: Response) => {
  try {
    const { specialty, page = 1, limit = 10, search } = req.query;

    const filter: any = { verified: true };
    if (specialty) filter.specialty = specialty;
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { specialty: { $regex: search, $options: 'i' } }
      ];
    }

    const doctors = await Doctor.find(filter)
      .sort({ rating: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await Doctor.countDocuments(filter);

    res.json({
      success: true,
      data: {
        doctors,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get doctors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch doctors'
    });
  }
};

// Book Appointment
export const bookAppointment = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { doctorId, date, time, type, symptoms } = req.body;

    // Check if doctor exists
    const doctor = await Doctor.findById(doctorId);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found'
      });
    }

    // Check if slot is available (simplified check)
    const existingAppointment = await Appointment.findOne({
      doctorId,
      date: new Date(date),
      time,
      status: { $in: ['scheduled', 'confirmed'] }
    });

    if (existingAppointment) {
      return res.status(400).json({
        success: false,
        message: 'Time slot is not available'
      });
    }

    const appointment = new Appointment({
      userId,
      doctorId,
      date: new Date(date),
      time,
      type: type || 'consultation',
      symptoms,
      fee: doctor.consultationFee,
      status: 'scheduled'
    });

    await appointment.save();
    await appointment.populate('doctorId');

    res.status(201).json({
      success: true,
      data: appointment,
      message: 'Appointment booked successfully'
    });
  } catch (error) {
    console.error('Book appointment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to book appointment'
    });
  }
};

// Get Appointments
export const getAppointments = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { status, page = 1, limit = 10 } = req.query;

    const filter: any = { userId };
    if (status) filter.status = status;

    const appointments = await Appointment.find(filter)
      .populate('doctorId')
      .sort({ date: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await Appointment.countDocuments(filter);

    res.json({
      success: true,
      data: {
        appointments,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointments'
    });
  }
};

// Cancel Appointment
export const cancelAppointment = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    const appointment = await Appointment.findOneAndUpdate(
      { _id: id, userId, status: { $in: ['scheduled', 'confirmed'] } },
      { status: 'cancelled' },
      { new: true }
    ).populate('doctorId');

    if (!appointment) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found or cannot be cancelled'
      });
    }

    res.json({
      success: true,
      data: appointment,
      message: 'Appointment cancelled successfully'
    });
  } catch (error) {
    console.error('Cancel appointment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel appointment'
    });
  }
};

// Get Health Insights
export const getHealthInsights = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    
    // Get last 30 days of data
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const healthData = await HealthMetrics.find({
      userId,
      date: { $gte: thirtyDaysAgo }
    }).sort({ date: 1 });

    const workouts = await Workout.find({
      userId,
      completedAt: { $gte: thirtyDaysAgo }
    });

    // Generate insights
    const insights = {
      stepsTrend: calculateTrend(healthData.map(d => d.steps)),
      sleepQuality: calculateAverage(healthData.map(d => d.sleepHours)),
      workoutFrequency: workouts.length,
      moodPattern: calculateAverage(healthData.map(d => d.mood)),
      recommendations: generateHealthRecommendations(healthData, workouts)
    };

    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    console.error('Get health insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch health insights'
    });
  }
};

// Update Fitness Goals
export const updateFitnessGoals = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { goals, weeklyTargets, monthlyTargets } = req.body;

    const fitnessGoals = await FitnessGoals.findOneAndUpdate(
      { userId },
      { goals, weeklyTargets, monthlyTargets },
      { upsert: true, new: true }
    );

    res.json({
      success: true,
      data: fitnessGoals,
      message: 'Fitness goals updated successfully'
    });
  } catch (error) {
    console.error('Update fitness goals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update fitness goals'
    });
  }
};

// Get Fitness Goals
export const getFitnessGoals = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    let fitnessGoals = await FitnessGoals.findOne({ userId });

    // Create default goals if none exist
    if (!fitnessGoals) {
      fitnessGoals = new FitnessGoals({
        userId,
        goals: [
          { type: 'steps', target: 10000, current: 0, unit: 'steps' },
          { type: 'workouts', target: 5, current: 0, unit: 'sessions' },
          { type: 'water', target: 8, current: 0, unit: 'glasses' },
          { type: 'sleep', target: 8, current: 0, unit: 'hours' }
        ],
        weeklyTargets: {
          workouts: 3,
          activeMinutes: 150,
          caloriesBurned: 2000
        },
        monthlyTargets: {
          weightLoss: 0,
          muscleGain: 0,
          enduranceImprovement: 0
        }
      });
      await fitnessGoals.save();
    }

    res.json({
      success: true,
      data: fitnessGoals
    });
  } catch (error) {
    console.error('Get fitness goals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch fitness goals'
    });
  }
};

// Helper functions
const calculateTrend = (values: number[]): 'up' | 'down' | 'stable' => {
  if (values.length < 2) return 'stable';
  const first = values.slice(0, Math.floor(values.length / 2));
  const second = values.slice(Math.floor(values.length / 2));
  const firstAvg = first.reduce((sum, val) => sum + val, 0) / first.length;
  const secondAvg = second.reduce((sum, val) => sum + val, 0) / second.length;
  
  if (secondAvg > firstAvg * 1.05) return 'up';
  if (secondAvg < firstAvg * 0.95) return 'down';
  return 'stable';
};

const calculateAverage = (values: number[]): number => {
  return values.reduce((sum, val) => sum + val, 0) / values.length || 0;
};

const generateHealthRecommendations = (healthData: any[], workouts: any[]): string[] => {
  const recommendations = [];
  
  const avgSteps = calculateAverage(healthData.map(d => d.steps));
  if (avgSteps < 8000) {
    recommendations.push('Try to increase your daily steps to at least 8,000');
  }
  
  const avgSleep = calculateAverage(healthData.map(d => d.sleepHours));
  if (avgSleep < 7) {
    recommendations.push('Aim for 7-9 hours of sleep per night for better health');
  }
  
  if (workouts.length < 3) {
    recommendations.push('Try to exercise at least 3 times per week');
  }
  
  return recommendations;
};
