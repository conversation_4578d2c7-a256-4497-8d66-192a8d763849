import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';

// Mock blockchain data structures
interface WalletInfo {
  address: string;
  balance: { [token: string]: number };
  network: string;
  connected: boolean;
}

interface DeFiProtocol {
  id: string;
  name: string;
  tvl: string;
  apy: string;
  risk: 'Low' | 'Medium' | 'High';
  description: string;
  category: string;
}

interface StakingPosition {
  id: string;
  protocol: string;
  amount: number;
  token: string;
  apy: number;
  rewards: number;
  startDate: Date;
}

// In-memory storage (replace with database in production)
const wallets = new Map<string, WalletInfo>();
const stakingPositions = new Map<string, StakingPosition[]>();
const cryptoPrices = {
  NEURO: 847.23,
  BTC: 67234.56,
  ETH: 3456.78,
  SOL: 234.56,
  MATIC: 0.89,
  USDC: 1.00,
  USDT: 1.00
};

const mockProtocols: DeFiProtocol[] = [
  {
    id: '1',
    name: 'NeuroStake',
    tvl: '$2.4B',
    apy: '12.5%',
    risk: 'Low',
    description: 'Secure staking protocol for NEURO tokens',
    category: 'Staking'
  },
  {
    id: '2',
    name: 'EthYield',
    tvl: '$1.8B',
    apy: '8.3%',
    risk: 'Medium',
    description: 'Ethereum-based yield farming protocol',
    category: 'Yield Farming'
  },
  {
    id: '3',
    name: 'LiquidSwap',
    tvl: '$950M',
    apy: '15.7%',
    risk: 'High',
    description: 'High-yield liquidity provision protocol',
    category: 'Liquidity'
  }
];

// Get Wallet Info
export const getWalletInfo = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const wallet = wallets.get(userId || '');

    if (!wallet) {
      return res.json({
        success: true,
        data: {
          connected: false,
          address: null,
          balance: {},
          network: null
        }
      });
    }

    res.json({
      success: true,
      data: wallet
    });
  } catch (error) {
    console.error('Get wallet info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch wallet information'
    });
  }
};

// Connect Wallet
export const connectWallet = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { walletType, address, network = 'ethereum' } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Simulate wallet connection
    const mockAddress = address || `0x${Math.random().toString(16).substr(2, 40)}`;
    const wallet: WalletInfo = {
      address: mockAddress,
      balance: {
        ETH: 2.5,
        NEURO: 1000,
        USDC: 500,
        BTC: 0.1
      },
      network,
      connected: true
    };

    wallets.set(userId, wallet);

    res.json({
      success: true,
      data: wallet,
      message: `${walletType} wallet connected successfully`
    });
  } catch (error) {
    console.error('Connect wallet error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to connect wallet'
    });
  }
};

// Disconnect Wallet
export const disconnectWallet = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    if (userId) {
      wallets.delete(userId);
    }

    res.json({
      success: true,
      message: 'Wallet disconnected successfully'
    });
  } catch (error) {
    console.error('Disconnect wallet error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to disconnect wallet'
    });
  }
};

// Get DeFi Protocols
export const getDeFiProtocols = async (req: Request, res: Response) => {
  try {
    const { category, sortBy = 'tvl' } = req.query;

    let protocols = [...mockProtocols];

    if (category) {
      protocols = protocols.filter(p => p.category.toLowerCase() === (category as string).toLowerCase());
    }

    // Simple sorting
    if (sortBy === 'apy') {
      protocols.sort((a, b) => parseFloat(b.apy) - parseFloat(a.apy));
    }

    res.json({
      success: true,
      data: protocols
    });
  } catch (error) {
    console.error('Get DeFi protocols error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch DeFi protocols'
    });
  }
};

// Get Protocol Details
export const getProtocolDetails = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const protocol = mockProtocols.find(p => p.id === id);

    if (!protocol) {
      return res.status(404).json({
        success: false,
        message: 'Protocol not found'
      });
    }

    const details = {
      ...protocol,
      totalStakers: Math.floor(Math.random() * 10000) + 1000,
      minStake: 100,
      lockPeriod: '30 days',
      fees: '0.5%',
      supportedTokens: ['NEURO', 'ETH', 'USDC'],
      rewardsDistribution: 'Daily'
    };

    res.json({
      success: true,
      data: details
    });
  } catch (error) {
    console.error('Get protocol details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch protocol details'
    });
  }
};

// Stake Tokens
export const stakeTokens = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { protocolId, amount, token } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const wallet = wallets.get(userId);
    if (!wallet || !wallet.connected) {
      return res.status(400).json({
        success: false,
        message: 'Wallet not connected'
      });
    }

    if (wallet.balance[token] < amount) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    const protocol = mockProtocols.find(p => p.id === protocolId);
    if (!protocol) {
      return res.status(404).json({
        success: false,
        message: 'Protocol not found'
      });
    }

    // Create staking position
    const position: StakingPosition = {
      id: `stake_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      protocol: protocol.name,
      amount,
      token,
      apy: parseFloat(protocol.apy),
      rewards: 0,
      startDate: new Date()
    };

    const userPositions = stakingPositions.get(userId) || [];
    userPositions.push(position);
    stakingPositions.set(userId, userPositions);

    // Update wallet balance
    wallet.balance[token] -= amount;
    wallets.set(userId, wallet);

    res.json({
      success: true,
      data: {
        position,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`,
        newBalance: wallet.balance[token]
      },
      message: 'Tokens staked successfully'
    });
  } catch (error) {
    console.error('Stake tokens error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stake tokens'
    });
  }
};

// Unstake Tokens
export const unstakeTokens = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { positionId } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const userPositions = stakingPositions.get(userId) || [];
    const positionIndex = userPositions.findIndex(p => p.id === positionId);

    if (positionIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Staking position not found'
      });
    }

    const position = userPositions[positionIndex];
    const wallet = wallets.get(userId);

    if (wallet) {
      // Return staked amount plus rewards
      const rewards = calculateRewards(position);
      wallet.balance[position.token] += position.amount + rewards;
      wallets.set(userId, wallet);
    }

    // Remove position
    userPositions.splice(positionIndex, 1);
    stakingPositions.set(userId, userPositions);

    res.json({
      success: true,
      data: {
        unstakedAmount: position.amount,
        rewards: calculateRewards(position),
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Tokens unstaked successfully'
    });
  } catch (error) {
    console.error('Unstake tokens error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unstake tokens'
    });
  }
};

// Claim Rewards
export const claimRewards = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const userPositions = stakingPositions.get(userId) || [];
    let totalRewards = 0;

    userPositions.forEach(position => {
      const rewards = calculateRewards(position);
      totalRewards += rewards;
      position.rewards = 0; // Reset rewards after claiming
    });

    stakingPositions.set(userId, userPositions);

    const wallet = wallets.get(userId);
    if (wallet) {
      wallet.balance.NEURO = (wallet.balance.NEURO || 0) + totalRewards;
      wallets.set(userId, wallet);
    }

    res.json({
      success: true,
      data: {
        totalRewards,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Rewards claimed successfully'
    });
  } catch (error) {
    console.error('Claim rewards error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to claim rewards'
    });
  }
};

// Get Staking History
export const getStakingHistory = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const userPositions = stakingPositions.get(userId) || [];
    
    // Add current rewards to positions
    const positionsWithRewards = userPositions.map(position => ({
      ...position,
      currentRewards: calculateRewards(position)
    }));

    res.json({
      success: true,
      data: positionsWithRewards
    });
  } catch (error) {
    console.error('Get staking history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch staking history'
    });
  }
};

// Get DAO Proposals
export const getDAOProposals = async (req: Request, res: Response) => {
  try {
    const mockProposals = [
      {
        id: '1',
        title: 'Increase Staking Rewards',
        description: 'Proposal to increase staking rewards by 2% across all protocols',
        status: 'active',
        votesFor: 15420,
        votesAgainst: 3280,
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        proposer: 'NeuroDAO Community'
      },
      {
        id: '2',
        title: 'New Protocol Integration',
        description: 'Add support for Polygon network protocols',
        status: 'pending',
        votesFor: 8950,
        votesAgainst: 1200,
        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        proposer: 'Core Team'
      }
    ];

    res.json({
      success: true,
      data: mockProposals
    });
  } catch (error) {
    console.error('Get DAO proposals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch DAO proposals'
    });
  }
};

// Vote on Proposal
export const voteOnProposal = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { vote, amount } = req.body; // vote: 'for' | 'against'

    res.json({
      success: true,
      data: {
        proposalId: id,
        vote,
        votingPower: amount,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Vote submitted successfully'
    });
  } catch (error) {
    console.error('Vote on proposal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit vote'
    });
  }
};

// Create Proposal
export const createProposal = async (req: AuthRequest, res: Response) => {
  try {
    const { title, description, category } = req.body;

    const proposal = {
      id: `prop_${Date.now()}`,
      title,
      description,
      category,
      status: 'pending',
      votesFor: 0,
      votesAgainst: 0,
      endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
      proposer: req.user?.name || 'Anonymous'
    };

    res.status(201).json({
      success: true,
      data: proposal,
      message: 'Proposal created successfully'
    });
  } catch (error) {
    console.error('Create proposal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create proposal'
    });
  }
};

// Get Launchpad Projects
export const getLaunchpadProjects = async (req: Request, res: Response) => {
  try {
    const mockProjects = [
      {
        id: '1',
        name: 'NeuroAI Token',
        symbol: 'NAI',
        description: 'AI-powered governance token for the NeuroSphere ecosystem',
        totalRaise: '$2.5M',
        price: '$0.10',
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        raised: '$1.2M',
        participants: 3420
      },
      {
        id: '2',
        name: 'MetaVerse Land',
        symbol: 'LAND',
        description: 'Virtual real estate tokens for the NeuroSphere metaverse',
        totalRaise: '$5M',
        price: '$50',
        status: 'upcoming',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000),
        raised: '$0',
        participants: 0
      }
    ];

    res.json({
      success: true,
      data: mockProjects
    });
  } catch (error) {
    console.error('Get launchpad projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch launchpad projects'
    });
  }
};

// Participate in Launchpad
export const participateInLaunchpad = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { amount, paymentToken } = req.body;

    res.json({
      success: true,
      data: {
        projectId: id,
        investmentAmount: amount,
        paymentToken,
        tokensAllocated: amount * 10, // Example allocation
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Successfully participated in launchpad'
    });
  } catch (error) {
    console.error('Participate in launchpad error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to participate in launchpad'
    });
  }
};

// Get Crypto Prices
export const getCryptoPrices = async (req: Request, res: Response) => {
  try {
    // Simulate price fluctuations
    const fluctuatedPrices = Object.entries(cryptoPrices).reduce((acc, [token, price]) => {
      const fluctuation = (Math.random() - 0.5) * 0.1; // ±5% fluctuation
      acc[token] = price * (1 + fluctuation);
      return acc;
    }, {} as { [key: string]: number });

    res.json({
      success: true,
      data: fluctuatedPrices
    });
  } catch (error) {
    console.error('Get crypto prices error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch crypto prices'
    });
  }
};

// Get Portfolio
export const getPortfolio = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const wallet = wallets.get(userId || '');
    const positions = stakingPositions.get(userId || '') || [];

    if (!wallet) {
      return res.json({
        success: true,
        data: {
          totalValue: 0,
          tokens: {},
          stakingPositions: [],
          performance: { daily: 0, weekly: 0, monthly: 0 }
        }
      });
    }

    // Calculate total portfolio value
    let totalValue = 0;
    Object.entries(wallet.balance).forEach(([token, amount]) => {
      totalValue += amount * (cryptoPrices[token as keyof typeof cryptoPrices] || 0);
    });

    // Add staking value
    positions.forEach(position => {
      totalValue += position.amount * (cryptoPrices[position.token as keyof typeof cryptoPrices] || 0);
    });

    res.json({
      success: true,
      data: {
        totalValue,
        tokens: wallet.balance,
        stakingPositions: positions.map(p => ({
          ...p,
          currentRewards: calculateRewards(p)
        })),
        performance: {
          daily: Math.random() * 10 - 5, // Mock performance
          weekly: Math.random() * 20 - 10,
          monthly: Math.random() * 50 - 25
        }
      }
    });
  } catch (error) {
    console.error('Get portfolio error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch portfolio'
    });
  }
};

// Get Transaction History
export const getTransactionHistory = async (req: AuthRequest, res: Response) => {
  try {
    const mockTransactions = [
      {
        id: '1',
        type: 'stake',
        token: 'NEURO',
        amount: 1000,
        hash: `0x${Math.random().toString(16).substr(2, 64)}`,
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        status: 'confirmed'
      },
      {
        id: '2',
        type: 'swap',
        fromToken: 'ETH',
        toToken: 'USDC',
        fromAmount: 1,
        toAmount: 3456,
        hash: `0x${Math.random().toString(16).substr(2, 64)}`,
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        status: 'confirmed'
      }
    ];

    res.json({
      success: true,
      data: mockTransactions
    });
  } catch (error) {
    console.error('Get transaction history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction history'
    });
  }
};

// Swap Tokens
export const swapTokens = async (req: AuthRequest, res: Response) => {
  try {
    const { fromToken, toToken, fromAmount } = req.body;
    
    // Mock exchange rate calculation
    const fromPrice = cryptoPrices[fromToken as keyof typeof cryptoPrices] || 1;
    const toPrice = cryptoPrices[toToken as keyof typeof cryptoPrices] || 1;
    const toAmount = (fromAmount * fromPrice) / toPrice;

    res.json({
      success: true,
      data: {
        fromToken,
        toToken,
        fromAmount,
        toAmount,
        exchangeRate: fromPrice / toPrice,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`,
        fee: fromAmount * 0.003 // 0.3% fee
      },
      message: 'Token swap completed successfully'
    });
  } catch (error) {
    console.error('Swap tokens error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to swap tokens'
    });
  }
};

// Add Liquidity
export const addLiquidity = async (req: AuthRequest, res: Response) => {
  try {
    const { tokenA, tokenB, amountA, amountB } = req.body;

    res.json({
      success: true,
      data: {
        tokenA,
        tokenB,
        amountA,
        amountB,
        lpTokens: Math.sqrt(amountA * amountB), // Simplified LP calculation
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Liquidity added successfully'
    });
  } catch (error) {
    console.error('Add liquidity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add liquidity'
    });
  }
};

// Remove Liquidity
export const removeLiquidity = async (req: AuthRequest, res: Response) => {
  try {
    const { lpTokens, tokenA, tokenB } = req.body;

    res.json({
      success: true,
      data: {
        lpTokens,
        tokenAReceived: lpTokens * 0.5, // Simplified calculation
        tokenBReceived: lpTokens * 0.5,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Liquidity removed successfully'
    });
  } catch (error) {
    console.error('Remove liquidity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove liquidity'
    });
  }
};

// Get Yield Farms
export const getYieldFarms = async (req: Request, res: Response) => {
  try {
    const mockFarms = [
      {
        id: '1',
        name: 'NEURO-ETH LP',
        apy: '45.2%',
        tvl: '$12.5M',
        tokens: ['NEURO', 'ETH'],
        rewards: ['NEURO'],
        multiplier: '2x'
      },
      {
        id: '2',
        name: 'USDC-USDT LP',
        apy: '8.7%',
        tvl: '$45.2M',
        tokens: ['USDC', 'USDT'],
        rewards: ['NEURO'],
        multiplier: '1x'
      }
    ];

    res.json({
      success: true,
      data: mockFarms
    });
  } catch (error) {
    console.error('Get yield farms error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch yield farms'
    });
  }
};

// Join Yield Farm
export const joinYieldFarm = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { lpTokens } = req.body;

    res.json({
      success: true,
      data: {
        farmId: id,
        stakedAmount: lpTokens,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Successfully joined yield farm'
    });
  } catch (error) {
    console.error('Join yield farm error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to join yield farm'
    });
  }
};

// Exit Yield Farm
export const exitYieldFarm = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    res.json({
      success: true,
      data: {
        farmId: id,
        lpTokensReturned: 100, // Mock amount
        rewardsEarned: 50,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
      },
      message: 'Successfully exited yield farm'
    });
  } catch (error) {
    console.error('Exit yield farm error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to exit yield farm'
    });
  }
};

// Helper function to calculate staking rewards
function calculateRewards(position: StakingPosition): number {
  const now = new Date();
  const stakingDays = (now.getTime() - position.startDate.getTime()) / (1000 * 60 * 60 * 24);
  const annualRewards = position.amount * (position.apy / 100);
  return (annualRewards / 365) * stakingDays;
}
