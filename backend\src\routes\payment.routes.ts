import { Router } from 'express';
import {
  createPaymentIntent,
  confirmPayment,
  processRefund,
  getPaymentHistory,
  handleWebhook,
  createSubscription
} from '../controllers/payment.controller';
import { authenticateToken, requireAdmin } from '../middleware/auth.middleware';

const router = Router();

// Payment intent routes
router.post('/create-intent', authenticateToken, createPaymentIntent);
router.post('/confirm', authenticateToken, confirmPayment);

// Subscription routes
router.post('/create-subscription', authenticateToken, createSubscription);

// Payment history
router.get('/history', authenticateToken, getPaymentHistory);

// Admin routes
router.post('/refund', requireAdmin, processRefund);

// Webhook route (no authentication required)
router.post('/webhook', handleWebhook);

export default router;
