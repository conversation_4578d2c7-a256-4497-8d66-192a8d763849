import React, { useState, useEffect } from 'react';
import { Search, Filter, X, ChevronDown, Star, DollarSign, Calendar, Tag } from 'lucide-react';
import api from '../services/api';

interface SearchFilters {
  query: string;
  category: string;
  minPrice: number;
  maxPrice: number;
  rating: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  dateRange: string;
  tags: string[];
}

interface AdvancedSearchProps {
  type: 'products' | 'courses' | 'nfts';
  onResults: (results: any[]) => void;
  onFiltersChange?: (filters: SearchFilters) => void;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({ type, onResults, onFiltersChange }) => {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    category: '',
    minPrice: 0,
    maxPrice: 10000,
    rating: 0,
    sortBy: 'createdAt',
    sortOrder: 'desc',
    dateRange: '',
    tags: []
  });

  const [showFilters, setShowFilters] = useState(false);
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Category options based on type
  const getCategoryOptions = () => {
    switch (type) {
      case 'products':
        return [
          { value: '', label: 'All Categories' },
          { value: 'electronics', label: 'Electronics' },
          { value: 'fashion', label: 'Fashion' },
          { value: 'home', label: 'Home & Garden' },
          { value: 'beauty', label: 'Beauty' },
          { value: 'sports', label: 'Sports' },
          { value: 'books', label: 'Books' },
          { value: 'toys', label: 'Toys' },
          { value: 'automotive', label: 'Automotive' }
        ];
      case 'courses':
        return [
          { value: '', label: 'All Categories' },
          { value: 'ai', label: 'AI & Machine Learning' },
          { value: 'blockchain', label: 'Blockchain' },
          { value: 'business', label: 'Business' },
          { value: 'design', label: 'Design' },
          { value: 'tech', label: 'Technology' },
          { value: 'marketing', label: 'Marketing' },
          { value: 'health', label: 'Health' },
          { value: 'finance', label: 'Finance' }
        ];
      case 'nfts':
        return [
          { value: '', label: 'All Categories' },
          { value: 'art', label: 'Art' },
          { value: 'music', label: 'Music' },
          { value: 'video', label: 'Video' },
          { value: 'gaming', label: 'Gaming' },
          { value: 'photography', label: 'Photography' },
          { value: 'utility', label: 'Utility' },
          { value: 'collectible', label: 'Collectible' }
        ];
      default:
        return [];
    }
  };

  // Sort options
  const getSortOptions = () => {
    const common = [
      { value: 'createdAt', label: 'Date Created' },
      { value: 'name', label: 'Name' },
      { value: 'rating', label: 'Rating' }
    ];

    if (type === 'products' || type === 'courses') {
      common.push({ value: 'price', label: 'Price' });
    }

    if (type === 'nfts') {
      common.push({ value: 'views', label: 'Views' }, { value: 'likes', label: 'Likes' });
    }

    return common;
  };

  // Perform search
  const performSearch = async () => {
    try {
      setLoading(true);
      let response;

      const searchParams = {
        search: filters.query,
        category: filters.category,
        minPrice: filters.minPrice,
        maxPrice: filters.maxPrice,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        limit: 20
      };

      switch (type) {
        case 'products':
          response = await api.getProducts(searchParams);
          break;
        case 'courses':
          response = await api.getCourses(searchParams);
          break;
        case 'nfts':
          response = await api.getNFTs(searchParams);
          break;
      }

      if (response?.success) {
        const results = response.data[type] || [];
        onResults(results);
      }
    } catch (error) {
      console.error('Search error:', error);
      onResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Update filters
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  // Get search suggestions
  const getSearchSuggestions = async (query: string) => {
    if (query.length < 2) {
      setSuggestions([]);
      return;
    }

    // Mock suggestions - in real app, this would be an API call
    const mockSuggestions = [
      'AI Machine Learning',
      'Blockchain Development',
      'Digital Art NFT',
      'Smart Watch',
      'Sustainable Fashion',
      'Web3 Development'
    ].filter(s => s.toLowerCase().includes(query.toLowerCase()));

    setSuggestions(mockSuggestions);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    updateFilter('query', value);
    getSearchSuggestions(value);
    setShowSuggestions(true);
  };

  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuggestions(false);
    performSearch();
  };

  // Auto-search when filters change
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (filters.query || filters.category || filters.minPrice > 0 || filters.maxPrice < 10000) {
        performSearch();
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [filters]);

  return (
    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="relative mb-4">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
          <input
            type="text"
            value={filters.query}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={() => setShowSuggestions(true)}
            placeholder={`Search ${type}...`}
            className="w-full pl-12 pr-12 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:border-blue-400 transition-colors"
          />
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
          >
            <Filter className="h-5 w-5" />
          </button>
        </div>

        {/* Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-xl shadow-2xl z-50">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => {
                  updateFilter('query', suggestion);
                  setShowSuggestions(false);
                  performSearch();
                }}
                className="w-full px-4 py-3 text-left text-white hover:bg-white/10 transition-colors first:rounded-t-xl last:rounded-b-xl"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </form>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="space-y-4 border-t border-white/10 pt-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Category</label>
              <select
                value={filters.category}
                onChange={(e) => updateFilter('category', e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-blue-400"
              >
                {getCategoryOptions().map(option => (
                  <option key={option.value} value={option.value} className="bg-gray-900">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Price Range
              </label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  value={filters.minPrice}
                  onChange={(e) => updateFilter('minPrice', Number(e.target.value))}
                  placeholder="Min"
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-blue-400"
                />
                <input
                  type="number"
                  value={filters.maxPrice}
                  onChange={(e) => updateFilter('maxPrice', Number(e.target.value))}
                  placeholder="Max"
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-blue-400"
                />
              </div>
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">
                <Star className="inline h-4 w-4 mr-1" />
                Minimum Rating
              </label>
              <select
                value={filters.rating}
                onChange={(e) => updateFilter('rating', Number(e.target.value))}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-blue-400"
              >
                <option value={0} className="bg-gray-900">Any Rating</option>
                <option value={1} className="bg-gray-900">1+ Stars</option>
                <option value={2} className="bg-gray-900">2+ Stars</option>
                <option value={3} className="bg-gray-900">3+ Stars</option>
                <option value={4} className="bg-gray-900">4+ Stars</option>
                <option value={5} className="bg-gray-900">5 Stars</option>
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-white/80 mb-2">Sort By</label>
              <div className="flex space-x-2">
                <select
                  value={filters.sortBy}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-blue-400"
                >
                  {getSortOptions().map(option => (
                    <option key={option.value} value={option.value} className="bg-gray-900">
                      {option.label}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-colors"
                >
                  {filters.sortOrder === 'asc' ? '↑' : '↓'}
                </button>
              </div>
            </div>
          </div>

          {/* Clear Filters */}
          <div className="flex justify-between items-center pt-4 border-t border-white/10">
            <button
              type="button"
              onClick={() => {
                setFilters({
                  query: '',
                  category: '',
                  minPrice: 0,
                  maxPrice: 10000,
                  rating: 0,
                  sortBy: 'createdAt',
                  sortOrder: 'desc',
                  dateRange: '',
                  tags: []
                });
              }}
              className="text-sm text-red-400 hover:text-red-300 transition-colors"
            >
              Clear All Filters
            </button>
            
            <div className="text-sm text-white/60">
              {loading ? 'Searching...' : 'Search results update automatically'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedSearch;
