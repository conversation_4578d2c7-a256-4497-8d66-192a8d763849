import { Router } from 'express';
import {
  getPlatformStats,
  getUserDashboard,
  getSalesAnalytics,
  getCourseAnalytics,
  getNFTAnalytics
} from '../controllers/analytics.controller';
import { authenticateToken, requireAdmin } from '../middleware/auth.middleware';

const router = Router();

// All analytics routes require authentication
router.use(authenticateToken);

// User analytics
router.get('/dashboard', getUserDashboard);
router.get('/sales', getSalesAnalytics);
router.get('/courses', getCourseAnalytics);
router.get('/nfts', getNFTAnalytics);

// Admin analytics
router.get('/platform', requireAdmin, getPlatformStats);

export default router;
