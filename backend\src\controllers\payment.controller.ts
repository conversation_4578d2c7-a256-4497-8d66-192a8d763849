import { Request, Response } from 'express';
import Stripe from 'stripe';
import { Order } from '../models/order.model';
import { Product } from '../models/product.model';
import { Course } from '../models/course.model';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2024-06-20',
});

// Create payment intent
export const createPaymentIntent = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { amount, currency = 'usd', orderId, metadata } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      customer: req.user.email, // You might want to create Stripe customers
      metadata: {
        userId: req.user._id.toString(),
        orderId: orderId || '',
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id
      }
    });
  } catch (error: any) {
    console.error('Create payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment intent',
      error: error.message
    });
  }
};

// Confirm payment
export const confirmPayment = async (req: Request, res: Response) => {
  try {
    const { paymentIntentId, orderId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        message: 'Payment intent ID is required'
      });
    }

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status === 'succeeded') {
      // Update order status if orderId is provided
      if (orderId) {
        const order = await Order.findById(orderId);
        if (order) {
          order.payment.status = 'completed';
          order.payment.transactionId = paymentIntent.id;
          order.payment.paidAt = new Date();
          order.status = 'confirmed';
          order.timeline.push({
            status: 'confirmed',
            timestamp: new Date(),
            note: 'Payment confirmed'
          });
          await order.save();

          // Update product inventory
          for (const item of order.items) {
            const product = await Product.findById(item.productId);
            if (product) {
              product.inventory.reserved -= item.quantity;
              product.inventory.sold += item.quantity;
              await product.save();
            }
          }
        }
      }

      res.json({
        success: true,
        message: 'Payment confirmed successfully',
        data: {
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount / 100,
            currency: paymentIntent.currency
          }
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Payment not completed',
        data: { status: paymentIntent.status }
      });
    }
  } catch (error: any) {
    console.error('Confirm payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to confirm payment',
      error: error.message
    });
  }
};

// Process refund
export const processRefund = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const { paymentIntentId, amount, reason } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        message: 'Payment intent ID is required'
      });
    }

    // Create refund
    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      amount: amount ? Math.round(amount * 100) : undefined, // Partial refund if amount specified
      reason: reason || 'requested_by_customer',
      metadata: {
        processedBy: req.user._id.toString(),
        processedAt: new Date().toISOString()
      }
    });

    res.json({
      success: true,
      message: 'Refund processed successfully',
      data: {
        refund: {
          id: refund.id,
          amount: refund.amount / 100,
          currency: refund.currency,
          status: refund.status,
          reason: refund.reason
        }
      }
    });
  } catch (error: any) {
    console.error('Process refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process refund',
      error: error.message
    });
  }
};

// Get payment history
export const getPaymentHistory = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { page = 1, limit = 20 } = req.query;

    // Get user's orders with payment information
    const orders = await Order.find({
      'user.id': req.user._id,
      'payment.status': { $in: ['completed', 'refunded', 'partially_refunded'] }
    })
    .sort({ createdAt: -1 })
    .skip((Number(page) - 1) * Number(limit))
    .limit(Number(limit));

    const totalOrders = await Order.countDocuments({
      'user.id': req.user._id,
      'payment.status': { $in: ['completed', 'refunded', 'partially_refunded'] }
    });

    const payments = orders.map(order => ({
      id: order._id,
      orderNumber: order.orderNumber,
      amount: order.pricing.total,
      currency: order.pricing.currency,
      status: order.payment.status,
      method: order.payment.method,
      transactionId: order.payment.transactionId,
      paidAt: order.payment.paidAt,
      refundedAt: order.payment.refundedAt,
      refundAmount: order.payment.refundAmount,
      items: order.items.map(item => ({
        name: item.productName,
        quantity: item.quantity,
        price: item.price
      }))
    }));

    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(totalOrders / Number(limit)),
          totalItems: totalOrders,
          itemsPerPage: Number(limit)
        }
      }
    });
  } catch (error: any) {
    console.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get payment history',
      error: error.message
    });
  }
};

// Webhook handler for Stripe events
export const handleWebhook = async (req: Request, res: Response) => {
  try {
    const sig = req.headers['stripe-signature'] as string;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!webhookSecret) {
      return res.status(400).json({
        success: false,
        message: 'Webhook secret not configured'
      });
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      return res.status(400).json({
        success: false,
        message: 'Webhook signature verification failed'
      });
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('Payment succeeded:', paymentIntent.id);
        // Handle successful payment
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        console.log('Payment failed:', failedPayment.id);
        // Handle failed payment
        break;

      case 'charge.dispute.created':
        const dispute = event.data.object as Stripe.Dispute;
        console.log('Dispute created:', dispute.id);
        // Handle dispute
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error: any) {
    console.error('Webhook handler error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook handler failed',
      error: error.message
    });
  }
};

// Create subscription
export const createSubscription = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { priceId, paymentMethodId } = req.body;

    if (!priceId) {
      return res.status(400).json({
        success: false,
        message: 'Price ID is required'
      });
    }

    // Create or retrieve customer
    let customer;
    try {
      const customers = await stripe.customers.list({
        email: req.user.email,
        limit: 1
      });

      if (customers.data.length > 0) {
        customer = customers.data[0];
      } else {
        customer = await stripe.customers.create({
          email: req.user.email,
          name: req.user.name,
          metadata: {
            userId: req.user._id.toString()
          }
        });
      }
    } catch (error) {
      console.error('Customer creation error:', error);
      throw error;
    }

    // Attach payment method to customer if provided
    if (paymentMethodId) {
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customer.id,
      });
    }

    // Create subscription
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
    });

    res.json({
      success: true,
      data: {
        subscriptionId: subscription.id,
        clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
        status: subscription.status
      }
    });
  } catch (error: any) {
    console.error('Create subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription',
      error: error.message
    });
  }
};
