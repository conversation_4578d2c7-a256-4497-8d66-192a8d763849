import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Brain, 
  ShoppingCart, 
  Video, 
  GraduationCap, 
  Wallet, 
  Heart, 
  CheckSquare,
  User,
  Bell,
  Search,
  Menu,
  X,
  Sparkles,
  Atom,
  Globe,
  Coins,
  Zap,
  MessageCircle,
  Play,
  BarChart3,
  Code
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import NotificationCenter from './NotificationCenter';
import AIAssistant from './AIAssistant';
import VoiceInterface from './VoiceInterface';
import PerformanceMonitor from './PerformanceMonitor';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { state } = useApp();
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/', icon: Brain },
    { name: 'Commerce', href: '/commerce', icon: ShoppingCart },
    { name: 'Creator', href: '/creator', icon: Video },
    { name: 'AI Studio', href: '/ai-studio', icon: Sparkles },
    { name: 'Learn', href: '/learn', icon: GraduationCap },
    { name: 'FinTech', href: '/fintech', icon: Wallet },
    { name: 'Health', href: '/health', icon: Heart },
    { name: 'Tasks', href: '/productivity', icon: CheckSquare },
    { name: 'Quantum AI', href: '/quantum-ai', icon: Atom },
    { name: 'Metaverse', href: '/metaverse', icon: Globe },
    { name: 'Blockchain', href: '/blockchain', icon: Coins },
    { name: 'NeuroLink', href: '/neuro-interface', icon: Zap },
    { name: 'Chat', href: '/chat', icon: MessageCircle },
    { name: 'Streaming', href: '/streaming', icon: Play },
    { name: 'Analytics', href: '/analytics', icon: BarChart3 },
    { name: 'Contracts', href: '/contracts', icon: Code }
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="fixed w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link to="/" className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-purple-400" />
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                NeuroSphere
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-2 overflow-x-auto max-w-2xl">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-1 px-2 py-2 rounded-lg transition-all duration-200 whitespace-nowrap ${
                    isActive(item.href)
                      ? 'bg-purple-600/20 text-purple-300 border border-purple-500/30'
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <item.icon className="h-4 w-4" />
                  <span className="text-xs font-medium">{item.name}</span>
                </Link>
              ))}
            </div>

            {/* User Section */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="hidden md:block bg-white/10 border border-white/20 rounded-full pl-10 pr-4 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              
              <NotificationCenter />

              <Link to="/profile" className="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                <img
                  src={state.user?.avatar}
                  alt={state.user?.name}
                  className="h-8 w-8 rounded-full border-2 border-purple-400"
                />
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-white">{state.user?.name}</div>
                  <div className="text-xs text-purple-300">Level {state.user?.level}</div>
                </div>
              </Link>

              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 text-white/80 hover:text-white transition-colors"
              >
                {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden bg-black/40 backdrop-blur-md border-t border-white/10">
            <div className="px-4 py-4 space-y-2 max-h-96 overflow-y-auto">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-purple-600/20 text-purple-300 border border-purple-500/30'
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="font-medium">{item.name}</span>
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Performance Monitor */}
      <PerformanceMonitor />

      {/* Voice Interface */}
      <VoiceInterface />

      {/* AI Assistant */}
      <AIAssistant />

      {/* Main Content */}
      <main className="pt-20">
        {children}
      </main>
    </div>
  );
};

export default Layout;