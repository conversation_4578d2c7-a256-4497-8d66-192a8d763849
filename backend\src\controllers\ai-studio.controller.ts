import { Request, Response } from 'express';
import { 
  AICreation, 
  AIGenerationHistory, 
  AICredits, 
  CollaborationProject, 
  AIMarketplaceItem, 
  AITool 
} from '../models/ai-studio.model';
import { AuthRequest } from '../middleware/auth.middleware';

// Content Generation
export const generateContent = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { type, prompt, parameters = {} } = req.body;

    // Check user credits
    const userCredits = await AICredits.findOne({ userId });
    if (!userCredits || userCredits.remainingCredits < 1) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient credits'
      });
    }

    // Get AI tool configuration
    const aiTool = await AITool.findOne({ type, isActive: true });
    if (!aiTool) {
      return res.status(400).json({
        success: false,
        message: 'AI tool not available'
      });
    }

    const startTime = Date.now();

    // Create generation history record
    const generationHistory = new AIGenerationHistory({
      userId,
      type,
      prompt,
      model: aiTool.model,
      parameters,
      creditsUsed: aiTool.creditsPerUse,
      generationTime: 0,
      status: 'pending'
    });
    await generationHistory.save();

    // Simulate AI generation (replace with actual AI service)
    const mockResults = {
      text: {
        content: `Generated content based on: "${prompt}"\n\nThis is a sample AI-generated text that demonstrates the system's capability.`,
        wordCount: 25,
        readingTime: '1 min'
      },
      image: {
        url: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=800',
        dimensions: '1024x1024',
        style: 'Realistic'
      },
      music: {
        url: '/uploads/generated-music.mp3',
        duration: '3:45',
        genre: 'Ambient',
        bpm: 120
      },
      video: {
        url: '/uploads/generated-video.mp4',
        duration: '0:30',
        resolution: '1920x1080'
      },
      voice: {
        url: '/uploads/generated-voice.mp3',
        duration: '1:23',
        voice: 'Natural Female'
      },
      design: {
        url: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=800',
        format: 'SVG',
        colors: ['#6366f1', '#8b5cf6', '#ec4899']
      }
    };

    const result = mockResults[type as keyof typeof mockResults];
    const generationTime = Date.now() - startTime;

    // Update generation history
    generationHistory.result = result;
    generationHistory.generationTime = generationTime;
    generationHistory.status = 'success';
    await generationHistory.save();

    // Deduct credits
    userCredits.usedCredits += aiTool.creditsPerUse;
    userCredits.remainingCredits -= aiTool.creditsPerUse;
    userCredits.transactions.push({
      type: 'usage',
      amount: -aiTool.creditsPerUse,
      description: `${type} generation: ${prompt.substring(0, 50)}...`,
      date: new Date()
    });
    await userCredits.save();

    res.json({
      success: true,
      data: {
        generationId: generationHistory._id,
        result,
        creditsUsed: aiTool.creditsPerUse,
        remainingCredits: userCredits.remainingCredits
      }
    });
  } catch (error) {
    console.error('Generate content error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate content'
    });
  }
};

// Get Generation History
export const getGenerationHistory = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { page = 1, limit = 20, type } = req.query;

    const filter: any = { userId };
    if (type) filter.type = type;

    const history = await AIGenerationHistory.find(filter)
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await AIGenerationHistory.countDocuments(filter);

    res.json({
      success: true,
      data: {
        history,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get generation history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch generation history'
    });
  }
};

// Save Creation
export const saveCreation = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { title, description, type, prompt, content, tags = [], isPublic = false } = req.body;

    const creation = new AICreation({
      userId,
      title,
      description,
      type,
      prompt,
      content,
      metadata: {
        model: 'gpt-4',
        parameters: {},
        generationTime: 1000,
        creditsUsed: 1
      },
      tags,
      isPublic,
      status: 'completed'
    });

    await creation.save();

    res.status(201).json({
      success: true,
      data: creation,
      message: 'Creation saved successfully'
    });
  } catch (error) {
    console.error('Save creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save creation'
    });
  }
};

// Get Creations
export const getCreations = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { page = 1, limit = 20, type, isPublic } = req.query;

    const filter: any = { userId };
    if (type) filter.type = type;
    if (isPublic !== undefined) filter.isPublic = isPublic === 'true';

    const creations = await AICreation.find(filter)
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit))
      .populate('collaborators', 'name avatar');

    const total = await AICreation.countDocuments(filter);

    res.json({
      success: true,
      data: {
        creations,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get creations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch creations'
    });
  }
};

// Delete Creation
export const deleteCreation = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    const creation = await AICreation.findOneAndDelete({ _id: id, userId });
    if (!creation) {
      return res.status(404).json({
        success: false,
        message: 'Creation not found'
      });
    }

    res.json({
      success: true,
      message: 'Creation deleted successfully'
    });
  } catch (error) {
    console.error('Delete creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete creation'
    });
  }
};

// Share Creation
export const shareCreation = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    const creation = await AICreation.findOneAndUpdate(
      { _id: id, userId },
      { isPublic: true },
      { new: true }
    );

    if (!creation) {
      return res.status(404).json({
        success: false,
        message: 'Creation not found'
      });
    }

    res.json({
      success: true,
      data: creation,
      message: 'Creation shared successfully'
    });
  } catch (error) {
    console.error('Share creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to share creation'
    });
  }
};

// Mint as NFT
export const mintAsNFT = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;
    const { name, description, price, royalty } = req.body;

    const creation = await AICreation.findOne({ _id: id, userId });
    if (!creation) {
      return res.status(404).json({
        success: false,
        message: 'Creation not found'
      });
    }

    // Simulate NFT minting (replace with actual blockchain integration)
    const tokenId = `nft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    creation.nftTokenId = tokenId;
    await creation.save();

    res.json({
      success: true,
      data: {
        tokenId,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`,
        creation
      },
      message: 'NFT minted successfully'
    });
  } catch (error) {
    console.error('Mint NFT error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mint NFT'
    });
  }
};

// Get AI Tools
export const getAITools = async (req: Request, res: Response) => {
  try {
    const { type } = req.query;
    
    const filter: any = { isActive: true };
    if (type) filter.type = type;

    const tools = await AITool.find(filter).sort({ name: 1 });

    res.json({
      success: true,
      data: tools
    });
  } catch (error) {
    console.error('Get AI tools error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch AI tools'
    });
  }
};

// Get AI Credits
export const getAICredits = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    let credits = await AICredits.findOne({ userId });
    if (!credits) {
      credits = new AICredits({
        userId,
        totalCredits: 100,
        usedCredits: 0,
        remainingCredits: 100,
        subscriptionType: 'free'
      });
      await credits.save();
    }

    res.json({
      success: true,
      data: credits
    });
  } catch (error) {
    console.error('Get AI credits error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch AI credits'
    });
  }
};

// Purchase Credits
export const purchaseCredits = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { amount, paymentMethod } = req.body;

    const credits = await AICredits.findOne({ userId });
    if (!credits) {
      return res.status(404).json({
        success: false,
        message: 'Credits account not found'
      });
    }

    // Simulate payment processing
    credits.totalCredits += amount;
    credits.remainingCredits += amount;
    credits.transactions.push({
      type: 'purchase',
      amount,
      description: `Purchased ${amount} credits`,
      date: new Date()
    });

    await credits.save();

    res.json({
      success: true,
      data: credits,
      message: 'Credits purchased successfully'
    });
  } catch (error) {
    console.error('Purchase credits error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to purchase credits'
    });
  }
};

// Get Collaborations
export const getCollaborations = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    const collaborations = await CollaborationProject.find({
      $or: [
        { owner: userId },
        { 'collaborators.userId': userId }
      ]
    })
    .populate('owner', 'name avatar')
    .populate('collaborators.userId', 'name avatar')
    .sort({ updatedAt: -1 });

    res.json({
      success: true,
      data: collaborations
    });
  } catch (error) {
    console.error('Get collaborations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch collaborations'
    });
  }
};

// Create Collaboration
export const createCollaboration = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { title, description, type, settings = {} } = req.body;

    const collaboration = new CollaborationProject({
      title,
      description,
      owner: userId,
      type,
      settings: {
        isPublic: false,
        allowInvites: true,
        maxCollaborators: 10,
        ...settings
      }
    });

    await collaboration.save();
    await collaboration.populate('owner', 'name avatar');

    res.status(201).json({
      success: true,
      data: collaboration,
      message: 'Collaboration created successfully'
    });
  } catch (error) {
    console.error('Create collaboration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create collaboration'
    });
  }
};

// Join Collaboration
export const joinCollaboration = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    const collaboration = await CollaborationProject.findById(id);
    if (!collaboration) {
      return res.status(404).json({
        success: false,
        message: 'Collaboration not found'
      });
    }

    // Check if user is already a collaborator
    const isCollaborator = collaboration.collaborators.some(
      collab => collab.userId.toString() === userId
    );

    if (isCollaborator) {
      return res.status(400).json({
        success: false,
        message: 'Already a collaborator'
      });
    }

    collaboration.collaborators.push({
      userId,
      role: 'editor',
      joinedAt: new Date(),
      permissions: ['read', 'write']
    });

    await collaboration.save();
    await collaboration.populate('collaborators.userId', 'name avatar');

    res.json({
      success: true,
      data: collaboration,
      message: 'Joined collaboration successfully'
    });
  } catch (error) {
    console.error('Join collaboration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to join collaboration'
    });
  }
};

// Get Marketplace
export const getMarketplace = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 20, type, category, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    const filter: any = { isActive: true };
    if (type) filter.type = type;
    if (category) filter.category = category;
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search as string, 'i')] } }
      ];
    }

    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const items = await AIMarketplaceItem.find(filter)
      .populate('sellerId', 'name avatar')
      .populate('creationId')
      .sort(sort)
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await AIMarketplaceItem.countDocuments(filter);

    res.json({
      success: true,
      data: {
        items,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get marketplace error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch marketplace items'
    });
  }
};

// Sell Creation
export const sellCreation = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { creationId, title, description, price, currency = 'USD', category, license = 'personal' } = req.body;

    const creation = await AICreation.findOne({ _id: creationId, userId });
    if (!creation) {
      return res.status(404).json({
        success: false,
        message: 'Creation not found'
      });
    }

    const marketplaceItem = new AIMarketplaceItem({
      creationId,
      sellerId: userId,
      title,
      description,
      type: creation.type,
      price,
      currency,
      category,
      tags: creation.tags,
      previewUrl: creation.content.imageUrl || creation.content.text?.substring(0, 200) || '',
      fullContentUrl: creation.content.imageUrl || creation.content.audioUrl || creation.content.videoUrl || '',
      license
    });

    await marketplaceItem.save();
    await marketplaceItem.populate('sellerId', 'name avatar');

    creation.isForSale = true;
    creation.price = price;
    await creation.save();

    res.status(201).json({
      success: true,
      data: marketplaceItem,
      message: 'Creation listed for sale successfully'
    });
  } catch (error) {
    console.error('Sell creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to list creation for sale'
    });
  }
};

// Buy Creation
export const buyCreation = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { id } = req.params;

    const item = await AIMarketplaceItem.findById(id)
      .populate('sellerId', 'name avatar')
      .populate('creationId');

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Marketplace item not found'
      });
    }

    if (item.sellerId._id.toString() === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot buy your own creation'
      });
    }

    // Simulate payment processing
    item.sales += 1;
    await item.save();

    res.json({
      success: true,
      data: {
        item,
        transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        downloadUrl: item.fullContentUrl
      },
      message: 'Purchase completed successfully'
    });
  } catch (error) {
    console.error('Buy creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to purchase creation'
    });
  }
};

// Get Trending Content
export const getTrendingContent = async (req: Request, res: Response) => {
  try {
    const { limit = 10, type } = req.query;

    const filter: any = { isPublic: true };
    if (type) filter.type = type;

    const trending = await AICreation.find(filter)
      .sort({ likes: -1, views: -1, createdAt: -1 })
      .limit(Number(limit))
      .populate('userId', 'name avatar');

    res.json({
      success: true,
      data: trending
    });
  } catch (error) {
    console.error('Get trending content error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch trending content'
    });
  }
};

// Get AI Insights
export const getAIInsights = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    // Get user's generation statistics
    const totalGenerations = await AIGenerationHistory.countDocuments({ userId });
    const successfulGenerations = await AIGenerationHistory.countDocuments({ 
      userId, 
      status: 'success' 
    });

    const recentGenerations = await AIGenerationHistory.find({ userId })
      .sort({ createdAt: -1 })
      .limit(30);

    const typeStats = await AIGenerationHistory.aggregate([
      { $match: { userId: userId } },
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    const insights = {
      totalGenerations,
      successRate: totalGenerations > 0 ? (successfulGenerations / totalGenerations) * 100 : 0,
      favoriteType: typeStats.length > 0 ? typeStats.reduce((a, b) => a.count > b.count ? a : b)._id : 'text',
      averageGenerationTime: recentGenerations.reduce((sum, gen) => sum + gen.generationTime, 0) / recentGenerations.length || 0,
      recommendations: [
        'Try experimenting with different prompt styles',
        'Consider using more specific parameters for better results',
        'Explore collaboration features for team projects'
      ]
    };

    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    console.error('Get AI insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch AI insights'
    });
  }
};
