import { Router } from 'express';
import {
  getNFTs,
  getNFTById,
  createNFT,
  updateNFT,
  transferNFT,
  toggleNFTLike
} from '../controllers/nft.controller';
import { authenticateToken, requireCreator, optionalAuth } from '../middleware/auth.middleware';
import { validate, nftValidation } from '../middleware/validation.middleware';

const router = Router();

// Public routes (with optional auth for likes)
router.get('/', optionalAuth, getNFTs);
router.get('/:id', optionalAuth, getNFTById);

// Protected routes
router.post('/', authenticateToken, requireCreator, validate(nftValidation.create), createNFT);
router.put('/:id', authenticateToken, updateNFT);
router.post('/:id/transfer', authenticateToken, transferNFT);
router.post('/:id/like', authenticateToken, toggleNFTLike);

export default router;
