import React, { createContext, useContext, useReducer, ReactNode } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  level: number;
  points: number;
  balance: number;
  subscriptions: string[];
}

interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  notifications: any[];
  cart: any[];
  courses: any[];
  investments: any[];
  healthData: any;
  tasks: any[];
}

type AppAction = 
  | { type: 'LOGIN'; payload: User }
  | { type: 'LOGOUT' }
  | { type: 'ADD_TO_CART'; payload: any }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'ENROLL_COURSE'; payload: any }
  | { type: 'ADD_INVESTMENT'; payload: any }
  | { type: 'UPDATE_HEALTH_DATA'; payload: any }
  | { type: 'ADD_TASK'; payload: any }
  | { type: 'COMPLETE_TASK'; payload: string }
  | { type: 'ADD_NOTIFICATION'; payload: any };

const initialState: AppState = {
  user: {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
    level: 12,
    points: 15420,
    balance: 2847.50,
    subscriptions: ['premium', 'creator-pro']
  },
  isAuthenticated: true,
  notifications: [],
  cart: [],
  courses: [],
  investments: [],
  healthData: {
    steps: 8420,
    heartRate: 72,
    sleep: 7.5,
    mood: 'Great'
  },
  tasks: []
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'LOGIN':
      return { ...state, user: action.payload, isAuthenticated: true };
    case 'LOGOUT':
      return { ...state, user: null, isAuthenticated: false };
    case 'ADD_TO_CART':
      return { ...state, cart: [...state.cart, action.payload] };
    case 'REMOVE_FROM_CART':
      return { ...state, cart: state.cart.filter(item => item.id !== action.payload) };
    case 'ENROLL_COURSE':
      return { ...state, courses: [...state.courses, action.payload] };
    case 'ADD_INVESTMENT':
      return { ...state, investments: [...state.investments, action.payload] };
    case 'UPDATE_HEALTH_DATA':
      return { ...state, healthData: { ...state.healthData, ...action.payload } };
    case 'ADD_TASK':
      return { ...state, tasks: [...state.tasks, action.payload] };
    case 'COMPLETE_TASK':
      return { 
        ...state, 
        tasks: state.tasks.map(task => 
          task.id === action.payload ? { ...task, completed: true } : task
        ) 
      };
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [action.payload, ...state.notifications] };
    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};