import { Request, Response } from 'express';
import { User } from '../models/user.model';

// Get all users (admin only)
export const getUsers = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      role,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter: any = {};

    if (search) {
      filter.$or = [
        { name: new RegExp(search as string, 'i') },
        { email: new RegExp(search as string, 'i') }
      ];
    }

    if (role) filter.roles = role;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    // Build sort object
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (Number(page) - 1) * Number(limit);
    
    const [users, total] = await Promise.all([
      User.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .select('-password -security'),
      User.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / Number(limit));

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: Number(page),
          totalPages,
          totalItems: total,
          itemsPerPage: Number(limit)
        }
      }
    });
  } catch (error: any) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
};

// Get user by ID
export const getUserById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id).select('-password -security');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if requesting user can view this profile
    if (req.user && (req.user._id.toString() === id || req.user.roles.includes('admin'))) {
      // Return full profile for own profile or admin
      res.json({
        success: true,
        data: { user: user.fullProfile }
      });
    } else {
      // Return public profile only
      res.json({
        success: true,
        data: {
          user: {
            id: user._id,
            name: user.name,
            avatar: user.avatar,
            level: user.level,
            points: user.points,
            profile: {
              bio: user.profile.bio,
              skills: user.profile.skills,
              interests: user.profile.interests
            },
            stats: user.stats,
            createdAt: user.createdAt
          }
        }
      });
    }
  } catch (error: any) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: error.message
    });
  }
};

// Update user role (admin only)
export const updateUserRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { roles } = req.body;

    if (!req.user || !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.roles = roles;
    await user.save();

    res.json({
      success: true,
      message: 'User role updated successfully',
      data: { user: user.fullProfile }
    });
  } catch (error: any) {
    console.error('Update user role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user role',
      error: error.message
    });
  }
};

// Deactivate user (admin only)
export const deactivateUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!req.user || !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.isActive = false;
    await user.save();

    res.json({
      success: true,
      message: 'User deactivated successfully'
    });
  } catch (error: any) {
    console.error('Deactivate user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to deactivate user',
      error: error.message
    });
  }
};

// Get user statistics
export const getUserStats = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userId = req.params.id || req.user._id;

    // Check if user can view these stats
    if (userId !== req.user._id.toString() && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view these statistics'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate additional stats
    const stats = {
      ...user.stats,
      level: user.level,
      points: user.points,
      balance: user.balance,
      joinDate: user.createdAt,
      lastActive: user.activity.lastActive,
      subscriptions: user.subscriptions
    };

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error: any) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user statistics',
      error: error.message
    });
  }
};

// Follow/Unfollow user
export const toggleFollowUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (id === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot follow yourself'
      });
    }

    const targetUser = await User.findById(id);
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // In a real app, you'd have a separate followers/following collection
    // For now, we'll just return a success message
    res.json({
      success: true,
      message: 'Follow status updated successfully'
    });
  } catch (error: any) {
    console.error('Toggle follow user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update follow status',
      error: error.message
    });
  }
};

// Get user activity feed
export const getUserActivity = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userId = id || req.user._id;

    // Check if user can view this activity
    if (userId !== req.user._id.toString() && !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this activity'
      });
    }

    // In a real app, you'd have an activity/feed collection
    // For now, we'll return mock data
    const activities = [
      {
        id: '1',
        type: 'course_completed',
        description: 'Completed AI & Machine Learning Bootcamp',
        timestamp: new Date(),
        metadata: { courseId: '123', courseName: 'AI Bootcamp' }
      },
      {
        id: '2',
        type: 'nft_created',
        description: 'Minted new NFT: Digital Art #001',
        timestamp: new Date(Date.now() - 86400000),
        metadata: { nftId: '456', nftName: 'Digital Art #001' }
      }
    ];

    res.json({
      success: true,
      data: {
        activities,
        pagination: {
          currentPage: Number(page),
          totalPages: 1,
          totalItems: activities.length,
          itemsPerPage: Number(limit)
        }
      }
    });
  } catch (error: any) {
    console.error('Get user activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user activity',
      error: error.message
    });
  }
};
