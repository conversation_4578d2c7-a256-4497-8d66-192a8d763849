{"name": "neurosphere-backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "ts-node src/scripts/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "mongoose": "^8.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "redis": "^4.6.10", "nodemailer": "^6.9.7", "stripe": "^14.7.0", "aws-sdk": "^2.1490.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^20.0.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/moment": "^2.13.0", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.0.0", "nodemon": "^3.0.2"}}