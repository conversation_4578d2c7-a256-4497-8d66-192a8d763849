import React, { useState, useEffect, useRef } from 'react';
import { Brain, MessageCircle, Mic, MicOff, Volume2, VolumeX, Settings, Zap, Sparkles, Eye, Heart, Target, Move } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  emotion?: string;
  confidence?: number;
}

const AIAssistant: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m <PERSON>, your quantum-conscious AI companion. I can help you with anything across the NeuroSphere platform. How can I assist you today?',
      timestamp: new Date(),
      emotion: 'friendly',
      confidence: 99
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [aiPersonality, setAiPersonality] = useState('helpful');

  // 拖拽相关
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: window.innerWidth - 420, y: window.innerHeight - 300 });
  const [dragging, setDragging] = useState(false);
  const [offset, setOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const personalities = [
    { id: 'helpful', name: 'Helpful Assistant', icon: Heart },
    { id: 'creative', name: 'Creative Genius', icon: Sparkles },
    { id: 'analytical', name: 'Data Analyst', icon: Target },
    { id: 'empathetic', name: 'Emotional Support', icon: Eye }
  ];

  const { t, i18n } = useTranslation();

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: generateAIResponse(inputMessage),
        timestamp: new Date(),
        emotion: 'thoughtful',
        confidence: Math.floor(Math.random() * 20) + 80
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const generateAIResponse = (input: string): string => {
    const responses = [
      "I understand your request. Let me analyze the quantum probability matrices to provide the optimal solution.",
      "Based on my neural network analysis, I recommend exploring the metaverse opportunities in your area of interest.",
      "Your consciousness patterns suggest you're in a creative state. Perfect time for AI-assisted content generation!",
      "I've processed 10^12 data points to give you this personalized recommendation tailored to your neural profile.",
      "Fascinating question! My quantum consciousness algorithms are computing multiple dimensional solutions for you."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const toggleVoiceInput = () => {
    setIsListening(!isListening);
    // Voice recognition logic would go here
  };

  const toggleSpeech = () => {
    setIsSpeaking(!isSpeaking);
    // Text-to-speech logic would go here
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragging) return;
      const newX = e.clientX - offset.x;
      const newY = e.clientY - offset.y;
      // 边界约束
      const width = containerRef.current?.offsetWidth || 380;
      const height = containerRef.current?.offsetHeight || 380;
      const maxX = window.innerWidth - width - 8;
      const maxY = window.innerHeight - height - 8;
      setPosition({
        x: Math.max(8, Math.min(newX, maxX)),
        y: Math.max(8, Math.min(newY, maxY)),
      });
    };
    const handleMouseUp = () => setDragging(false);
    if (dragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragging, offset]);

  return (
    <div
      ref={containerRef}
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 50,
        cursor: dragging ? 'grabbing' : 'default',
        userSelect: dragging ? 'none' : 'auto',
      }}
    >
      <div className="bg-gradient-to-br from-purple-600/90 to-blue-600/90 backdrop-blur-md border border-white/20 rounded-2xl p-6 w-96 max-h-96 flex flex-col">
        {/* Header - 拖拽手柄 */}
        <div
          className="flex items-center justify-between mb-4 cursor-move select-none"
          style={{ cursor: dragging ? 'grabbing' : 'grab' }}
          onMouseDown={e => {
            setDragging(true);
            const rect = containerRef.current?.getBoundingClientRect();
            setOffset({ x: e.clientX - (rect?.left || 0), y: e.clientY - (rect?.top || 0) });
          }}
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Move className="h-5 w-5 text-white/80 mr-1" />
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-white font-semibold">{t('aria_title')}</h3>
              <p className="text-white/70 text-xs">{t('aria_subtitle')}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleVoiceInput}
              className={`p-2 rounded-lg transition-colors ${
                isListening ? 'bg-red-500 text-white' : 'bg-white/20 text-white/80 hover:text-white'
              }`}
            >
              {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </button>
            <button
              onClick={toggleSpeech}
              className={`p-2 rounded-lg transition-colors ${
                isSpeaking ? 'bg-green-500 text-white' : 'bg-white/20 text-white/80 hover:text-white'
              }`}
            >
              {isSpeaking ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </button>
            <button className="p-2 bg-white/20 text-white/80 hover:text-white rounded-lg transition-colors">
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto space-y-3 mb-4 max-h-48">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white/20 text-white'
                }`}
              >
                <p className="text-sm">{message.content}</p>
                {message.type === 'ai' && message.confidence && (
                  <div className="flex items-center justify-between mt-2 text-xs text-white/60">
                    <span>Confidence: {message.confidence}%</span>
                    <span className="capitalize">{message.emotion}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Personality Selector */}
        <div className="flex space-x-2 mb-3">
          {personalities.map((personality) => (
            <button
              key={personality.id}
              onClick={() => setAiPersonality(personality.id)}
              className={`flex-1 p-2 rounded-lg text-xs transition-colors ${
                aiPersonality === personality.id
                  ? 'bg-purple-500 text-white'
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              <personality.icon className="h-3 w-3 mx-auto mb-1" />
              <div>{personality.name.split(' ')[0]}</div>
            </button>
          ))}
        </div>

        {/* Input */}
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            placeholder={t('ask_me_anything')}
            className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
          />
          <button
            onClick={sendMessage}
            className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-colors"
          >
            <MessageCircle className="h-4 w-4" />
          </button>
        </div>

        <div className="flex justify-end mb-2 space-x-2">
          <button onClick={() => i18n.changeLanguage('en')} className="px-2 py-1 rounded text-xs bg-white/10 text-white hover:bg-white/20">EN</button>
          <button onClick={() => i18n.changeLanguage('zh')} className="px-2 py-1 rounded text-xs bg-white/10 text-white hover:bg-white/20">中文</button>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;