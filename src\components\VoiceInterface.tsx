import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, MicOff, Volume2, VolumeX, Setting<PERSON>, <PERSON>, Waves, Move } from 'lucide-react';
import { useTranslation } from 'react-i18next';

// 语音命令类型
interface VoiceCommand {
  command: string;
  action: string;
  confidence: number;
  timestamp: Date;
}

const VoiceInterface: React.FC = () => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [voiceLevel, setVoiceLevel] = useState(0);
  const [recentCommands, setRecentCommands] = useState<VoiceCommand[]>([]);
  const [voiceSettings] = useState({
    language: 'en-US',
    voice: 'neural-female',
    speed: 1.0,
    pitch: 1.0,
    sensitivity: 0.7
  });

  // 拖拽相关
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: window.innerWidth - 350, y: window.innerHeight - 400 });
  const [dragging, setDragging] = useState(false);
  const [offset, setOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // 可用语音命令
  const availableCommands = [
    { phrase: "Open AI Studio", action: "navigate_ai_studio" },
    { phrase: "Show my portfolio", action: "navigate_portfolio" },
    { phrase: "Start neural training", action: "start_training" },
    { phrase: "Create new content", action: "create_content" },
    { phrase: "Check notifications", action: "show_notifications" },
    { phrase: "Enter metaverse", action: "enter_metaverse" },
    { phrase: "Activate quantum mode", action: "quantum_mode" },
    { phrase: "Show brain activity", action: "show_brainwaves" }
  ];

  const { t, i18n } = useTranslation();

  // 拖拽事件
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragging) return;
      const newX = e.clientX - offset.x;
      const newY = e.clientY - offset.y;
      const width = containerRef.current?.offsetWidth || 320;
      const height = containerRef.current?.offsetHeight || 320;
      const maxX = window.innerWidth - width - 8;
      const maxY = window.innerHeight - height - 8;
      setPosition({
        x: Math.max(8, Math.min(newX, maxX)),
        y: Math.max(8, Math.min(newY, maxY)),
      });
    };
    const handleMouseUp = () => setDragging(false);
    if (dragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragging, offset]);

  // 语音可视化模拟
  useEffect(() => {
    if (isListening) {
      const interval = setInterval(() => {
        setVoiceLevel(Math.random() * 100);
      }, 100);
      return () => clearInterval(interval);
    } else {
      setVoiceLevel(0);
    }
  }, [isListening]);

  // 开始/停止监听
  const startListening = () => {
    setIsListening(true);
    // 模拟识别命令
    setTimeout(() => {
      const randomCommand = availableCommands[Math.floor(Math.random() * availableCommands.length)];
      const newCommand: VoiceCommand = {
        command: randomCommand.phrase,
        action: randomCommand.action,
        confidence: Math.floor(Math.random() * 20) + 80,
        timestamp: new Date()
      };
      setRecentCommands(prev => [newCommand, ...prev.slice(0, 4)]);
      setIsListening(false);
      executeVoiceCommand(newCommand);
    }, 3000);
  };
  const stopListening = () => setIsListening(false);

  // 执行命令（mock）
  const executeVoiceCommand = (command: VoiceCommand) => {
    // 这里可对接真实命令执行
    speak(`Executing ${command.command}`);
  };

  // 语音输出（mock）
  const speak = (text: string) => {
    setIsSpeaking(true);
    setTimeout(() => {
      setIsSpeaking(false);
    }, 2000);
  };

  // 静音切换（mock）
  const toggleMute = () => {};

  return (
    <div
      ref={containerRef}
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 40,
        cursor: dragging ? 'grabbing' : 'default',
        userSelect: dragging ? 'none' : 'auto',
      }}
    >
      <div className="bg-gradient-to-br from-blue-600/90 to-purple-600/90 backdrop-blur-md border border-white/20 rounded-2xl p-4 w-80">
        {/* Header - 拖拽手柄 */}
        <div
          className="flex items-center justify-between mb-4 cursor-move select-none"
          style={{ cursor: dragging ? 'grabbing' : 'grab' }}
          onMouseDown={e => {
            setDragging(true);
            const rect = containerRef.current?.getBoundingClientRect();
            setOffset({ x: e.clientX - (rect?.left || 0), y: e.clientY - (rect?.top || 0) });
          }}
        >
          <div className="flex items-center space-x-2">
            <Move className="h-5 w-5 text-white/80 mr-1" />
            <Brain className="h-5 w-5 text-white" />
            <span className="text-white font-semibold">{t('voice_interface')}</span>
          </div>
          <button className="p-1 text-white/60 hover:text-white transition-colors">
            <Settings className="h-4 w-4" />
          </button>
        </div>

        {/* Voice Visualization */}
        <div className="mb-4">
          <div className="h-16 bg-black/40 rounded-lg p-2 flex items-center justify-center">
            {isListening ? (
              <div className="flex items-center space-x-1">
                {Array.from({ length: 20 }).map((_, i) => (
                  <div
                    key={i}
                    className="w-1 bg-gradient-to-t from-blue-500 to-purple-500 rounded-full transition-all duration-100"
                    style={{
                      height: `${Math.max(4, (Math.sin(Date.now() * 0.01 + i) + 1) * voiceLevel * 0.3)}px`
                    }}
                  />
                ))}
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-white/60">
                <Waves className="h-6 w-6" />
                <span className="text-sm">{t('ready_to_listen')}</span>
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center space-x-4 mb-4">
          <button
            onClick={isListening ? stopListening : startListening}
            className={`p-4 rounded-full transition-all duration-200 ${
              isListening
                ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
            title={isListening ? '停止监听' : '开始监听'}
          >
            {isListening ? <MicOff className="h-6 w-6" /> : <Mic className="h-6 w-6" />}
          </button>
          <button
            onClick={toggleMute}
            className={`p-3 rounded-full transition-colors ${
              isSpeaking
                ? 'bg-green-500 text-white'
                : 'bg-white/20 text-white/80 hover:text-white'
            }`}
            title="静音"
          >
            {isSpeaking ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
          </button>
        </div>

        {/* Status */}
        <div className="text-center mb-4">
          <div className="text-white text-sm">
            {isListening ? t('listening') : isSpeaking ? t('speaking') : t('ready')}
          </div>
          {isListening && (
            <div className="text-white/60 text-xs mt-1">
              Say a command or ask a question
            </div>
          )}
        </div>

        {/* Recent Commands */}
        {recentCommands.length > 0 && (
          <div>
            <h4 className="text-white text-sm font-medium mb-2">{t('recent_commands')}</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {recentCommands.map((command, index) => (
                <div key={index} className="bg-white/10 rounded-lg p-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white text-xs">{command.command}</span>
                    <span className="text-green-400 text-xs">{command.confidence}%</span>
                  </div>
                  <div className="text-white/60 text-xs">
                    {command.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Commands */}
        <div className="mt-4">
          <h4 className="text-white text-sm font-medium mb-2">{t('quick_commands')}</h4>
          <div className="grid grid-cols-2 gap-2">
            {availableCommands.slice(0, 4).map((cmd, index) => (
              <button
                key={index}
                onClick={() => executeVoiceCommand({
                  command: cmd.phrase,
                  action: cmd.action,
                  confidence: 100,
                  timestamp: new Date()
                })}
                className="bg-white/10 hover:bg-white/20 text-white text-xs p-2 rounded-lg transition-colors"
              >
                {cmd.phrase}
              </button>
            ))}
          </div>
        </div>

        <div className="flex justify-end mb-2 space-x-2">
          <button onClick={() => i18n.changeLanguage('en')} className="px-2 py-1 rounded text-xs bg-white/10 text-white hover:bg-white/20">EN</button>
          <button onClick={() => i18n.changeLanguage('zh')} className="px-2 py-1 rounded text-xs bg-white/10 text-white hover:bg-white/20">中文</button>
        </div>
      </div>
    </div>
  );
};

export default VoiceInterface;