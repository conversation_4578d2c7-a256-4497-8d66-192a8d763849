# NeuroSphere Backend API

A comprehensive backend API for the NeuroSphere super app platform, supporting social commerce, learning, NFTs, and more.

## 🚀 Features

- **User Authentication & Authorization** - JWT-based auth with role-based access control
- **Social Commerce** - Product management, orders, reviews, and live streaming
- **Learning Platform** - Course creation, enrollment, and progress tracking
- **NFT Marketplace** - NFT minting, trading, and collection management
- **Analytics Dashboard** - Comprehensive analytics for users and admins
- **Real-time Features** - WebSocket support for live interactions
- **Security** - Rate limiting, input validation, and data encryption

## 🛠️ Tech Stack

- **Runtime**: Node.js + TypeScript
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Cache**: Redis
- **Authentication**: JWT
- **Validation**: Joi
- **Security**: Helmet, CORS, Rate Limiting
- **File Upload**: Multer
- **Email**: <PERSON>demailer
- **Payments**: Stripe integration

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB and Redis**
   ```bash
   # MongoDB (if running locally)
   mongod
   
   # Redis (if running locally)
   redis-server
   ```

5. **Run database migrations/seed data**
   ```bash
   npm run seed
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

## 🔧 Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Server
PORT=4000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/neurosphere
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password

# AWS (for file uploads)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=your-bucket-name

# Stripe
STRIPE_SECRET_KEY=sk_test_your-stripe-key
```

## 📚 API Documentation

### Authentication Endpoints

```
POST /api/auth/register     - Register new user
POST /api/auth/login        - User login
POST /api/auth/logout       - User logout
GET  /api/auth/profile      - Get user profile
PUT  /api/auth/profile      - Update user profile
PUT  /api/auth/change-password - Change password
```

### Product Endpoints

```
GET    /api/products        - Get all products (with filters)
GET    /api/products/:id    - Get single product
POST   /api/products        - Create product (creator only)
PUT    /api/products/:id    - Update product (owner only)
DELETE /api/products/:id    - Delete product (owner only)
POST   /api/products/:id/reviews - Add product review
```

### Course Endpoints

```
GET    /api/courses         - Get all courses (with filters)
GET    /api/courses/:id     - Get single course
POST   /api/courses         - Create course (creator only)
PUT    /api/courses/:id     - Update course (owner only)
DELETE /api/courses/:id     - Delete course (owner only)
POST   /api/courses/:id/enroll - Enroll in course
POST   /api/courses/:id/reviews - Add course review
```

### Order Endpoints

```
GET    /api/orders          - Get user orders
GET    /api/orders/:id      - Get single order
POST   /api/orders          - Create new order
PUT    /api/orders/:id/cancel - Cancel order
PUT    /api/orders/:id/status - Update order status (admin)
```

### NFT Endpoints

```
GET    /api/nfts            - Get all NFTs (with filters)
GET    /api/nfts/:id        - Get single NFT
POST   /api/nfts            - Mint new NFT (creator only)
PUT    /api/nfts/:id        - Update NFT (owner only)
POST   /api/nfts/:id/transfer - Transfer NFT ownership
POST   /api/nfts/:id/like   - Like/unlike NFT
```

### User Endpoints

```
GET    /api/users           - Get all users (admin only)
GET    /api/users/:id       - Get user profile
GET    /api/users/:id/stats - Get user statistics
GET    /api/users/:id/activity - Get user activity
POST   /api/users/:id/follow - Follow/unfollow user
PUT    /api/users/:id/role  - Update user role (admin)
PUT    /api/users/:id/deactivate - Deactivate user (admin)
```

### Analytics Endpoints

```
GET    /api/analytics/dashboard - Get user dashboard
GET    /api/analytics/sales     - Get sales analytics
GET    /api/analytics/courses   - Get course analytics
GET    /api/analytics/nfts      - Get NFT analytics
GET    /api/analytics/platform  - Get platform stats (admin)
```

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### User Roles

- **user**: Basic user with standard permissions
- **creator**: Can create products, courses, and NFTs
- **moderator**: Can moderate content
- **admin**: Full access to all features

## 📊 Response Format

All API responses follow this format:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data here
  }
}
```

Error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    // Validation errors (if any)
  ]
}
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📝 Scripts

```bash
npm run dev        # Start development server
npm run build      # Build for production
npm run start      # Start production server
npm run seed       # Seed database with sample data
npm test           # Run tests
npm run lint       # Run ESLint
```

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Set production environment variables**

3. **Start the production server**
   ```bash
   npm start
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
