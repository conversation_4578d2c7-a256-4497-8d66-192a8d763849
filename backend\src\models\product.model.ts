import mongoose, { Document, Schema } from 'mongoose';

export interface IProduct extends Document {
  _id: string;
  name: string;
  description: string;
  price: number;
  originalPrice: number;
  currency: string;
  images: string[];
  videos?: string[];
  category: string;
  subcategory: string;
  brand: string;
  seller: {
    id: string;
    name: string;
    avatar: string;
    rating: number;
    verified: boolean;
  };
  specifications: Array<{
    key: string;
    value: string;
  }>;
  variants: Array<{
    id: string;
    name: string;
    price: number;
    stock: number;
    attributes: Record<string, string>;
  }>;
  inventory: {
    stock: number;
    reserved: number;
    sold: number;
    sku: string;
  };
  shipping: {
    weight: number;
    dimensions: {
      length: number;
      width: number;
      height: number;
    };
    freeShipping: boolean;
    shippingCost: number;
    estimatedDelivery: string;
  };
  rating: {
    average: number;
    count: number;
    distribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
  };
  reviews: Array<{
    userId: string;
    userName: string;
    userAvatar: string;
    rating: number;
    comment: string;
    images?: string[];
    verified: boolean;
    helpful: number;
    createdAt: Date;
  }>;
  liveStream: {
    isLive: boolean;
    streamId?: string;
    viewers: number;
    scheduledAt?: Date;
    streamUrl?: string;
  };
  tags: string[];
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
  };
  status: 'draft' | 'active' | 'inactive' | 'out_of_stock';
  featured: boolean;
  trending: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const productSchema = new Schema<IProduct>({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Name cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Product description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  price: {
    type: Number,
    required: [true, 'Product price is required'],
    min: [0, 'Price cannot be negative']
  },
  originalPrice: {
    type: Number,
    required: [true, 'Original price is required'],
    min: [0, 'Original price cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD']
  },
  images: [{
    type: String,
    required: true
  }],
  videos: [String],
  category: {
    type: String,
    required: [true, 'Product category is required'],
    enum: ['electronics', 'fashion', 'home', 'beauty', 'sports', 'books', 'toys', 'automotive']
  },
  subcategory: String,
  brand: {
    type: String,
    required: [true, 'Brand is required']
  },
  seller: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: { type: String, required: true },
    avatar: String,
    rating: { type: Number, default: 0, min: 0, max: 5 },
    verified: { type: Boolean, default: false }
  },
  specifications: [{
    key: { type: String, required: true },
    value: { type: String, required: true }
  }],
  variants: [{
    id: { type: String, required: true },
    name: { type: String, required: true },
    price: { type: Number, required: true },
    stock: { type: Number, required: true },
    attributes: { type: Map, of: String }
  }],
  inventory: {
    stock: { type: Number, required: true, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    sku: { type: String, required: true, unique: true }
  },
  shipping: {
    weight: { type: Number, required: true },
    dimensions: {
      length: { type: Number, required: true },
      width: { type: Number, required: true },
      height: { type: Number, required: true }
    },
    freeShipping: { type: Boolean, default: false },
    shippingCost: { type: Number, default: 0 },
    estimatedDelivery: String
  },
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 },
    distribution: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 }
    }
  },
  reviews: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    userName: String,
    userAvatar: String,
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    images: [String],
    verified: { type: Boolean, default: false },
    helpful: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now }
  }],
  liveStream: {
    isLive: { type: Boolean, default: false },
    streamId: String,
    viewers: { type: Number, default: 0 },
    scheduledAt: Date,
    streamUrl: String
  },
  tags: [String],
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String]
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'inactive', 'out_of_stock'],
    default: 'draft'
  },
  featured: { type: Boolean, default: false },
  trending: { type: Boolean, default: false }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
productSchema.index({ category: 1, status: 1 });
productSchema.index({ 'rating.average': -1 });
productSchema.index({ price: 1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ featured: -1, trending: -1 });
productSchema.index({ 'seller.id': 1 });
productSchema.index({ 'inventory.sku': 1 });

// Virtual for discount percentage
productSchema.virtual('discountPercentage').get(function() {
  if (this.originalPrice > this.price) {
    return Math.round((1 - this.price / this.originalPrice) * 100);
  }
  return 0;
});

// Virtual for availability
productSchema.virtual('isAvailable').get(function() {
  return this.status === 'active' && this.inventory.stock > this.inventory.reserved;
});

export const Product = mongoose.model<IProduct>('Product', productSchema);