import React, { useState, useEffect } from 'react';
import { Coins, TrendingUp, Shield, Zap, Globe, Users, Lock, Unlock, ArrowUpRight, ArrowDownLeft, BarChart3, PieChart, Activity, Star, Crown, Gem, Flame, CloudLightning as Lightning, Infinity, Target, Rocket, Brain } from 'lucide-react';
import { useApp } from '../context/AppContext';

const BlockchainEcosystem: React.FC = () => {
  const [activeTab, setActiveTab] = useState('defi');
  const [stakingAmount, setStakingAmount] = useState('');
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [selectedProtocol, setSelectedProtocol] = useState<any>(null);
  const [showProtocolModal, setShowProtocolModal] = useState(false);
  const [cryptoPrices, setCryptoPrices] = useState({
    NEURO: 847.23,
    BTC: 67234.56,
    ETH: 3456.78,
    SOL: 234.56
  });
  const { state, dispatch } = useApp();

  useEffect(() => {
    const interval = setInterval(() => {
      setCryptoPrices(prev => ({
        NEURO: prev.NEURO + (Math.random() - 0.5) * 10,
        BTC: prev.BTC + (Math.random() - 0.5) * 100,
        ETH: prev.ETH + (Math.random() - 0.5) * 50,
        SOL: prev.SOL + (Math.random() - 0.5) * 5
      }));
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const connectWallet = async (walletType: string) => {
    // Simulate wallet connection
    setTimeout(() => {
      setIsWalletConnected(true);
      setWalletAddress('******************************************');
      setShowWalletModal(false);
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: `${walletType} wallet connected successfully!`,
          type: 'success'
        }
      });
    }, 1500);
  };

  const disconnectWallet = () => {
    setIsWalletConnected(false);
    setWalletAddress('');
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: 'Wallet disconnected',
        type: 'info'
      }
    });
  };

  const openProtocol = (protocol: any) => {
    if (!isWalletConnected) {
      setShowWalletModal(true);
      return;
    }
    setSelectedProtocol(protocol);
    setShowProtocolModal(true);
  };

  const stakeTokens = () => {
    if (!stakingAmount || !isWalletConnected) return;

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: `Successfully staked ${stakingAmount} NEURO tokens!`,
        type: 'success'
      }
    });
    setStakingAmount('');
  };

  const tabs = [
    { id: 'defi', name: 'DeFi Hub', icon: Coins },
    { id: 'nft', name: 'NFT Marketplace', icon: Gem },
    { id: 'dao', name: 'DAO Governance', icon: Users },
    { id: 'staking', name: 'Staking Pools', icon: Shield },
    { id: 'trading', name: 'DEX Trading', icon: TrendingUp },
    { id: 'launchpad', name: 'Launchpad', icon: Rocket }
  ];

  const defiProtocols = [
    {
      id: '1',
      name: 'NeuroSwap',
      type: 'DEX',
      tvl: '$2.8B',
      apy: '247%',
      icon: Zap,
      color: 'from-blue-500 to-cyan-500',
      description: 'AI-powered automated market maker'
    },
    {
      id: '2',
      name: 'QuantumLend',
      type: 'Lending',
      tvl: '$1.9B',
      apy: '156%',
      icon: Shield,
      color: 'from-green-500 to-emerald-500',
      description: 'Quantum-secured lending protocol'
    },
    {
      id: '3',
      name: 'ConsciousYield',
      type: 'Yield Farming',
      tvl: '$3.4B',
      apy: '389%',
      icon: Brain,
      color: 'from-purple-500 to-pink-500',
      description: 'AI consciousness-driven yield optimization'
    },
    {
      id: '4',
      name: 'MetaVault',
      type: 'Asset Management',
      tvl: '$5.2B',
      apy: '198%',
      icon: Crown,
      color: 'from-orange-500 to-red-500',
      description: 'Cross-dimensional asset management'
    }
  ];

  const nftCollections = [
    {
      id: '1',
      name: 'Quantum Consciousness',
      floor: '12.5 ETH',
      volume: '2,847 ETH',
      items: 10000,
      owners: 3456,
      image: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=300'
    },
    {
      id: '2',
      name: 'Neural Avatars',
      floor: '8.9 ETH',
      volume: '1,923 ETH',
      items: 7777,
      owners: 2891,
      image: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=300'
    },
    {
      id: '3',
      name: 'Metaverse Artifacts',
      floor: '15.7 ETH',
      volume: '4,156 ETH',
      items: 5000,
      owners: 1847,
      image: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=300'
    }
  ];

  const stakingPools = [
    {
      id: '1',
      token: 'NEURO',
      apy: '247%',
      tvl: '$847M',
      lockPeriod: '30 days',
      rewards: 'NEURO + NFTs',
      risk: 'Low'
    },
    {
      id: '2',
      token: 'QUANTUM',
      apy: '389%',
      tvl: '$1.2B',
      lockPeriod: '90 days',
      rewards: 'QUANTUM + Governance',
      risk: 'Medium'
    },
    {
      id: '3',
      token: 'CONSCIOUSNESS',
      apy: '567%',
      tvl: '$2.1B',
      lockPeriod: '365 days',
      rewards: 'CONSCIOUSNESS + AI Access',
      risk: 'High'
    }
  ];

  const daoProposals = [
    {
      id: '1',
      title: 'Implement Quantum Computing Integration',
      description: 'Proposal to integrate quantum computing capabilities into the platform',
      votes: { for: 2847293, against: 156789 },
      status: 'Active',
      timeLeft: '5 days'
    },
    {
      id: '2',
      title: 'Launch Consciousness AI Research Fund',
      description: 'Allocate $100M for AI consciousness research and development',
      votes: { for: 1923847, against: 234567 },
      status: 'Passed',
      timeLeft: 'Completed'
    },
    {
      id: '3',
      title: 'Expand Metaverse Infrastructure',
      description: 'Build next-generation metaverse infrastructure across 50 virtual worlds',
      votes: { for: 3456789, against: 89234 },
      status: 'Active',
      timeLeft: '12 days'
    }
  ];

  const launchpadProjects = [
    {
      id: '1',
      name: 'QuantumNet',
      description: 'Decentralized quantum internet protocol',
      raised: '$45M',
      target: '$50M',
      price: '$0.25',
      allocation: '2M tokens',
      status: 'Live'
    },
    {
      id: '2',
      name: 'ConsciousDAO',
      description: 'AI consciousness governance protocol',
      raised: '$78M',
      target: '$80M',
      price: '$1.50',
      allocation: '500K tokens',
      status: 'Upcoming'
    },
    {
      id: '3',
      name: 'MetaReality',
      description: 'Cross-dimensional reality synthesis platform',
      raised: '$120M',
      target: '$100M',
      price: '$5.00',
      allocation: '100K tokens',
      status: 'Completed'
    }
  ];

  const renderDeFi = () => (
    <div className="space-y-8">
      {/* DeFi Overview */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">DeFi Ecosystem Overview</h3>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">$12.8B</div>
            <div className="text-white/60">Total Value Locked</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">247%</div>
            <div className="text-white/60">Average APY</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">2.3M</div>
            <div className="text-white/60">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">156</div>
            <div className="text-white/60">Protocols</div>
          </div>
        </div>
      </div>

      {/* DeFi Protocols */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Featured Protocols</h4>
        <div className="grid md:grid-cols-2 gap-6">
          {defiProtocols.map((protocol) => (
            <div key={protocol.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:from-white/15 hover:to-white/10 transition-all duration-300">
              <div className="flex items-start space-x-4">
                <div className={`p-4 rounded-2xl bg-gradient-to-r ${protocol.color}`}>
                  <protocol.icon className="h-8 w-8 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="text-xl font-semibold text-white">{protocol.name}</h5>
                    <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                      {protocol.type}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm mb-4">{protocol.description}</p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-white/60">TVL</div>
                      <div className="text-green-400 font-semibold">{protocol.tvl}</div>
                    </div>
                    <div>
                      <div className="text-white/60">APY</div>
                      <div className="text-purple-400 font-semibold">{protocol.apy}</div>
                    </div>
                  </div>
                  <button
                    onClick={() => openProtocol(protocol)}
                    className="w-full mt-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors"
                  >
                    Enter Protocol
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Live Prices */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Live Prices</h4>
        <div className="grid md:grid-cols-4 gap-6">
          {Object.entries(cryptoPrices).map(([symbol, price]) => (
            <div key={symbol} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
              <div className="text-lg font-semibold text-white mb-2">{symbol}</div>
              <div className="text-2xl font-bold text-green-400 mb-2">${price.toFixed(2)}</div>
              <div className="text-green-400 text-sm">+{(Math.random() * 10).toFixed(2)}%</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderNFT = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">NFT Marketplace</h3>
        <p className="text-white/70 text-lg">Discover and trade unique digital assets</p>
      </div>

      {/* NFT Collections */}
      <div className="grid md:grid-cols-3 gap-6">
        {nftCollections.map((collection) => (
          <div key={collection.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
            <img
              src={collection.image}
              alt={collection.name}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h4 className="text-lg font-semibold text-white mb-4">{collection.name}</h4>
              <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                <div>
                  <div className="text-white/60">Floor Price</div>
                  <div className="text-green-400 font-semibold">{collection.floor}</div>
                </div>
                <div>
                  <div className="text-white/60">Volume</div>
                  <div className="text-blue-400 font-semibold">{collection.volume}</div>
                </div>
                <div>
                  <div className="text-white/60">Items</div>
                  <div className="text-white font-semibold">{collection.items.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-white/60">Owners</div>
                  <div className="text-purple-400 font-semibold">{collection.owners.toLocaleString()}</div>
                </div>
              </div>
              <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors">
                View Collection
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* NFT Statistics */}
      <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Marketplace Statistics</h4>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">847K</div>
            <div className="text-white/60">Total NFTs</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">$2.8B</div>
            <div className="text-white/60">Total Volume</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-pink-400 mb-2">156K</div>
            <div className="text-white/60">Active Traders</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">23.4 ETH</div>
            <div className="text-white/60">Avg. Sale Price</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDAO = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">DAO Governance</h3>
        <p className="text-white/70 text-lg">Shape the future of NeuroSphere through decentralized governance</p>
      </div>

      {/* Governance Stats */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">12.4M</div>
            <div className="text-white/60">Total Votes Cast</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">847K</div>
            <div className="text-white/60">Active Voters</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">156</div>
            <div className="text-white/60">Proposals</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">89%</div>
            <div className="text-white/60">Participation Rate</div>
          </div>
        </div>
      </div>

      {/* Active Proposals */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Active Proposals</h4>
        <div className="space-y-6">
          {daoProposals.map((proposal) => (
            <div key={proposal.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h5 className="text-lg font-semibold text-white">{proposal.title}</h5>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      proposal.status === 'Active' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-blue-500/20 text-blue-400'
                    }`}>
                      {proposal.status}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm mb-4">{proposal.description}</p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-white/60">For: {proposal.votes.for.toLocaleString()}</div>
                      <div className="w-full bg-white/20 rounded-full h-2 mt-1">
                        <div 
                          className="bg-green-500 h-2 rounded-full"
                          style={{ 
                            width: `${(proposal.votes.for / (proposal.votes.for + proposal.votes.against)) * 100}%` 
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <div className="text-white/60">Against: {proposal.votes.against.toLocaleString()}</div>
                      <div className="w-full bg-white/20 rounded-full h-2 mt-1">
                        <div 
                          className="bg-red-500 h-2 rounded-full"
                          style={{ 
                            width: `${(proposal.votes.against / (proposal.votes.for + proposal.votes.against)) * 100}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="lg:w-48 text-center">
                  <div className="text-white/60 text-sm mb-2">Time Left</div>
                  <div className="text-white font-semibold mb-4">{proposal.timeLeft}</div>
                  {proposal.status === 'Active' && (
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg transition-colors text-sm">
                        Vote For
                      </button>
                      <button className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg transition-colors text-sm">
                        Vote Against
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderStaking = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Staking Pools</h3>
        <p className="text-white/70 text-lg">Stake your tokens and earn rewards</p>
      </div>

      {/* Staking Interface */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Stake NEURO Tokens</h4>
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <label className="block text-white/70 text-sm mb-2">Amount to Stake</label>
            <input
              type="number"
              value={stakingAmount}
              onChange={(e) => setStakingAmount(e.target.value)}
              placeholder="Enter amount..."
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
            <div className="text-center">
              <div className="text-white/60">Estimated APY</div>
              <div className="text-green-400 font-semibold text-lg">247%</div>
            </div>
            <div className="text-center">
              <div className="text-white/60">Lock Period</div>
              <div className="text-blue-400 font-semibold text-lg">30 days</div>
            </div>
          </div>
          <button
            onClick={stakeTokens}
            disabled={!isWalletConnected || !stakingAmount}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isWalletConnected ? 'Stake Tokens' : 'Connect Wallet to Stake'}
          </button>
        </div>
      </div>

      {/* Staking Pools */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Available Pools</h4>
        <div className="grid md:grid-cols-3 gap-6">
          {stakingPools.map((pool) => (
            <div key={pool.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="text-center mb-4">
                <h5 className="text-xl font-semibold text-white mb-2">{pool.token}</h5>
                <div className="text-3xl font-bold text-green-400 mb-1">{pool.apy}</div>
                <div className="text-white/60 text-sm">Annual Percentage Yield</div>
              </div>
              <div className="space-y-3 mb-6 text-sm">
                <div className="flex justify-between">
                  <span className="text-white/60">TVL</span>
                  <span className="text-blue-400 font-semibold">{pool.tvl}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Lock Period</span>
                  <span className="text-purple-400 font-semibold">{pool.lockPeriod}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Rewards</span>
                  <span className="text-orange-400 font-semibold">{pool.rewards}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Risk Level</span>
                  <span className={`font-semibold ${
                    pool.risk === 'Low' ? 'text-green-400' :
                    pool.risk === 'Medium' ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {pool.risk}
                  </span>
                </div>
              </div>
              <button className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors">
                Stake Now
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Blockchain <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Ecosystem</span>
          </h1>
          <p className="text-white/70 text-lg">Decentralized finance, NFTs, and governance powered by quantum blockchain</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'defi' && renderDeFi()}
          {activeTab === 'nft' && renderNFT()}
          {activeTab === 'dao' && renderDAO()}
          {activeTab === 'staking' && renderStaking()}
          {activeTab === 'trading' && (
            <div className="text-center py-12">
              <TrendingUp className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">DEX Trading Platform</h3>
              <p className="text-white/60">Advanced decentralized exchange with AI-powered trading algorithms</p>
            </div>
          )}
          {activeTab === 'launchpad' && (
            <div className="space-y-8">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-4">Project Launchpad</h3>
                <p className="text-white/70 text-lg">Discover and invest in revolutionary blockchain projects</p>
              </div>
              <div className="space-y-6">
                {launchpadProjects.map((project) => (
                  <div key={project.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-xl font-semibold text-white">{project.name}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            project.status === 'Live' ? 'bg-green-500/20 text-green-400' :
                            project.status === 'Upcoming' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-blue-500/20 text-blue-400'
                          }`}>
                            {project.status}
                          </span>
                        </div>
                        <p className="text-white/70 text-sm mb-4">{project.description}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="text-white/60">Raised</div>
                            <div className="text-green-400 font-semibold">{project.raised}</div>
                          </div>
                          <div>
                            <div className="text-white/60">Target</div>
                            <div className="text-blue-400 font-semibold">{project.target}</div>
                          </div>
                          <div>
                            <div className="text-white/60">Price</div>
                            <div className="text-purple-400 font-semibold">{project.price}</div>
                          </div>
                          <div>
                            <div className="text-white/60">Allocation</div>
                            <div className="text-orange-400 font-semibold">{project.allocation}</div>
                          </div>
                        </div>
                      </div>
                      <div className="lg:w-48">
                        {project.status === 'Live' && (
                          <button className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors font-semibold">
                            Invest Now
                          </button>
                        )}
                        {project.status === 'Upcoming' && (
                          <button className="w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-3 rounded-lg hover:from-yellow-700 hover:to-orange-700 transition-colors font-semibold">
                            Get Notified
                          </button>
                        )}
                        {project.status === 'Completed' && (
                          <button className="w-full bg-white/10 border border-white/20 text-white py-3 rounded-lg cursor-not-allowed">
                            Completed
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Wallet Connection Modal */}
      {showWalletModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white">Connect Wallet</h2>
              <button
                onClick={() => setShowWalletModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 space-y-4">
              {[
                { name: 'MetaMask', icon: '🦊', description: 'Connect using MetaMask wallet' },
                { name: 'WalletConnect', icon: '🔗', description: 'Scan with WalletConnect' },
                { name: 'Coinbase Wallet', icon: '🔵', description: 'Connect with Coinbase' },
                { name: 'Phantom', icon: '👻', description: 'Solana wallet connection' }
              ].map((wallet) => (
                <button
                  key={wallet.name}
                  onClick={() => connectWallet(wallet.name)}
                  className="w-full flex items-center space-x-4 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-colors"
                >
                  <span className="text-2xl">{wallet.icon}</span>
                  <div className="text-left">
                    <div className="text-white font-medium">{wallet.name}</div>
                    <div className="text-white/60 text-sm">{wallet.description}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Protocol Interaction Modal */}
      {showProtocolModal && selectedProtocol && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-2xl w-full">
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white">{selectedProtocol.name}</h2>
              <button
                onClick={() => setShowProtocolModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Protocol Stats</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-white/60">TVL:</span>
                      <span className="text-white font-medium">{selectedProtocol.tvl}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">APY:</span>
                      <span className="text-green-400 font-medium">{selectedProtocol.apy}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Risk Level:</span>
                      <span className={`font-medium ${
                        selectedProtocol.risk === 'Low' ? 'text-green-400' :
                        selectedProtocol.risk === 'Medium' ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {selectedProtocol.risk}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                  <div className="space-y-3">
                    <button className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 rounded-xl hover:from-green-700 hover:to-green-800 transition-colors">
                      Deposit
                    </button>
                    <button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-colors">
                      Withdraw
                    </button>
                    <button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-3 rounded-xl hover:from-purple-700 hover:to-purple-800 transition-colors">
                      Claim Rewards
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-white/5 rounded-xl">
                <h4 className="text-white font-medium mb-2">About {selectedProtocol.name}</h4>
                <p className="text-white/70 text-sm">
                  {selectedProtocol.description || 'This is a decentralized finance protocol that allows users to earn yield on their cryptocurrency holdings through various strategies including lending, liquidity provision, and yield farming.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Wallet Status Bar */}
      {isWalletConnected && (
        <div className="fixed bottom-4 right-4 bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center space-x-3">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <div>
            <div className="text-white text-sm font-medium">Wallet Connected</div>
            <div className="text-white/60 text-xs">{walletAddress.slice(0, 6)}...{walletAddress.slice(-4)}</div>
          </div>
          <button
            onClick={disconnectWallet}
            className="text-red-400 hover:text-red-300 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default BlockchainEcosystem;