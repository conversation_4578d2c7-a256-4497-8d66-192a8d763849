import { io, Socket } from 'socket.io-client';

interface WebSocketMessage {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  message: string;
  timestamp: Date;
  type?: string;
}

interface NotificationData {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read?: boolean;
}

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(token: string) {
    if (this.socket?.connected) {
      return;
    }

    const wsUrl = import.meta.env.VITE_WS_URL || 'http://localhost:4000';
    
    this.socket = io(wsUrl, {
      auth: { token },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.setupEventListeners();
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    });

    // Real-time notifications
    this.socket.on('notification', (data: NotificationData) => {
      this.handleNotification(data);
    });

    // Live chat messages
    this.socket.on('new-message', (data: WebSocketMessage) => {
      this.handleNewMessage(data);
    });

    // Live streaming events
    this.socket.on('stream-message', (data: WebSocketMessage) => {
      this.handleStreamMessage(data);
    });

    this.socket.on('user-joined-stream', (data: { userId: string; userName: string }) => {
      this.handleUserJoinedStream(data);
    });

    this.socket.on('user-left-stream', (data: { userId: string; userName: string }) => {
      this.handleUserLeftStream(data);
    });

    // Product events
    this.socket.on('new-product-question', (data: any) => {
      this.handleProductQuestion(data);
    });

    // NFT auction events
    this.socket.on('new-bid', (data: any) => {
      this.handleNewBid(data);
    });

    // AI assistant responses
    this.socket.on('ai-response', (data: any) => {
      this.handleAIResponse(data);
    });

    // Typing indicators
    this.socket.on('user-typing', (data: { userId: string; userName: string }) => {
      this.handleUserTyping(data);
    });

    this.socket.on('user-stopped-typing', (data: { userId: string }) => {
      this.handleUserStoppedTyping(data);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.socket?.connect();
      }, delay);
    }
  }

  // Event handlers (can be overridden by components)
  private handleNotification(data: NotificationData) {
    // Default notification handler
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(data.title, {
        body: data.message,
        icon: '/favicon.ico',
      });
    }
    
    // Dispatch custom event for components to listen
    window.dispatchEvent(new CustomEvent('ws-notification', { detail: data }));
  }

  private handleNewMessage(data: WebSocketMessage) {
    window.dispatchEvent(new CustomEvent('ws-new-message', { detail: data }));
  }

  private handleStreamMessage(data: WebSocketMessage) {
    window.dispatchEvent(new CustomEvent('ws-stream-message', { detail: data }));
  }

  private handleUserJoinedStream(data: { userId: string; userName: string }) {
    window.dispatchEvent(new CustomEvent('ws-user-joined-stream', { detail: data }));
  }

  private handleUserLeftStream(data: { userId: string; userName: string }) {
    window.dispatchEvent(new CustomEvent('ws-user-left-stream', { detail: data }));
  }

  private handleProductQuestion(data: any) {
    window.dispatchEvent(new CustomEvent('ws-product-question', { detail: data }));
  }

  private handleNewBid(data: any) {
    window.dispatchEvent(new CustomEvent('ws-new-bid', { detail: data }));
  }

  private handleAIResponse(data: any) {
    window.dispatchEvent(new CustomEvent('ws-ai-response', { detail: data }));
  }

  private handleUserTyping(data: { userId: string; userName: string }) {
    window.dispatchEvent(new CustomEvent('ws-user-typing', { detail: data }));
  }

  private handleUserStoppedTyping(data: { userId: string }) {
    window.dispatchEvent(new CustomEvent('ws-user-stopped-typing', { detail: data }));
  }

  // Public methods for sending events
  joinStream(streamId: string) {
    this.socket?.emit('join-stream', streamId);
  }

  leaveStream(streamId: string) {
    this.socket?.emit('leave-stream', streamId);
  }

  sendStreamMessage(streamId: string, message: string) {
    this.socket?.emit('stream-message', { streamId, message });
  }

  joinChat(chatId: string) {
    this.socket?.emit('join-chat', chatId);
  }

  leaveChat(chatId: string) {
    this.socket?.emit('leave-chat', chatId);
  }

  sendMessage(chatId: string, message: string, type = 'text') {
    this.socket?.emit('send-message', { chatId, message, type });
  }

  startTyping(chatId: string) {
    this.socket?.emit('typing-start', { chatId });
  }

  stopTyping(chatId: string) {
    this.socket?.emit('typing-stop', { chatId });
  }

  joinProductRoom(productId: string) {
    this.socket?.emit('join-product-room', productId);
  }

  leaveProductRoom(productId: string) {
    this.socket?.emit('leave-product-room', productId);
  }

  askProductQuestion(productId: string, question: string) {
    this.socket?.emit('product-question', { productId, question });
  }

  joinCourse(courseId: string) {
    this.socket?.emit('join-course', courseId);
  }

  leaveCourse(courseId: string) {
    this.socket?.emit('leave-course', courseId);
  }

  updateCourseProgress(courseId: string, lessonId: string, progress: number) {
    this.socket?.emit('course-progress', { courseId, lessonId, progress });
  }

  joinNFTAuction(nftId: string) {
    this.socket?.emit('join-nft-auction', nftId);
  }

  placeBid(nftId: string, amount: number) {
    this.socket?.emit('place-bid', { nftId, amount });
  }

  sendAIQuery(query: string, context?: string) {
    this.socket?.emit('ai-query', { query, context });
  }

  markNotificationRead(notificationId: string) {
    this.socket?.emit('mark-notification-read', notificationId);
  }

  updateUserActivity(activity: string) {
    this.socket?.emit('user-activity', activity);
  }

  // Utility methods
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getSocket(): Socket | null {
    return this.socket;
  }
}

// Create and export WebSocket service instance
export const wsService = new WebSocketService();
export default wsService;
