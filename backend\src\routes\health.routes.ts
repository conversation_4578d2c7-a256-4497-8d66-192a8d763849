import { Router } from 'express';
import {
  getHealthDashboard,
  updateHealthMetrics,
  getWorkouts,
  createWorkout,
  completeWorkout,
  getNutritionPlan,
  updateNutritionPlan,
  logMeal,
  getMentalHealthData,
  updateMoodTracking,
  getDoctors,
  bookAppointment,
  getAppointments,
  cancelAppointment,
  getHealthInsights,
  updateFitnessGoals,
  getFitnessGoals
} from '../controllers/health.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { validateHealthData } from '../middleware/validation.middleware';

const router = Router();

// All health routes require authentication
router.use(authenticateToken);

// Health Dashboard
router.get('/dashboard', getHealthDashboard);
router.get('/insights', getHealthInsights);

// Health Metrics
router.put('/metrics', validateHealthData, updateHealthMetrics);

// Fitness & Workouts
router.get('/workouts', getWorkouts);
router.post('/workouts', createWorkout);
router.put('/workouts/:id/complete', completeWorkout);
router.get('/fitness-goals', getFitnessGoals);
router.put('/fitness-goals', updateFitnessGoals);

// Nutrition
router.get('/nutrition', getNutritionPlan);
router.put('/nutrition', updateNutritionPlan);
router.post('/nutrition/log-meal', logMeal);

// Mental Health
router.get('/mental-health', getMentalHealthData);
router.put('/mood-tracking', updateMoodTracking);

// Telemedicine
router.get('/doctors', getDoctors);
router.post('/appointments', bookAppointment);
router.get('/appointments', getAppointments);
router.delete('/appointments/:id', cancelAppointment);

export default router;
