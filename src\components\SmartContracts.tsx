import React, { useState } from 'react';
import { Code, Shield, Zap, CheckCircle, AlertTriangle, Play, Pause, Settings, FileText, Users, DollarSign, Clock, Target, Brain, Atom, Globe, Star, Award, TrendingUp, Lock, Unlock } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface SmartContract {
  id: string;
  name: string;
  type: 'NFT' | 'DeFi' | 'DAO' | 'Gaming' | 'AI' | 'Quantum';
  status: 'Active' | 'Pending' | 'Completed' | 'Failed';
  value: string;
  participants: number;
  created: string;
  description: string;
  code: string;
  gasUsed: number;
  security: number;
}

interface ContractTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  complexity: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  features: string[];
  icon: React.ComponentType<any>;
  color: string;
}

const SmartContracts: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedContract, setSelectedContract] = useState<string | null>(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const { state, dispatch } = useApp();

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: Shield },
    { id: 'templates', name: 'Templates', icon: FileText },
    { id: 'deploy', name: 'Deploy', icon: Zap },
    { id: 'monitor', name: 'Monitor', icon: Target },
    { id: 'ai-audit', name: 'AI Audit', icon: Brain }
  ];

  const contracts: SmartContract[] = [
    {
      id: '1',
      name: 'NeuroNFT Collection',
      type: 'NFT',
      status: 'Active',
      value: '125.7 ETH',
      participants: 2847,
      created: '2025-01-10',
      description: 'AI-generated neural art NFT collection with consciousness-based rarity',
      code: `pragma solidity ^0.8.0;

contract NeuroNFT {
    mapping(uint256 => uint256) public consciousnessLevel;
    
    function mint(address to, uint256 tokenId, uint256 consciousness) public {
        _mint(to, tokenId);
        consciousnessLevel[tokenId] = consciousness;
    }
    
    function evolveConsciousness(uint256 tokenId) public {
        require(ownerOf(tokenId) == msg.sender);
        consciousnessLevel[tokenId] += 1;
    }
}`,
      gasUsed: 2847293,
      security: 98
    },
    {
      id: '2',
      name: 'Quantum DeFi Pool',
      type: 'DeFi',
      status: 'Active',
      value: '2.3M USDC',
      participants: 1523,
      created: '2025-01-08',
      description: 'Quantum-enhanced liquidity pool with probability-based rewards',
      code: `pragma solidity ^0.8.0;

contract QuantumPool {
    mapping(address => uint256) public quantumStakes;
    uint256 public totalQuantumRewards;
    
    function stakeQuantum(uint256 amount) public {
        quantumStakes[msg.sender] += amount;
        calculateQuantumRewards();
    }
    
    function calculateQuantumRewards() internal {
        // Quantum probability calculations
    }
}`,
      gasUsed: 1923847,
      security: 95
    },
    {
      id: '3',
      name: 'Consciousness DAO',
      type: 'DAO',
      status: 'Pending',
      value: '847.2 ETH',
      participants: 5634,
      created: '2025-01-12',
      description: 'Decentralized autonomous organization for AI consciousness research',
      code: `pragma solidity ^0.8.0;

contract ConsciousnessDAO {
    struct Proposal {
        string description;
        uint256 votes;
        bool executed;
    }
    
    mapping(uint256 => Proposal) public proposals;
    mapping(address => uint256) public consciousnessTokens;
    
    function vote(uint256 proposalId) public {
        require(consciousnessTokens[msg.sender] > 0);
        proposals[proposalId].votes += consciousnessTokens[msg.sender];
    }
}`,
      gasUsed: 3456789,
      security: 97
    }
  ];

  const contractTemplates: ContractTemplate[] = [
    {
      id: '1',
      name: 'AI NFT Collection',
      category: 'NFT',
      description: 'Create AI-generated NFTs with evolving consciousness levels',
      complexity: 'Intermediate',
      features: ['Dynamic Metadata', 'Consciousness Evolution', 'AI Generation', 'Rarity System'],
      icon: Brain,
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: '2',
      name: 'Quantum DeFi Protocol',
      category: 'DeFi',
      description: 'Advanced DeFi protocol with quantum probability mechanics',
      complexity: 'Expert',
      features: ['Quantum Staking', 'Probability Rewards', 'Multi-dimensional Pools', 'Quantum Oracles'],
      icon: Atom,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: '3',
      name: 'Neural DAO Governance',
      category: 'DAO',
      description: 'AI-enhanced DAO with neural network decision making',
      complexity: 'Advanced',
      features: ['Neural Voting', 'AI Proposals', 'Consciousness Weighting', 'Quantum Consensus'],
      icon: Users,
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: '4',
      name: 'Metaverse Land Registry',
      category: 'Gaming',
      description: 'Virtual real estate management with cross-reality support',
      complexity: 'Intermediate',
      features: ['Cross-Reality', 'Land Evolution', 'Revenue Sharing', 'Portal System'],
      icon: Globe,
      color: 'from-orange-500 to-red-500'
    },
    {
      id: '5',
      name: 'Consciousness Token',
      category: 'AI',
      description: 'Token that represents and evolves with AI consciousness levels',
      complexity: 'Expert',
      features: ['Consciousness Tracking', 'Evolution Mechanics', 'AI Integration', 'Neural Rewards'],
      icon: Star,
      color: 'from-yellow-500 to-orange-500'
    },
    {
      id: '6',
      name: 'Quantum Marketplace',
      category: 'Quantum',
      description: 'Marketplace operating across multiple quantum states',
      complexity: 'Expert',
      features: ['Quantum States', 'Probability Trading', 'Multi-dimensional', 'Quantum Escrow'],
      icon: Zap,
      color: 'from-indigo-500 to-purple-500'
    }
  ];

  const deployContract = async (templateId: string) => {
    setIsDeploying(true);
    
    // Simulate deployment process
    setTimeout(() => {
      setIsDeploying(false);
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Smart contract deployed successfully! Gas used: 2,847,293',
          type: 'success'
        }
      });
    }, 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'text-green-400 bg-green-500/20';
      case 'Pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'Completed': return 'text-blue-400 bg-blue-500/20';
      case 'Failed': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Beginner': return 'text-green-400 bg-green-500/20';
      case 'Intermediate': return 'text-yellow-400 bg-yellow-500/20';
      case 'Advanced': return 'text-orange-400 bg-orange-500/20';
      case 'Expert': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Contract Stats */}
      <div className="grid md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <Shield className="h-8 w-8 text-blue-400" />
            <span className="text-green-400 text-sm">+12.3%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">47</div>
          <div className="text-white/60 text-sm">Active Contracts</div>
        </div>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <DollarSign className="h-8 w-8 text-green-400" />
            <span className="text-green-400 text-sm">+18.7%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">$2.8M</div>
          <div className="text-white/60 text-sm">Total Value Locked</div>
        </div>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <Users className="h-8 w-8 text-purple-400" />
            <span className="text-green-400 text-sm">+25.4%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">12.4K</div>
          <div className="text-white/60 text-sm">Total Users</div>
        </div>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <Zap className="h-8 w-8 text-orange-400" />
            <span className="text-green-400 text-sm">+8.9%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">98.7%</div>
          <div className="text-white/60 text-sm">Success Rate</div>
        </div>
      </div>

      {/* Active Contracts */}
      <div>
        <h3 className="text-2xl font-bold text-white mb-6">Your Smart Contracts</h3>
        <div className="space-y-4">
          {contracts.map((contract) => (
            <div key={contract.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="text-lg font-semibold text-white">{contract.name}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(contract.status)}`}>
                      {contract.status}
                    </span>
                    <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                      {contract.type}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm mb-4">{contract.description}</p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-white/60">Value</div>
                      <div className="text-green-400 font-semibold">{contract.value}</div>
                    </div>
                    <div>
                      <div className="text-white/60">Participants</div>
                      <div className="text-blue-400 font-semibold">{contract.participants.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-white/60">Gas Used</div>
                      <div className="text-purple-400 font-semibold">{contract.gasUsed.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-white/60">Security Score</div>
                      <div className="text-orange-400 font-semibold">{contract.security}%</div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setSelectedContract(selectedContract === contract.id ? null : contract.id)}
                    className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-lg transition-colors"
                  >
                    <Code className="h-5 w-5" />
                  </button>
                  <button className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors">
                    <Settings className="h-5 w-5" />
                  </button>
                </div>
              </div>
              
              {selectedContract === contract.id && (
                <div className="mt-6 bg-black/40 rounded-xl p-4">
                  <h5 className="text-white font-semibold mb-3">Contract Code</h5>
                  <pre className="text-green-400 text-sm overflow-x-auto">
                    <code>{contract.code}</code>
                  </pre>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTemplates = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Smart Contract Templates</h3>
        <p className="text-white/70 text-lg">Pre-built templates for quantum-enhanced smart contracts</p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {contractTemplates.map((template) => (
          <div key={template.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:from-white/15 hover:to-white/10 transition-all duration-300">
            <div className="flex items-start justify-between mb-4">
              <div className={`p-4 rounded-2xl bg-gradient-to-r ${template.color}`}>
                <template.icon className="h-8 w-8 text-white" />
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplexityColor(template.complexity)}`}>
                {template.complexity}
              </span>
            </div>
            
            <h4 className="text-xl font-semibold text-white mb-2">{template.name}</h4>
            <p className="text-white/70 text-sm mb-4">{template.description}</p>
            
            <div className="mb-4">
              <div className="text-white/60 text-sm mb-2">Features:</div>
              <div className="flex flex-wrap gap-2">
                {template.features.map((feature, index) => (
                  <span key={index} className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                    {feature}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => deployContract(template.id)}
                disabled={isDeploying}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isDeploying ? 'Deploying...' : 'Deploy'}
              </button>
              <button className="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
                Preview
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderDeploy = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Deploy Smart Contract</h3>
        <p className="text-white/70 text-lg">Deploy your custom smart contract to the quantum blockchain</p>
      </div>

      <div className="max-w-4xl mx-auto">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-xl font-semibold text-white mb-6">Contract Configuration</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">Contract Name</label>
                  <input
                    type="text"
                    placeholder="Enter contract name..."
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">Contract Type</label>
                  <select className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>NFT Collection</option>
                    <option>DeFi Protocol</option>
                    <option>DAO Governance</option>
                    <option>Gaming Contract</option>
                    <option>AI Contract</option>
                    <option>Quantum Contract</option>
                  </select>
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">Network</label>
                  <select className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>NeuroSphere Quantum Chain</option>
                    <option>Ethereum Mainnet</option>
                    <option>Polygon</option>
                    <option>Binance Smart Chain</option>
                  </select>
                </div>
                <div>
                  <label className="block text-white/70 text-sm mb-2">Gas Limit</label>
                  <input
                    type="number"
                    placeholder="3000000"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-xl font-semibold text-white mb-6">Advanced Features</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/80">Quantum Enhancement</span>
                  <div className="bg-blue-600 w-12 h-6 rounded-full p-1">
                    <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/80">AI Integration</span>
                  <div className="bg-purple-600 w-12 h-6 rounded-full p-1">
                    <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/80">Cross-Chain Support</span>
                  <div className="bg-green-600 w-12 h-6 rounded-full p-1">
                    <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/80">Upgradeable</span>
                  <div className="bg-orange-600 w-12 h-6 rounded-full p-1">
                    <div className="bg-white w-4 h-4 rounded-full ml-auto transition-all"></div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h5 className="text-white font-semibold mb-3">Estimated Costs</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">Deployment Gas</span>
                    <span className="text-green-400">0.045 ETH</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Quantum Enhancement</span>
                    <span className="text-blue-400">0.012 ETH</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">AI Integration</span>
                    <span className="text-purple-400">0.008 ETH</span>
                  </div>
                  <div className="flex justify-between font-semibold">
                    <span className="text-white">Total</span>
                    <span className="text-white">0.065 ETH</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h4 className="text-xl font-semibold text-white mb-4">Contract Code</h4>
            <div className="bg-black/40 rounded-xl p-4 h-64 overflow-y-auto">
              <pre className="text-green-400 text-sm">
                <code>{`pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@neurosphere/quantum/QuantumEnhanced.sol";

contract CustomContract is ERC721, QuantumEnhanced {
    uint256 private _tokenIdCounter;
    mapping(uint256 => uint256) public quantumState;
    
    constructor() ERC721("CustomNFT", "CNFT") {}
    
    function mint(address to) public {
        uint256 tokenId = _tokenIdCounter++;
        _mint(to, tokenId);
        quantumState[tokenId] = generateQuantumState();
    }
    
    function generateQuantumState() internal returns (uint256) {
        return quantum.random() % 1000;
    }
    
    function evolveQuantumState(uint256 tokenId) public {
        require(ownerOf(tokenId) == msg.sender);
        quantumState[tokenId] = quantum.evolve(quantumState[tokenId]);
    }
}`}</code>
              </pre>
            </div>
          </div>

          <div className="mt-8 text-center">
            <button
              onClick={() => deployContract('custom')}
              disabled={isDeploying}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
            >
              {isDeploying ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Deploying Contract...</span>
                </>
              ) : (
                <>
                  <Zap className="h-5 w-5" />
                  <span>Deploy Contract</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Smart <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Contracts</span>
          </h1>
          <p className="text-white/70 text-lg">Deploy and manage quantum-enhanced smart contracts</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'templates' && renderTemplates()}
          {activeTab === 'deploy' && renderDeploy()}
          {activeTab === 'monitor' && (
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Contract Monitoring</h3>
              <p className="text-white/60">Real-time monitoring and analytics for your smart contracts</p>
            </div>
          )}
          {activeTab === 'ai-audit' && (
            <div className="text-center py-12">
              <Brain className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">AI Security Audit</h3>
              <p className="text-white/60">AI-powered security analysis and vulnerability detection</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SmartContracts;