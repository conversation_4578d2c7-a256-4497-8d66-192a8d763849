import React, { useState } from 'react';
import { 
  Wallet, 
  TrendingUp, 
  CreditCard, 
  PiggyBank, 
  ArrowUpRight, 
  ArrowDownLeft,
  DollarSign,
  BarChart3,
  Shield,
  Zap,
  Target,
  Globe,
  Plus,
  Minus,
  Eye,
  EyeOff
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const FinTechServices: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showBalance, setShowBalance] = useState(true);
  const { state, dispatch } = useApp();

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'investments', name: 'Investments', icon: TrendingUp },
    { id: 'savings', name: 'Savings', icon: PiggyBank },
    { id: 'cards', name: 'Cards', icon: CreditCard },
    { id: 'crypto', name: 'Crypto', icon: Globe }
  ];

  const portfolioData = [
    { name: 'AAPL', value: 4250, change: 2.5, allocation: 25 },
    { name: 'GOOGL', value: 3800, change: -1.2, allocation: 22 },
    { name: 'TSLA', value: 2900, change: 4.8, allocation: 18 },
    { name: 'MSFT', value: 2100, change: 1.9, allocation: 12 },
    { name: 'BTC', value: 1897, change: 8.3, allocation: 11 },
    { name: 'ETH', value: 1200, change: 5.7, allocation: 7 },
    { name: 'Others', value: 853, change: 0.8, allocation: 5 }
  ];

  const recentTransactions = [
    { id: '1', type: 'investment', description: 'AAPL Stock Purchase', amount: -500, date: '2 hours ago', icon: TrendingUp },
    { id: '2', type: 'income', description: 'Creator Revenue', amount: 1247, date: '1 day ago', icon: DollarSign },
    { id: '3', type: 'expense', description: 'Course Purchase', amount: -299, date: '2 days ago', icon: Minus },
    { id: '4', type: 'crypto', description: 'Bitcoin Purchase', amount: -800, date: '3 days ago', icon: Globe },
    { id: '5', type: 'savings', description: 'Auto-Save Transfer', amount: -200, date: '1 week ago', icon: PiggyBank }
  ];

  const savingsGoals = [
    { name: 'Emergency Fund', target: 10000, current: 7500, color: 'from-blue-500 to-blue-600' },
    { name: 'Vacation', target: 5000, current: 2800, color: 'from-green-500 to-green-600' },
    { name: 'New Equipment', target: 3000, current: 1200, color: 'from-purple-500 to-purple-600' }
  ];

  const cryptoHoldings = [
    { symbol: 'BTC', name: 'Bitcoin', amount: 0.0847, value: 1897, change: 8.3 },
    { symbol: 'ETH', name: 'Ethereum', amount: 2.34, value: 1200, change: 5.7 },
    { symbol: 'ADA', name: 'Cardano', amount: 1250, value: 450, change: -2.1 }
  ];

  const addInvestment = (stock: string, amount: number) => {
    dispatch({ 
      type: 'ADD_INVESTMENT', 
      payload: { stock, amount, date: new Date().toISOString() } 
    });
    dispatch({ 
      type: 'ADD_NOTIFICATION', 
      payload: { 
        id: Date.now(), 
        message: `Invested $${amount} in ${stock}`, 
        type: 'success' 
      } 
    });
  };

  const totalPortfolioValue = portfolioData.reduce((sum, item) => sum + item.value, 0);
  const totalChange = portfolioData.reduce((sum, item) => sum + (item.value * item.change / 100), 0);
  const totalChangePercent = (totalChange / totalPortfolioValue) * 100;

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Balance Cards */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-green-600/20 to-green-700/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Wallet className="h-6 w-6 text-green-400" />
              <span className="text-white/80">Total Balance</span>
            </div>
            <button
              onClick={() => setShowBalance(!showBalance)}
              className="p-1 text-white/60 hover:text-white transition-colors"
            >
              {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            {showBalance ? `$${(state.user?.balance || 0).toLocaleString()}` : '••••••'}
          </div>
          <div className="text-green-400 text-sm">+$247 today</div>
        </div>

        <div className="bg-gradient-to-br from-blue-600/20 to-blue-700/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center space-x-2 mb-4">
            <TrendingUp className="h-6 w-6 text-blue-400" />
            <span className="text-white/80">Investments</span>
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            ${totalPortfolioValue.toLocaleString()}
          </div>
          <div className={`text-sm ${totalChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {totalChangePercent >= 0 ? '+' : ''}{totalChangePercent.toFixed(2)}% today
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="flex items-center space-x-2 mb-4">
            <PiggyBank className="h-6 w-6 text-purple-400" />
            <span className="text-white/80">Savings</span>
          </div>
          <div className="text-3xl font-bold text-white mb-2">
            ${savingsGoals.reduce((sum, goal) => sum + goal.current, 0).toLocaleString()}
          </div>
          <div className="text-purple-400 text-sm">3 active goals</div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-4 gap-4">
        <button className="bg-gradient-to-r from-green-600 to-green-700 text-white p-4 rounded-xl hover:from-green-700 hover:to-green-800 transition-colors flex items-center space-x-3">
          <Plus className="h-5 w-5" />
          <span>Add Money</span>
        </button>
        <button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-colors flex items-center space-x-3">
          <TrendingUp className="h-5 w-5" />
          <span>Invest</span>
        </button>
        <button className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-4 rounded-xl hover:from-purple-700 hover:to-purple-800 transition-colors flex items-center space-x-3">
          <Target className="h-5 w-5" />
          <span>Set Goal</span>
        </button>
        <button className="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-4 rounded-xl hover:from-orange-700 hover:to-orange-800 transition-colors flex items-center space-x-3">
          <Globe className="h-5 w-5" />
          <span>Buy Crypto</span>
        </button>
      </div>

      {/* Recent Transactions */}
      <div>
        <h3 className="text-2xl font-bold text-white mb-6">Recent Transactions</h3>
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <div className="space-y-4">
            {recentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    transaction.amount > 0 ? 'bg-green-500/20' : 'bg-red-500/20'
                  }`}>
                    <transaction.icon className={`h-5 w-5 ${
                      transaction.amount > 0 ? 'text-green-400' : 'text-red-400'
                    }`} />
                  </div>
                  <div>
                    <div className="text-white font-medium">{transaction.description}</div>
                    <div className="text-white/60 text-sm">{transaction.date}</div>
                  </div>
                </div>
                <div className={`text-lg font-semibold ${
                  transaction.amount > 0 ? 'text-green-400' : 'text-white'
                }`}>
                  {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderInvestments = () => (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="text-center mb-6">
          <div className="text-4xl font-bold text-white mb-2">${totalPortfolioValue.toLocaleString()}</div>
          <div className={`text-lg ${totalChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {totalChangePercent >= 0 ? '+' : ''}${totalChange.toFixed(2)} ({totalChangePercent.toFixed(2)}%) today
          </div>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          {portfolioData.slice(0, 4).map((stock, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
              <div className="text-lg font-semibold text-white mb-1">{stock.name}</div>
              <div className="text-2xl font-bold text-white mb-2">${stock.value.toLocaleString()}</div>
              <div className={`text-sm ${stock.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {stock.change >= 0 ? '+' : ''}{stock.change}%
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Portfolio Allocation</h3>
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="space-y-4">
              {portfolioData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    <span className="text-white">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-medium">{item.allocation}%</div>
                    <div className="text-white/60 text-sm">${item.value.toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-2xl font-bold text-white mb-6">AI Recommendations</h3>
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Zap className="h-5 w-5 text-yellow-400 mt-1" />
                <div>
                  <div className="text-white font-medium mb-1">Diversify Tech Holdings</div>
                  <div className="text-white/70 text-sm">Consider adding healthcare or energy stocks to balance your tech-heavy portfolio.</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-blue-400 mt-1" />
                <div>
                  <div className="text-white font-medium mb-1">Increase Bond Allocation</div>
                  <div className="text-white/70 text-sm">Add 10-15% bonds for stability as market volatility increases.</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Target className="h-5 w-5 text-green-400 mt-1" />
                <div>
                  <div className="text-white font-medium mb-1">Dollar-Cost Averaging</div>
                  <div className="text-white/70 text-sm">Set up automatic monthly investments to reduce timing risk.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSavings = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Savings Goals</h3>
        <p className="text-white/70 text-lg">Track your progress towards financial milestones</p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {savingsGoals.map((goal, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="text-center mb-6">
              <h4 className="text-xl font-semibold text-white mb-2">{goal.name}</h4>
              <div className="text-3xl font-bold text-white mb-1">${goal.current.toLocaleString()}</div>
              <div className="text-white/60">of ${goal.target.toLocaleString()}</div>
            </div>
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-white/70">Progress</span>
                <span className="text-white/70">{Math.round((goal.current / goal.target) * 100)}%</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-3">
                <div 
                  className={`bg-gradient-to-r ${goal.color} h-3 rounded-full transition-all duration-300`}
                  style={{ width: `${(goal.current / goal.target) * 100}%` }}
                ></div>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="flex-1 bg-white/10 border border-white/20 text-white py-2 rounded-lg hover:bg-white/20 transition-colors">
                Add Funds
              </button>
              <button className="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors">
                Auto-Save
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="text-center">
          <h4 className="text-2xl font-bold text-white mb-4">Smart Savings Tips</h4>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-gradient-to-r from-green-500 to-green-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h5 className="text-lg font-semibold text-white mb-2">Auto-Save</h5>
              <p className="text-white/70 text-sm">Automatically save a percentage of your income</p>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h5 className="text-lg font-semibold text-white mb-2">Goal-Based</h5>
              <p className="text-white/70 text-sm">Set specific targets for better motivation</p>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h5 className="text-lg font-semibold text-white mb-2">High-Yield</h5>
              <p className="text-white/70 text-sm">Earn up to 4.5% APY on your savings</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCrypto = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Crypto Portfolio</h3>
        <p className="text-white/70 text-lg">Your digital asset holdings</p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {cryptoHoldings.map((crypto, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">{crypto.symbol}</span>
                </div>
                <div>
                  <div className="text-white font-semibold">{crypto.name}</div>
                  <div className="text-white/60 text-sm">{crypto.symbol}</div>
                </div>
              </div>
              <div className={`text-sm font-medium ${crypto.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {crypto.change >= 0 ? '+' : ''}{crypto.change}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">${crypto.value.toLocaleString()}</div>
              <div className="text-white/60 text-sm">{crypto.amount} {crypto.symbol}</div>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-gradient-to-r from-orange-600/20 to-yellow-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="text-center mb-6">
          <h4 className="text-2xl font-bold text-white mb-4">Crypto Insights</h4>
          <p className="text-white/70">AI-powered market analysis and recommendations</p>
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white/10 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h5 className="text-lg font-semibold text-white mb-3">Market Sentiment</h5>
            <div className="text-green-400 text-2xl font-bold mb-2">Bullish</div>
            <p className="text-white/70 text-sm">Strong institutional adoption and positive regulatory news driving market confidence.</p>
          </div>
          <div className="bg-white/10 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h5 className="text-lg font-semibold text-white mb-3">Recommendation</h5>
            <div className="text-blue-400 text-2xl font-bold mb-2">DCA Strategy</div>
            <p className="text-white/70 text-sm">Consider dollar-cost averaging into BTC and ETH over the next 3-6 months.</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            FinTech <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">Services</span>
          </h1>
          <p className="text-white/70 text-lg">Manage your finances with AI-powered insights and tools</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-green-600 to-blue-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'investments' && renderInvestments()}
          {activeTab === 'savings' && renderSavings()}
          {activeTab === 'crypto' && renderCrypto()}
          {activeTab === 'cards' && (
            <div className="text-center py-12">
              <CreditCard className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Card Management</h3>
              <p className="text-white/60">Manage your virtual and physical cards</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FinTechServices;