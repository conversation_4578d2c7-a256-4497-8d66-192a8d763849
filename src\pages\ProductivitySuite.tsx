import React, { useState } from 'react';
import { 
  CheckSquare, 
  Calendar, 
  Clock, 
  Plus, 
  Filter, 
  Search,
  BarChart3,
  Target,
  Zap,
  Users,
  FileText,
  Folder,
  Star,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const ProductivitySuite: React.FC = () => {
  const [activeTab, setActiveTab] = useState('tasks');
  const [newTask, setNewTask] = useState('');
  const [showAddTask, setShowAddTask] = useState(false);
  const { state, dispatch } = useApp();

  const tabs = [
    { id: 'tasks', name: 'Tasks', icon: CheckSquare },
    { id: 'calendar', name: 'Calendar', icon: Calendar },
    { id: 'projects', name: 'Projects', icon: Folder },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 }
  ];

  const taskCategories = [
    { id: 'all', name: 'All Tasks', count: 24 },
    { id: 'today', name: 'Today', count: 8 },
    { id: 'urgent', name: 'Urgent', count: 3 },
    { id: 'completed', name: 'Completed', count: 12 }
  ];

  const tasks = [
    {
      id: '1',
      title: 'Complete AI course module',
      description: 'Finish the neural networks chapter and take the quiz',
      priority: 'high',
      dueDate: 'Today',
      category: 'Learning',
      completed: false,
      assignee: 'You',
      progress: 75
    },
    {
      id: '2',
      title: 'Review brand partnership proposal',
      description: 'Analyze the terms and prepare counter-proposal',
      priority: 'medium',
      dueDate: 'Tomorrow',
      category: 'Business',
      completed: false,
      assignee: 'You',
      progress: 30
    },
    {
      id: '3',
      title: 'Create content calendar',
      description: 'Plan next month\'s content strategy',
      priority: 'medium',
      dueDate: 'This week',
      category: 'Content',
      completed: true,
      assignee: 'You',
      progress: 100
    },
    {
      id: '4',
      title: 'Update portfolio website',
      description: 'Add recent projects and testimonials',
      priority: 'low',
      dueDate: 'Next week',
      category: 'Personal',
      completed: false,
      assignee: 'You',
      progress: 10
    }
  ];

  const projects = [
    {
      id: '1',
      name: 'NeuroSphere Launch Campaign',
      description: 'Marketing campaign for platform launch',
      progress: 68,
      dueDate: '2 weeks',
      team: 5,
      tasks: 24,
      completedTasks: 16,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: '2',
      name: 'AI Course Development',
      description: 'Create comprehensive AI learning curriculum',
      progress: 45,
      dueDate: '1 month',
      team: 3,
      tasks: 18,
      completedTasks: 8,
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: '3',
      name: 'Creator Tools Beta',
      description: 'Develop and test new creator monetization tools',
      progress: 82,
      dueDate: '1 week',
      team: 7,
      tasks: 32,
      completedTasks: 26,
      color: 'from-green-500 to-green-600'
    }
  ];

  const upcomingEvents = [
    {
      id: '1',
      title: 'Team Standup',
      time: '9:00 AM',
      duration: '30 min',
      type: 'meeting',
      color: 'bg-blue-500'
    },
    {
      id: '2',
      title: 'Content Review',
      time: '2:00 PM',
      duration: '1 hour',
      type: 'review',
      color: 'bg-purple-500'
    },
    {
      id: '3',
      title: 'Client Call',
      time: '4:30 PM',
      duration: '45 min',
      type: 'call',
      color: 'bg-green-500'
    }
  ];

  const addTask = () => {
    if (newTask.trim()) {
      dispatch({
        type: 'ADD_TASK',
        payload: {
          id: Date.now().toString(),
          title: newTask,
          completed: false,
          priority: 'medium',
          dueDate: 'Today',
          category: 'General'
        }
      });
      setNewTask('');
      setShowAddTask(false);
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          message: 'Task added successfully!',
          type: 'success'
        }
      });
    }
  };

  const completeTask = (taskId: string) => {
    dispatch({ type: 'COMPLETE_TASK', payload: taskId });
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: 'Task completed! +50 points earned',
        type: 'success'
      }
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400 bg-red-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20';
      case 'low': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const renderTasks = () => (
    <div className="space-y-6">
      {/* Task Stats */}
      <div className="grid md:grid-cols-4 gap-4">
        {taskCategories.map((category) => (
          <div key={category.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
            <div className="text-2xl font-bold text-white mb-1">{category.count}</div>
            <div className="text-white/60 text-sm">{category.name}</div>
          </div>
        ))}
      </div>

      {/* Add Task */}
      <div className="flex items-center space-x-4">
        {showAddTask ? (
          <div className="flex-1 flex items-center space-x-2">
            <input
              type="text"
              value={newTask}
              onChange={(e) => setNewTask(e.target.value)}
              placeholder="Enter task title..."
              className="flex-1 bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && addTask()}
              autoFocus
            />
            <button
              onClick={addTask}
              className="bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg transition-colors"
            >
              <CheckCircle className="h-5 w-5" />
            </button>
            <button
              onClick={() => setShowAddTask(false)}
              className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        ) : (
          <button
            onClick={() => setShowAddTask(true)}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors"
          >
            <Plus className="h-5 w-5" />
            <span>Add Task</span>
          </button>
        )}
        <div className="flex items-center space-x-2">
          <button className="flex items-center space-x-2 bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white hover:bg-white/20 transition-colors">
            <Filter className="h-4 w-4" />
            <span>Filter</span>
          </button>
          <button className="flex items-center space-x-2 bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white hover:bg-white/20 transition-colors">
            <Search className="h-4 w-4" />
            <span>Search</span>
          </button>
        </div>
      </div>

      {/* Tasks List */}
      <div className="space-y-4">
        {tasks.map((task) => (
          <div key={task.id} className={`bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 transition-all duration-300 ${
            task.completed ? 'opacity-60' : 'hover:from-white/15 hover:to-white/10'
          }`}>
            <div className="flex items-start space-x-4">
              <button
                onClick={() => !task.completed && completeTask(task.id)}
                className={`mt-1 p-1 rounded-full transition-colors ${
                  task.completed 
                    ? 'bg-green-500 text-white' 
                    : 'bg-white/20 hover:bg-green-500 text-white/60 hover:text-white'
                }`}
              >
                <CheckCircle className="h-5 w-5" />
              </button>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className={`text-lg font-semibold ${task.completed ? 'text-white/60 line-through' : 'text-white'}`}>
                    {task.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </span>
                    <span className="text-white/60 text-sm">{task.dueDate}</span>
                  </div>
                </div>
                <p className="text-white/70 text-sm mb-3">{task.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-white/60">
                    <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">{task.category}</span>
                    <span>Assigned to {task.assignee}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-white/60 text-sm">{task.progress}%</span>
                    <div className="w-20 bg-white/20 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderProjects = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-2xl font-bold text-white">Active Projects</h3>
        <button className="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>New Project</span>
        </button>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project) => (
          <div key={project.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:from-white/15 hover:to-white/10 transition-all duration-300">
            <div className="mb-4">
              <h4 className="text-lg font-semibold text-white mb-2">{project.name}</h4>
              <p className="text-white/70 text-sm">{project.description}</p>
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-white/70">Progress</span>
                <span className="text-white/70">{project.progress}%</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-3">
                <div 
                  className={`bg-gradient-to-r ${project.color} h-3 rounded-full transition-all duration-300`}
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
              <div className="text-center">
                <div className="text-white font-semibold">{project.completedTasks}/{project.tasks}</div>
                <div className="text-white/60">Tasks</div>
              </div>
              <div className="text-center">
                <div className="text-white font-semibold">{project.team}</div>
                <div className="text-white/60">Team Members</div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white/60 text-sm">Due in {project.dueDate}</span>
              <button className="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderCalendar = () => (
    <div className="space-y-6">
      <div className="grid md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <h3 className="text-2xl font-bold text-white mb-6">Today's Schedule</h3>
          <div className="space-y-4">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                <div className="flex items-center space-x-4">
                  <div className={`w-4 h-4 rounded-full ${event.color}`}></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="text-white font-semibold">{event.title}</h4>
                      <span className="text-white/60 text-sm">{event.duration}</span>
                    </div>
                    <div className="text-white/70 text-sm">{event.time}</div>
                  </div>
                  <button className="text-white/60 hover:text-white transition-colors">
                    <Calendar className="h-5 w-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-2xl font-bold text-white mb-6">Quick Stats</h3>
          <div className="space-y-4">
            <div className="bg-gradient-to-br from-blue-600/20 to-blue-700/20 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
              <Clock className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">6.5h</div>
              <div className="text-white/60 text-sm">Productive Time</div>
            </div>
            <div className="bg-gradient-to-br from-green-600/20 to-green-700/20 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
              <Target className="h-8 w-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">8/12</div>
              <div className="text-white/60 text-sm">Tasks Completed</div>
            </div>
            <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/20 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
              <Zap className="h-8 w-8 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">94%</div>
              <div className="text-white/60 text-sm">Focus Score</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Productivity <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Suite</span>
          </h1>
          <p className="text-white/70 text-lg">Organize your work and boost productivity with AI-powered tools</p>
        </div>

        {/* Productivity Overview */}
        <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 mb-8">
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">24</div>
              <div className="text-white/60">Active Tasks</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">3</div>
              <div className="text-white/60">Projects</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">68%</div>
              <div className="text-white/60">Completion Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">+15%</div>
              <div className="text-white/60">Productivity Boost</div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'tasks' && renderTasks()}
          {activeTab === 'projects' && renderProjects()}
          {activeTab === 'calendar' && renderCalendar()}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Productivity Analytics</h3>
              <p className="text-white/60">Detailed insights into your productivity patterns and trends</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductivitySuite;