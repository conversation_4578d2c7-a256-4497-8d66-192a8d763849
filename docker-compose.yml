version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: neurosphere-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: neurosphere
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./backend/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - neurosphere-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: neurosphere-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis123
    networks:
      - neurosphere-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: neurosphere-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 4000
      MONGODB_URI: **********************************************************************
      REDIS_URL: redis://:redis123@redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-in-production
      JWT_REFRESH_EXPIRES_IN: 30d
      STRIPE_SECRET_KEY: sk_test_your_stripe_secret_key
      STRIPE_WEBHOOK_SECRET: whsec_your_webhook_secret
      FRONTEND_URL: http://localhost:3000
      MAX_FILE_SIZE: 10485760
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
    ports:
      - "4000:4000"
    volumes:
      - ./backend/uploads:/app/uploads
    depends_on:
      - mongodb
      - redis
    networks:
      - neurosphere-network

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: http://localhost:4000/api
        VITE_WS_URL: http://localhost:4000
        VITE_APP_NAME: NeuroSphere
        VITE_APP_VERSION: 1.0.0
        VITE_ENABLE_WEBSOCKET: true
        VITE_ENABLE_NOTIFICATIONS: true
        VITE_ENABLE_ANALYTICS: true
        VITE_ENABLE_PAYMENTS: true
    container_name: neurosphere-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - neurosphere-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: neurosphere-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - neurosphere-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  neurosphere-network:
    driver: bridge
