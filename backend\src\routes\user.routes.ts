import { Router } from 'express';
import {
  getUsers,
  getUserById,
  updateUserRole,
  deactivateUser,
  getUserStats,
  toggleFollowUser,
  getUserActivity
} from '../controllers/user.controller';
import { authenticateToken, requireAdmin, optionalAuth } from '../middleware/auth.middleware';

const router = Router();

// Public routes (with optional auth)
router.get('/:id', optionalAuth, getUserById);

// Protected routes
router.get('/', authenticateToken, getUsers);
router.get('/:id/stats', authenticateToken, getUserStats);
router.get('/:id/activity', authenticateToken, getUserActivity);
router.post('/:id/follow', authenticateToken, toggleFollowUser);

// Admin routes
router.put('/:id/role', requireAdmin, updateUserRole);
router.put('/:id/deactivate', requireAdmin, deactivateUser);

export default router;
