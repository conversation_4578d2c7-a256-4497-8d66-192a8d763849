import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, TrendingDown, Users, Eye, Heart, MessageCircle, DollarSign, Target, Zap, Brain, Globe, Star, Award, Calendar, Clock, Filter, Download, Share2, RefreshCw } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface AnalyticsData {
  period: string;
  views: number;
  engagement: number;
  revenue: number;
  followers: number;
  growth: number;
}

interface MetricCard {
  title: string;
  value: string;
  change: number;
  icon: React.ComponentType<any>;
  color: string;
  trend: 'up' | 'down' | 'stable';
}

const AdvancedAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('overview');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const { state } = useApp();

  const timeRanges = [
    { id: '24h', name: '24 Hours' },
    { id: '7d', name: '7 Days' },
    { id: '30d', name: '30 Days' },
    { id: '90d', name: '90 Days' },
    { id: '1y', name: '1 Year' }
  ];

  const metricCategories = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'audience', name: 'Audience', icon: Users },
    { id: 'content', name: 'Content', icon: Eye },
    { id: 'revenue', name: 'Revenue', icon: DollarSign },
    { id: 'ai-insights', name: 'AI Insights', icon: Brain },
    { id: 'predictions', name: 'Predictions', icon: Target }
  ];

  const overviewMetrics: MetricCard[] = [
    {
      title: 'Total Views',
      value: '2.3M',
      change: 15.2,
      icon: Eye,
      color: 'from-blue-500 to-blue-600',
      trend: 'up'
    },
    {
      title: 'Engagement Rate',
      value: '9.4%',
      change: 2.1,
      icon: Heart,
      color: 'from-pink-500 to-pink-600',
      trend: 'up'
    },
    {
      title: 'Revenue',
      value: '$24.8K',
      change: 18.7,
      icon: DollarSign,
      color: 'from-green-500 to-green-600',
      trend: 'up'
    },
    {
      title: 'Followers',
      value: '847K',
      change: 12.3,
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      trend: 'up'
    },
    {
      title: 'AI Score',
      value: '94.7',
      change: 5.8,
      icon: Brain,
      color: 'from-orange-500 to-orange-600',
      trend: 'up'
    },
    {
      title: 'Global Reach',
      value: '156',
      change: 23.4,
      icon: Globe,
      color: 'from-cyan-500 to-cyan-600',
      trend: 'up'
    }
  ];

  const audienceInsights = [
    { country: 'United States', percentage: 35.2, users: '295K' },
    { country: 'United Kingdom', percentage: 18.7, users: '158K' },
    { country: 'Germany', percentage: 12.4, users: '105K' },
    { country: 'Japan', percentage: 10.8, users: '91K' },
    { country: 'Canada', percentage: 8.9, users: '75K' },
    { country: 'Others', percentage: 14.0, users: '118K' }
  ];

  const contentPerformance = [
    {
      title: 'AI Art Tutorial: Neural Networks',
      views: 456789,
      engagement: 12.4,
      revenue: 2847,
      type: 'Video'
    },
    {
      title: 'Quantum Computing Explained',
      views: 234567,
      engagement: 15.7,
      revenue: 1923,
      type: 'Article'
    },
    {
      title: 'Metaverse Building Workshop',
      views: 345678,
      engagement: 18.9,
      revenue: 3456,
      type: 'Live Stream'
    },
    {
      title: 'Neural Interface Demo',
      views: 567890,
      engagement: 22.1,
      revenue: 4567,
      type: 'Interactive'
    }
  ];

  const aiInsights = [
    {
      insight: 'Optimal Posting Time',
      description: 'Your audience is most active between 2-4 PM EST',
      impact: 'High',
      recommendation: 'Schedule content during peak hours for 25% more engagement'
    },
    {
      insight: 'Content Trend Prediction',
      description: 'AI-generated art content will peak in the next 2 weeks',
      impact: 'Medium',
      recommendation: 'Create more AI art tutorials to capitalize on trend'
    },
    {
      insight: 'Audience Sentiment',
      description: 'Positive sentiment increased by 15% this week',
      impact: 'High',
      recommendation: 'Continue current content strategy and engagement approach'
    },
    {
      insight: 'Revenue Optimization',
      description: 'Premium content generates 3x more revenue per view',
      impact: 'High',
      recommendation: 'Focus on creating more premium, in-depth content'
    }
  ];

  const predictions = [
    {
      metric: 'Follower Growth',
      current: '847K',
      predicted: '1.2M',
      timeframe: '30 days',
      confidence: 94
    },
    {
      metric: 'Revenue Projection',
      current: '$24.8K',
      predicted: '$45.2K',
      timeframe: '30 days',
      confidence: 87
    },
    {
      metric: 'Engagement Rate',
      current: '9.4%',
      predicted: '12.8%',
      timeframe: '30 days',
      confidence: 91
    },
    {
      metric: 'Content Virality',
      current: '2.3M views',
      predicted: '5.7M views',
      timeframe: '30 days',
      confidence: 89
    }
  ];

  useEffect(() => {
    // Simulate data fetching
    const generateData = () => {
      const data: AnalyticsData[] = [];
      const days = timeRange === '24h' ? 1 : timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      
      for (let i = 0; i < days; i++) {
        data.push({
          period: `Day ${i + 1}`,
          views: Math.floor(Math.random() * 100000) + 50000,
          engagement: Math.random() * 20 + 5,
          revenue: Math.floor(Math.random() * 5000) + 1000,
          followers: Math.floor(Math.random() * 10000) + 5000,
          growth: (Math.random() - 0.5) * 20
        });
      }
      setAnalyticsData(data);
    };

    generateData();
  }, [timeRange]);

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Metric Cards */}
      <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-6">
        {overviewMetrics.map((metric, index) => (
          <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${metric.color}`}>
                <metric.icon className="h-6 w-6 text-white" />
              </div>
              <div className={`flex items-center space-x-1 text-sm ${
                metric.trend === 'up' ? 'text-green-400' : 
                metric.trend === 'down' ? 'text-red-400' : 'text-gray-400'
              }`}>
                {metric.trend === 'up' ? <TrendingUp className="h-4 w-4" /> : 
                 metric.trend === 'down' ? <TrendingDown className="h-4 w-4" /> : null}
                <span>{metric.change > 0 ? '+' : ''}{metric.change}%</span>
              </div>
            </div>
            <div className="text-2xl font-bold text-white mb-1">{metric.value}</div>
            <div className="text-white/60 text-sm">{metric.title}</div>
          </div>
        ))}
      </div>

      {/* Charts */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Views & Engagement</h3>
          <div className="h-64 bg-black/40 rounded-xl p-4">
            <div className="flex items-end h-full space-x-2">
              {analyticsData.slice(0, 7).map((data, index) => (
                <div key={index} className="flex-1 flex flex-col items-center space-y-2">
                  <div
                    className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-t w-full transition-all duration-300"
                    style={{ height: `${(data.views / 100000) * 100}%` }}
                  />
                  <span className="text-white/60 text-xs">{data.period}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Revenue Growth</h3>
          <div className="h-64 bg-black/40 rounded-xl p-4">
            <div className="flex items-end h-full space-x-2">
              {analyticsData.slice(0, 7).map((data, index) => (
                <div key={index} className="flex-1 flex flex-col items-center space-y-2">
                  <div
                    className="bg-gradient-to-t from-green-500 to-emerald-500 rounded-t w-full transition-all duration-300"
                    style={{ height: `${(data.revenue / 5000) * 100}%` }}
                  />
                  <span className="text-white/60 text-xs">{data.period}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAudience = () => (
    <div className="space-y-8">
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Geographic Distribution</h3>
          <div className="space-y-4">
            {audienceInsights.map((country, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    {index + 1}
                  </div>
                  <span className="text-white">{country.country}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-32 bg-white/20 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                      style={{ width: `${country.percentage}%` }}
                    />
                  </div>
                  <span className="text-white/80 text-sm w-16 text-right">{country.users}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Audience Demographics</h3>
          <div className="space-y-6">
            <div>
              <h4 className="text-white font-medium mb-3">Age Groups</h4>
              <div className="space-y-2">
                {[
                  { range: '18-24', percentage: 28 },
                  { range: '25-34', percentage: 35 },
                  { range: '35-44', percentage: 22 },
                  { range: '45-54', percentage: 12 },
                  { range: '55+', percentage: 3 }
                ].map((age, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-white/80">{age.range}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-white/20 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                          style={{ width: `${age.percentage}%` }}
                        />
                      </div>
                      <span className="text-white/60 text-sm w-8">{age.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-white font-medium mb-3">Device Usage</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">67%</div>
                  <div className="text-white/60 text-sm">Mobile</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">28%</div>
                  <div className="text-white/60 text-sm">Desktop</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">5%</div>
                  <div className="text-white/60 text-sm">Tablet</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAIInsights = () => (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">AI-Powered Insights</h3>
        <div className="grid md:grid-cols-2 gap-6">
          {aiInsights.map((insight, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-semibold text-white">{insight.insight}</h4>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  insight.impact === 'High' ? 'bg-red-500/20 text-red-400' :
                  insight.impact === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-green-500/20 text-green-400'
                }`}>
                  {insight.impact} Impact
                </span>
              </div>
              <p className="text-white/70 text-sm mb-4">{insight.description}</p>
              <div className="bg-blue-500/20 text-blue-400 p-3 rounded-lg">
                <div className="font-medium text-sm">💡 Recommendation:</div>
                <div className="text-sm mt-1">{insight.recommendation}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPredictions = () => (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">AI Predictions</h3>
        <div className="grid md:grid-cols-2 gap-6">
          {predictions.map((prediction, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4">{prediction.metric}</h4>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <div className="text-white/60 text-sm">Current</div>
                  <div className="text-2xl font-bold text-white">{prediction.current}</div>
                </div>
                <div className="text-center">
                  <TrendingUp className="h-8 w-8 text-green-400 mx-auto mb-1" />
                  <div className="text-white/60 text-xs">{prediction.timeframe}</div>
                </div>
                <div className="text-right">
                  <div className="text-white/60 text-sm">Predicted</div>
                  <div className="text-2xl font-bold text-green-400">{prediction.predicted}</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">Confidence Level</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-white/20 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                      style={{ width: `${prediction.confidence}%` }}
                    />
                  </div>
                  <span className="text-green-400 font-semibold text-sm">{prediction.confidence}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Advanced <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Analytics</span>
          </h1>
          <p className="text-white/70 text-lg">AI-powered insights and predictions for your content performance</p>
        </div>

        {/* Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex space-x-2">
            {timeRanges.map((range) => (
              <button
                key={range.id}
                onClick={() => setTimeRange(range.id)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  timeRange === range.id
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                    : 'bg-white/10 text-white/80 hover:bg-white/20'
                }`}
              >
                {range.name}
              </button>
            ))}
          </div>
          <div className="flex items-center space-x-2 ml-auto">
            <button className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
            <button className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {metricCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedMetric(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                selectedMetric === category.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <category.icon className="h-5 w-5" />
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div>
          {selectedMetric === 'overview' && renderOverview()}
          {selectedMetric === 'audience' && renderAudience()}
          {selectedMetric === 'ai-insights' && renderAIInsights()}
          {selectedMetric === 'predictions' && renderPredictions()}
          {selectedMetric === 'content' && (
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-white">Content Performance</h3>
              <div className="space-y-4">
                {contentPerformance.map((content, index) => (
                  <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-1">{content.title}</h4>
                        <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                          {content.type}
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-6 text-center">
                        <div>
                          <div className="text-xl font-bold text-white">{content.views.toLocaleString()}</div>
                          <div className="text-white/60 text-sm">Views</div>
                        </div>
                        <div>
                          <div className="text-xl font-bold text-green-400">{content.engagement}%</div>
                          <div className="text-white/60 text-sm">Engagement</div>
                        </div>
                        <div>
                          <div className="text-xl font-bold text-purple-400">${content.revenue}</div>
                          <div className="text-white/60 text-sm">Revenue</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {selectedMetric === 'revenue' && (
            <div className="text-center py-12">
              <DollarSign className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Revenue Analytics</h3>
              <p className="text-white/60">Detailed revenue breakdown and monetization insights</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalytics;