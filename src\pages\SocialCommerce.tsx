import React, { useState } from 'react';
import { 
  ShoppingCart, 
  Heart, 
  Star, 
  Filter, 
  Search, 
  Play,
  Users,
  TrendingUp,
  Eye,
  MessageCircle,
  Share2
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const SocialCommerce: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('trending');
  const [searchQuery, setSearchQuery] = useState('');
  const { state, dispatch } = useApp();

  const categories = [
    { id: 'trending', name: 'Trending', icon: TrendingUp },
    { id: 'electronics', name: 'Electronics', icon: ShoppingCart },
    { id: 'fashion', name: 'Fashion', icon: Heart },
    { id: 'home', name: 'Home & Garden', icon: Users },
    { id: 'beauty', name: 'Beauty', icon: Star }
  ];

  const products = [
    {
      id: '1',
      name: 'AI-Powered Smart Watch',
      price: 299,
      originalPrice: 399,
      image: 'https://images.pexels.com/photos/393047/pexels-photo-393047.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.8,
      reviews: 1247,
      seller: 'TechNova',
      isLive: true,
      viewers: 2847,
      category: 'electronics'
    },
    {
      id: '2',
      name: 'Sustainable Fashion Bundle',
      price: 149,
      originalPrice: 199,
      image: 'https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.9,
      reviews: 892,
      seller: 'EcoStyle',
      isLive: false,
      viewers: 0,
      category: 'fashion'
    },
    {
      id: '3',
      name: 'Premium Skincare Set',
      price: 89,
      originalPrice: 129,
      image: 'https://images.pexels.com/photos/3685530/pexels-photo-3685530.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.7,
      reviews: 634,
      seller: 'GlowBeauty',
      isLive: true,
      viewers: 1523,
      category: 'beauty'
    },
    {
      id: '4',
      name: 'Smart Home Starter Kit',
      price: 199,
      originalPrice: 299,
      image: 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.6,
      reviews: 445,
      seller: 'HomeAI',
      isLive: false,
      viewers: 0,
      category: 'home'
    },
    {
      id: '5',
      name: 'Wireless Gaming Headset',
      price: 179,
      originalPrice: 229,
      image: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.8,
      reviews: 1089,
      seller: 'GamePro',
      isLive: true,
      viewers: 3241,
      category: 'electronics'
    },
    {
      id: '6',
      name: 'Organic Tea Collection',
      price: 45,
      originalPrice: 65,
      image: 'https://images.pexels.com/photos/1638280/pexels-photo-1638280.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.9,
      reviews: 723,
      seller: 'PureTea',
      isLive: false,
      viewers: 0,
      category: 'home'
    }
  ];

  const liveStreams = [
    {
      id: '1',
      title: 'Tech Review: Latest AI Gadgets',
      streamer: 'TechGuru Mike',
      viewers: 15420,
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Electronics'
    },
    {
      id: '2',
      title: 'Fashion Haul: Spring Collection',
      streamer: 'StyleQueen Emma',
      viewers: 8934,
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Fashion'
    },
    {
      id: '3',
      title: 'Skincare Routine for Glowing Skin',
      streamer: 'BeautyExpert Sarah',
      viewers: 12567,
      thumbnail: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Beauty'
    }
  ];

  const addToCart = (product: any) => {
    dispatch({ type: 'ADD_TO_CART', payload: product });
    dispatch({ 
      type: 'ADD_NOTIFICATION', 
      payload: { 
        id: Date.now(), 
        message: `${product.name} added to cart!`, 
        type: 'success' 
      } 
    });
  };

  const filteredProducts = products.filter(product => 
    activeCategory === 'trending' || product.category === activeCategory
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Social <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Commerce</span>
          </h1>
          <p className="text-white/70 text-lg">Discover, shop, and connect with creators and brands worldwide</p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
            <input
              type="text"
              placeholder="Search products, brands, creators..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
          <button className="flex items-center space-x-2 bg-white/10 border border-white/20 rounded-xl px-6 py-3 text-white hover:bg-white/20 transition-colors backdrop-blur-sm">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Categories */}
        <div className="flex space-x-4 mb-8 overflow-x-auto pb-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeCategory === category.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <category.icon className="h-5 w-5" />
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Live Streams Section */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Live Shopping Streams</h2>
            <button className="text-blue-400 hover:text-blue-300 transition-colors">View All</button>
          </div>
          <div className="grid md:grid-cols-3 gap-6">
            {liveStreams.map((stream) => (
              <div key={stream.id} className="group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
                <div className="relative">
                  <img
                    src={stream.thumbnail}
                    alt={stream.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>LIVE</span>
                  </div>
                  <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{stream.viewers.toLocaleString()}</span>
                  </div>
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-3 hover:bg-white/30 transition-colors">
                      <Play className="h-6 w-6 text-white" />
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold mb-1">{stream.title}</h3>
                  <p className="text-white/70 text-sm mb-2">{stream.streamer}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400 text-xs bg-blue-400/20 px-2 py-1 rounded-full">{stream.category}</span>
                    <div className="flex items-center space-x-2 text-white/60 text-xs">
                      <MessageCircle className="h-3 w-3" />
                      <Share2 className="h-3 w-3" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Featured Products</h2>
            <div className="flex items-center space-x-2 text-white/60">
              <span className="text-sm">{filteredProducts.length} products</span>
            </div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <div key={product.id} className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105">
                <div className="relative">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-48 object-cover"
                  />
                  {product.isLive && (
                    <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      <span>LIVE</span>
                    </div>
                  )}
                  {product.viewers > 0 && (
                    <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                      <Eye className="h-3 w-3" />
                      <span>{product.viewers.toLocaleString()}</span>
                    </div>
                  )}
                  <button className="absolute top-3 right-3 p-2 bg-white/20 backdrop-blur-sm rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                    <Heart className="h-4 w-4 text-white" />
                  </button>
                  <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => addToCart(product)}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-colors flex items-center justify-center space-x-2"
                    >
                      <ShoppingCart className="h-4 w-4" />
                      <span>Add to Cart</span>
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold mb-2">{product.name}</h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-white/80 text-sm">{product.rating}</span>
                    </div>
                    <span className="text-white/60 text-sm">({product.reviews} reviews)</span>
                  </div>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-white">${product.price}</span>
                      <span className="text-white/60 line-through text-sm">${product.originalPrice}</span>
                    </div>
                    <span className="text-green-400 text-sm font-medium">
                      {Math.round((1 - product.price / product.originalPrice) * 100)}% OFF
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">by {product.seller}</span>
                    <div className="flex items-center space-x-2">
                      <button className="p-1 text-white/60 hover:text-white transition-colors">
                        <MessageCircle className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-white/60 hover:text-white transition-colors">
                        <Share2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Shopping Cart Summary */}
        {state.cart.length > 0 && (
          <div className="fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl shadow-2xl backdrop-blur-sm border border-white/20">
            <div className="flex items-center space-x-3">
              <ShoppingCart className="h-6 w-6" />
              <div>
                <div className="font-semibold">{state.cart.length} items in cart</div>
                <div className="text-sm opacity-90">
                  Total: ${state.cart.reduce((sum, item) => sum + item.price, 0).toFixed(2)}
                </div>
              </div>
              <button className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
                View Cart
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SocialCommerce;