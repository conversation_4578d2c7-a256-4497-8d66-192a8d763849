import React, { useState, useEffect } from 'react';
import {
  ShoppingCart,
  Heart,
  Star,
  Filter,
  Search,
  Play,
  Users,
  TrendingUp,
  Eye,
  MessageCircle,
  Share2,
  Plus,
  Minus,
  X,
  Check,
  Truck,
  Shield,
  Award,
  Clock,
  MapPin,
  CreditCard,
  Package
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import api from '../services/api';
import AdvancedSearch from '../components/AdvancedSearch';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  rating: number;
  reviews: number;
  seller: string;
  isLive: boolean;
  viewers: number;
  category: string;
  description?: string;
  features?: string[];
  shipping?: {
    free: boolean;
    time: string;
    cost?: number;
  };
  stock: number;
  discount?: number;
}

interface CartItem extends Product {
  quantity: number;
  selectedVariant?: string;
}

const SocialCommerce: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('trending');
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showCart, setShowCart] = useState(false);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [wishlist, setWishlist] = useState<string[]>([]);
  const { state, dispatch } = useApp();

  // Load products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await api.getProducts({
          category: activeCategory === 'trending' ? undefined : activeCategory,
          search: searchQuery,
          limit: 20
        });
        if (response.success) {
          setProducts(response.data.products || []);
        }
      } catch (error) {
        console.error('Failed to fetch products:', error);
        // Fallback to mock data
        setProducts(mockProducts);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [activeCategory, searchQuery]);

  const categories = [
    { id: 'trending', name: 'Trending', icon: TrendingUp },
    { id: 'electronics', name: 'Electronics', icon: ShoppingCart },
    { id: 'fashion', name: 'Fashion', icon: Heart },
    { id: 'home', name: 'Home & Garden', icon: Users },
    { id: 'beauty', name: 'Beauty', icon: Star }
  ];

  // Mock products data (fallback)
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'AI-Powered Smart Watch',
      price: 299,
      originalPrice: 399,
      image: 'https://images.pexels.com/photos/393047/pexels-photo-393047.jpeg?auto=compress&cs=tinysrgb&w=400',
      images: [
        'https://images.pexels.com/photos/393047/pexels-photo-393047.jpeg?auto=compress&cs=tinysrgb&w=400',
        'https://images.pexels.com/photos/437037/pexels-photo-437037.jpeg?auto=compress&cs=tinysrgb&w=400'
      ],
      rating: 4.8,
      reviews: 1247,
      seller: 'TechNova',
      isLive: true,
      viewers: 2847,
      category: 'electronics',
      description: 'Revolutionary smartwatch with advanced AI capabilities, health monitoring, and seamless connectivity.',
      features: ['AI Health Monitoring', '7-day Battery Life', 'Waterproof Design', 'Voice Assistant'],
      shipping: { free: true, time: '2-3 days' },
      stock: 50,
      discount: 25
    },
    {
      id: '2',
      name: 'Sustainable Fashion Bundle',
      price: 149,
      originalPrice: 199,
      image: 'https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=400',
      rating: 4.9,
      reviews: 892,
      seller: 'EcoStyle',
      isLive: false,
      viewers: 0,
      category: 'fashion',
      description: 'Eco-friendly clothing collection made from recycled materials.',
      features: ['100% Recycled Materials', 'Carbon Neutral Shipping', 'Ethical Production'],
      shipping: { free: false, time: '3-5 days', cost: 9.99 },
      stock: 25,
      discount: 25
    }
  ];

  // Cart functions
  const addToCart = (product: Product, quantity: number = 1) => {
    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + quantity }
          : item
      ));
    } else {
      setCart([...cart, { ...product, quantity }]);
    }
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId));
  };

  const updateCartQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
    } else {
      setCart(cart.map(item =>
        item.id === productId ? { ...item, quantity } : item
      ));
    }
  };

  const toggleWishlist = (productId: string) => {
    if (wishlist.includes(productId)) {
      setWishlist(wishlist.filter(id => id !== productId));
    } else {
      setWishlist([...wishlist, productId]);
    }
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    setShowProductModal(true);
  };

  const handleSearchResults = (results: Product[]) => {
    setProducts(results);
  };

  // Product Detail Modal Component
  const ProductDetailModal = () => {
    if (!selectedProduct) return null;

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <h2 className="text-2xl font-bold text-white">{selectedProduct.name}</h2>
            <button
              onClick={() => setShowProductModal(false)}
              className="text-white/60 hover:text-white transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6 grid md:grid-cols-2 gap-8">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="aspect-square rounded-xl overflow-hidden">
                <img
                  src={selectedProduct.image}
                  alt={selectedProduct.name}
                  className="w-full h-full object-cover"
                />
              </div>
              {selectedProduct.images && selectedProduct.images.length > 1 && (
                <div className="flex space-x-2">
                  {selectedProduct.images.map((img, index) => (
                    <div key={index} className="w-20 h-20 rounded-lg overflow-hidden">
                      <img src={img} alt={`${selectedProduct.name} ${index + 1}`} className="w-full h-full object-cover" />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              {/* Price */}
              <div className="flex items-center space-x-3">
                <span className="text-3xl font-bold text-white">${selectedProduct.price}</span>
                {selectedProduct.originalPrice && (
                  <>
                    <span className="text-xl text-white/60 line-through">${selectedProduct.originalPrice}</span>
                    <span className="bg-red-500 text-white px-2 py-1 rounded-full text-sm">
                      {selectedProduct.discount}% OFF
                    </span>
                  </>
                )}
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${i < Math.floor(selectedProduct.rating) ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
                    />
                  ))}
                </div>
                <span className="text-white/80">{selectedProduct.rating}</span>
                <span className="text-white/60">({selectedProduct.reviews} reviews)</span>
              </div>

              {/* Seller */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">{selectedProduct.seller[0]}</span>
                </div>
                <div>
                  <p className="text-white font-medium">{selectedProduct.seller}</p>
                  <p className="text-white/60 text-sm">Verified Seller</p>
                </div>
              </div>

              {/* Description */}
              {selectedProduct.description && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Description</h3>
                  <p className="text-white/80">{selectedProduct.description}</p>
                </div>
              )}

              {/* Features */}
              {selectedProduct.features && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Features</h3>
                  <ul className="space-y-2">
                    {selectedProduct.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2 text-white/80">
                        <Check className="h-4 w-4 text-green-400" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Shipping */}
              {selectedProduct.shipping && (
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Truck className="h-5 w-5 text-blue-400" />
                    <span className="text-white font-medium">Shipping Info</span>
                  </div>
                  <div className="space-y-1 text-white/80">
                    <p>{selectedProduct.shipping.free ? 'Free Shipping' : `Shipping: $${selectedProduct.shipping.cost}`}</p>
                    <p>Delivery: {selectedProduct.shipping.time}</p>
                  </div>
                </div>
              )}

              {/* Stock */}
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-green-400" />
                <span className="text-white/80">{selectedProduct.stock} in stock</span>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    addToCart(selectedProduct);
                    setShowProductModal(false);
                  }}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <ShoppingCart className="h-5 w-5" />
                  <span>Add to Cart</span>
                </button>
                <button
                  onClick={() => toggleWishlist(selectedProduct.id)}
                  className={`px-4 py-3 rounded-xl transition-colors ${
                    wishlist.includes(selectedProduct.id)
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-white/10 hover:bg-white/20 text-white'
                  }`}
                >
                  <Heart className={`h-5 w-5 ${wishlist.includes(selectedProduct.id) ? 'fill-current' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Shopping Cart Modal Component
  const ShoppingCartModal = () => {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900/95 backdrop-blur-sm border border-white/10 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <h2 className="text-2xl font-bold text-white">Shopping Cart ({getTotalItems()} items)</h2>
            <button
              onClick={() => setShowCart(false)}
              className="text-white/60 hover:text-white transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Cart Items */}
          <div className="p-6">
            {cart.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart className="h-16 w-16 text-white/40 mx-auto mb-4" />
                <p className="text-white/60 text-lg">Your cart is empty</p>
                <button
                  onClick={() => setShowCart(false)}
                  className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Continue Shopping
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {cart.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 bg-white/5 rounded-xl p-4">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h3 className="text-white font-medium">{item.name}</h3>
                      <p className="text-white/60 text-sm">by {item.seller}</p>
                      <p className="text-white font-semibold">${item.price}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateCartQuantity(item.id, item.quantity - 1)}
                        className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                      <span className="text-white font-medium w-8 text-center">{item.quantity}</span>
                      <button
                        onClick={() => updateCartQuantity(item.id, item.quantity + 1)}
                        className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                    <button
                      onClick={() => removeFromCart(item.id)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                ))}

                {/* Cart Summary */}
                <div className="border-t border-white/10 pt-4 mt-6">
                  <div className="space-y-2">
                    <div className="flex justify-between text-white/80">
                      <span>Subtotal:</span>
                      <span>${getTotalPrice().toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-white/80">
                      <span>Shipping:</span>
                      <span>Free</span>
                    </div>
                    <div className="flex justify-between text-white/80">
                      <span>Tax:</span>
                      <span>${(getTotalPrice() * 0.08).toFixed(2)}</span>
                    </div>
                    <div className="border-t border-white/10 pt-2">
                      <div className="flex justify-between text-white font-bold text-lg">
                        <span>Total:</span>
                        <span>${(getTotalPrice() * 1.08).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Checkout Button */}
                  <button
                    onClick={() => {
                      // Handle checkout
                      setShowCart(false);
                      // Navigate to checkout page or process payment
                    }}
                    className="w-full mt-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 rounded-xl font-medium transition-colors flex items-center justify-center space-x-2"
                  >
                    <CreditCard className="h-5 w-5" />
                    <span>Proceed to Checkout</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const liveStreams = [
    {
      id: '1',
      title: 'Tech Review: Latest AI Gadgets',
      streamer: 'TechGuru Mike',
      viewers: 15420,
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Electronics'
    },
    {
      id: '2',
      title: 'Fashion Haul: Spring Collection',
      streamer: 'StyleQueen Emma',
      viewers: 8934,
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Fashion'
    },
    {
      id: '3',
      title: 'Skincare Routine for Glowing Skin',
      streamer: 'BeautyExpert Sarah',
      viewers: 12567,
      thumbnail: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Beauty'
    }
  ];

  // Enhanced addToCart function that integrates with global state
  const addToCartWithNotification = (product: Product, quantity: number = 1) => {
    addToCart(product, quantity);
    dispatch({ type: 'ADD_TO_CART', payload: product });
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        message: `${product.name} added to cart!`,
        type: 'success'
      }
    });
  };

  const filteredProducts = products.filter(product => 
    activeCategory === 'trending' || product.category === activeCategory
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Social <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Commerce</span>
          </h1>
          <p className="text-white/70 text-lg">Discover, shop, and connect with creators and brands worldwide</p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
            <input
              type="text"
              placeholder="Search products, brands, creators..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
          <button className="flex items-center space-x-2 bg-white/10 border border-white/20 rounded-xl px-6 py-3 text-white hover:bg-white/20 transition-colors backdrop-blur-sm">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Categories */}
        <div className="flex space-x-4 mb-8 overflow-x-auto pb-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeCategory === category.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <category.icon className="h-5 w-5" />
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Live Streams Section */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Live Shopping Streams</h2>
            <button className="text-blue-400 hover:text-blue-300 transition-colors">View All</button>
          </div>
          <div className="grid md:grid-cols-3 gap-6">
            {liveStreams.map((stream) => (
              <div key={stream.id} className="group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300">
                <div className="relative">
                  <img
                    src={stream.thumbnail}
                    alt={stream.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>LIVE</span>
                  </div>
                  <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{stream.viewers.toLocaleString()}</span>
                  </div>
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-3 hover:bg-white/30 transition-colors">
                      <Play className="h-6 w-6 text-white" />
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold mb-1">{stream.title}</h3>
                  <p className="text-white/70 text-sm mb-2">{stream.streamer}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-blue-400 text-xs bg-blue-400/20 px-2 py-1 rounded-full">{stream.category}</span>
                    <div className="flex items-center space-x-2 text-white/60 text-xs">
                      <MessageCircle className="h-3 w-3" />
                      <Share2 className="h-3 w-3" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Featured Products</h2>
            <div className="flex items-center space-x-2 text-white/60">
              <span className="text-sm">{filteredProducts.length} products</span>
            </div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <div key={product.id} className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105">
                <div className="relative cursor-pointer" onClick={() => handleProductClick(product)}>
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-48 object-cover"
                  />
                  {product.isLive && (
                    <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      <span>LIVE</span>
                    </div>
                  )}
                  {product.viewers > 0 && (
                    <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                      <Eye className="h-3 w-3" />
                      <span>{product.viewers.toLocaleString()}</span>
                    </div>
                  )}
                  {product.discount && (
                    <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      -{product.discount}%
                    </div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleWishlist(product.id);
                    }}
                    className={`absolute top-3 right-3 p-2 backdrop-blur-sm rounded-full opacity-0 group-hover:opacity-100 transition-opacity ${
                      wishlist.includes(product.id)
                        ? 'bg-red-500 text-white'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    <Heart className={`h-4 w-4 ${wishlist.includes(product.id) ? 'fill-current' : ''}`} />
                  </button>
                  <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        addToCartWithNotification(product);
                      }}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-colors flex items-center justify-center space-x-2"
                    >
                      <ShoppingCart className="h-4 w-4" />
                      <span>Add to Cart</span>
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-white font-semibold mb-2 cursor-pointer hover:text-blue-400 transition-colors" onClick={() => handleProductClick(product)}>
                    {product.name}
                  </h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
                        />
                      ))}
                      <span className="text-white/80 text-sm">{product.rating}</span>
                    </div>
                    <span className="text-white/60 text-sm">({product.reviews} reviews)</span>
                  </div>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-white">${product.price}</span>
                      {product.originalPrice && (
                        <span className="text-white/60 line-through text-sm">${product.originalPrice}</span>
                      )}
                    </div>
                    {product.originalPrice && (
                      <span className="text-green-400 text-sm font-medium">
                        {Math.round((1 - product.price / product.originalPrice) * 100)}% OFF
                      </span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">by {product.seller}</span>
                    <div className="flex items-center space-x-2">
                      <button className="p-1 text-white/60 hover:text-white transition-colors">
                        <MessageCircle className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-white/60 hover:text-white transition-colors">
                        <Share2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  {product.shipping?.free && (
                    <div className="mt-2 flex items-center space-x-1 text-green-400 text-xs">
                      <Truck className="h-3 w-3" />
                      <span>Free Shipping</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Shopping Cart Summary */}
        {cart.length > 0 && (
          <div className="fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl shadow-2xl backdrop-blur-sm border border-white/20">
            <div className="flex items-center space-x-3">
              <ShoppingCart className="h-6 w-6" />
              <div>
                <div className="font-semibold">{getTotalItems()} items in cart</div>
                <div className="text-sm opacity-90">
                  Total: ${getTotalPrice().toFixed(2)}
                </div>
              </div>
              <button
                onClick={() => setShowCart(true)}
                className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                View Cart
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Product Detail Modal */}
      {showProductModal && <ProductDetailModal />}

      {/* Shopping Cart Modal */}
      {showCart && <ShoppingCartModal />}
    </div>
  );
};

export default SocialCommerce;