import mongoose from 'mongoose';
import { createClient } from 'redis';

// MongoDB Connection
export const connectMongoDB = async (): Promise<void> => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/neurosphere';
    
    await mongoose.connect(mongoUri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    
    console.log('✅ MongoDB connected successfully');
    
    mongoose.connection.on('error', (error) => {
      console.error('❌ MongoDB connection error:', error);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.log('⚠️ MongoDB disconnected');
    });
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Redis Connection
export const connectRedis = async () => {
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    
    const client = createClient({
      url: redisUrl,
      retry_delay_on_failover: 100,
      max_attempts: 3,
    });
    
    client.on('error', (error) => {
      console.error('❌ Redis connection error:', error);
    });
    
    client.on('connect', () => {
      console.log('✅ Redis connected successfully');
    });
    
    client.on('disconnect', () => {
      console.log('⚠️ Redis disconnected');
    });
    
    await client.connect();
    return client;
    
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    return null;
  }
};

// Database cleanup on app termination
export const gracefulShutdown = async (redisClient?: any) => {
  try {
    if (redisClient) {
      await redisClient.quit();
      console.log('✅ Redis connection closed');
    }
    
    await mongoose.connection.close();
    console.log('✅ MongoDB connection closed');
    
  } catch (error) {
    console.error('❌ Error during database cleanup:', error);
  }
};
