import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  Zap, 
  Activity, 
  Eye, 
  Waves, 
  Target, 
  Settings,
  Play,
  Pause,
  RotateCcw,
  TrendingUp,
  Heart,
  Moon,
  Sun,
  Lightbulb,
  Headphones,
  Mic,
  Camera,
  Wifi,
  Battery,
  Signal,
  Cpu,
  HardDrive,
  Monitor
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const NeuroInterface: React.FC = () => {
  const [activeTab, setActiveTab] = useState('brainwaves');
  const [isRecording, setIsRecording] = useState(false);
  const [brainActivity, setBrainActivity] = useState({
    alpha: 0,
    beta: 0,
    gamma: 0,
    theta: 0,
    delta: 0
  });
  const [neuralSync, setNeuralSync] = useState(0);
  const { state, dispatch } = useApp();

  useEffect(() => {
    const interval = setInterval(() => {
      setBrainActivity({
        alpha: Math.random() * 100,
        beta: Math.random() * 100,
        gamma: Math.random() * 100,
        theta: Math.random() * 100,
        delta: Math.random() * 100
      });
      setNeuralSync(Math.random() * 100);
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const tabs = [
    { id: 'brainwaves', name: 'Brainwave Monitor', icon: Waves },
    { id: 'neural-training', name: 'Neural Training', icon: Target },
    { id: 'thought-interface', name: 'Thought Interface', icon: Brain },
    { id: 'emotion-sync', name: 'Emotion Sync', icon: Heart },
    { id: 'memory-palace', name: 'Memory Palace', icon: Eye },
    { id: 'consciousness', name: 'Consciousness Hub', icon: Lightbulb }
  ];

  const brainwaveTypes = [
    { name: 'Alpha', frequency: '8-12 Hz', description: 'Relaxed awareness', color: 'from-blue-500 to-cyan-500' },
    { name: 'Beta', frequency: '13-30 Hz', description: 'Active thinking', color: 'from-green-500 to-emerald-500' },
    { name: 'Gamma', frequency: '30-100 Hz', description: 'High-level cognition', color: 'from-purple-500 to-pink-500' },
    { name: 'Theta', frequency: '4-8 Hz', description: 'Deep meditation', color: 'from-orange-500 to-red-500' },
    { name: 'Delta', frequency: '0.5-4 Hz', description: 'Deep sleep', color: 'from-indigo-500 to-purple-500' }
  ];

  const neuralTrainingPrograms = [
    {
      id: '1',
      name: 'Focus Enhancement',
      description: 'Improve concentration and attention span',
      duration: '20 min',
      difficulty: 'Beginner',
      sessions: 156,
      effectiveness: '94%'
    },
    {
      id: '2',
      name: 'Memory Boost',
      description: 'Enhance memory formation and recall',
      duration: '30 min',
      difficulty: 'Intermediate',
      sessions: 89,
      effectiveness: '87%'
    },
    {
      id: '3',
      name: 'Creativity Unlock',
      description: 'Stimulate creative thinking patterns',
      duration: '25 min',
      difficulty: 'Advanced',
      sessions: 234,
      effectiveness: '91%'
    },
    {
      id: '4',
      name: 'Stress Reduction',
      description: 'Calm the mind and reduce anxiety',
      duration: '15 min',
      difficulty: 'Beginner',
      sessions: 445,
      effectiveness: '96%'
    }
  ];

  const thoughtCommands = [
    { command: 'Open App', confidence: 98, status: 'Active' },
    { command: 'Send Message', confidence: 94, status: 'Ready' },
    { command: 'Create Content', confidence: 89, status: 'Learning' },
    { command: 'Navigate Menu', confidence: 96, status: 'Active' },
    { command: 'Voice Control', confidence: 92, status: 'Ready' },
    { command: 'Emotion Express', confidence: 87, status: 'Training' }
  ];

  const emotionStates = [
    { emotion: 'Joy', intensity: 78, color: 'from-yellow-400 to-orange-400' },
    { emotion: 'Calm', intensity: 92, color: 'from-blue-400 to-cyan-400' },
    { emotion: 'Focus', intensity: 85, color: 'from-purple-400 to-pink-400' },
    { emotion: 'Creativity', intensity: 67, color: 'from-green-400 to-emerald-400' },
    { emotion: 'Confidence', intensity: 89, color: 'from-red-400 to-orange-400' },
    { emotion: 'Empathy', intensity: 74, color: 'from-pink-400 to-purple-400' }
  ];

  const memoryPalaces = [
    {
      id: '1',
      name: 'Learning Archive',
      type: 'Educational',
      memories: 2847,
      size: '12.4 GB',
      lastAccessed: '2 hours ago'
    },
    {
      id: '2',
      name: 'Creative Vault',
      type: 'Artistic',
      memories: 1923,
      size: '8.7 GB',
      lastAccessed: '1 day ago'
    },
    {
      id: '3',
      name: 'Personal Journey',
      type: 'Life Events',
      memories: 5634,
      size: '23.1 GB',
      lastAccessed: '3 hours ago'
    }
  ];

  const renderBrainwaves = () => (
    <div className="space-y-8">
      {/* Neural Interface Status */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white">Neural Interface Status</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm">Connected</span>
            </div>
            <div className="flex items-center space-x-2 text-white/60">
              <Signal className="h-4 w-4" />
              <Battery className="h-4 w-4" />
              <Wifi className="h-4 w-4" />
            </div>
          </div>
        </div>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">{neuralSync.toFixed(1)}%</div>
            <div className="text-white/60">Neural Sync</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">847 Hz</div>
            <div className="text-white/60">Dominant Frequency</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">94%</div>
            <div className="text-white/60">Signal Quality</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">12.4s</div>
            <div className="text-white/60">Response Time</div>
          </div>
        </div>
      </div>

      {/* Brainwave Visualization */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-6">Real-time Brainwave Activity</h4>
          <div className="space-y-4">
            {brainwaveTypes.map((wave) => (
              <div key={wave.name} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div>
                    <span className="text-white font-medium">{wave.name}</span>
                    <span className="text-white/60 text-sm ml-2">({wave.frequency})</span>
                  </div>
                  <span className="text-white/80 text-sm">{brainActivity[wave.name.toLowerCase() as keyof typeof brainActivity].toFixed(1)}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-3">
                  <div 
                    className={`bg-gradient-to-r ${wave.color} h-3 rounded-full transition-all duration-1000`}
                    style={{ width: `${brainActivity[wave.name.toLowerCase() as keyof typeof brainActivity]}%` }}
                  />
                </div>
                <p className="text-white/60 text-xs">{wave.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-6">Brainwave Spectrum</h4>
          <div className="h-64 bg-black/40 rounded-2xl p-4 relative overflow-hidden">
            <svg className="w-full h-full" viewBox="0 0 400 200">
              {brainwaveTypes.map((wave, index) => (
                <g key={wave.name}>
                  <path
                    d={`M 0 ${100 + index * 20} ${Array.from({ length: 40 }, (_, i) => 
                      `L ${i * 10} ${100 + index * 20 + Math.sin(Date.now() * 0.01 + i * 0.5 + index) * (brainActivity[wave.name.toLowerCase() as keyof typeof brainActivity] * 0.3)}`
                    ).join(' ')}`}
                    stroke={`hsl(${index * 60}, 70%, 60%)`}
                    strokeWidth="2"
                    fill="none"
                  />
                </g>
              ))}
            </svg>
          </div>
          <div className="flex justify-center space-x-4 mt-4">
            <button
              onClick={() => setIsRecording(!isRecording)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isRecording 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {isRecording ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              <span>{isRecording ? 'Stop' : 'Start'} Recording</span>
            </button>
            <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              <RotateCcw className="h-4 w-4" />
              <span>Reset</span>
            </button>
          </div>
        </div>
      </div>

      {/* Neural Insights */}
      <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
        <h4 className="text-xl font-semibold text-white mb-6">AI Neural Insights</h4>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Optimal State</h5>
            <p className="text-white/70 text-sm">Your brain is in an optimal state for creative work. Consider starting a new project.</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Target className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Focus Recommendation</h5>
            <p className="text-white/70 text-sm">Beta waves are elevated. Perfect time for analytical tasks and problem-solving.</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Moon className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Rest Suggestion</h5>
            <p className="text-white/70 text-sm">Consider a 10-minute meditation break to optimize theta wave production.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNeuralTraining = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Neural Training Programs</h3>
        <p className="text-white/70 text-lg">Enhance your cognitive abilities through targeted neural training</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {neuralTrainingPrograms.map((program) => (
          <div key={program.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h4 className="text-lg font-semibold text-white mb-2">{program.name}</h4>
                <p className="text-white/70 text-sm">{program.description}</p>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                program.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-400' :
                program.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-red-500/20 text-red-400'
              }`}>
                {program.difficulty}
              </span>
            </div>
            
            <div className="grid grid-cols-3 gap-4 mb-6 text-sm">
              <div>
                <div className="text-white/60">Duration</div>
                <div className="text-blue-400 font-semibold">{program.duration}</div>
              </div>
              <div>
                <div className="text-white/60">Sessions</div>
                <div className="text-green-400 font-semibold">{program.sessions}</div>
              </div>
              <div>
                <div className="text-white/60">Effectiveness</div>
                <div className="text-purple-400 font-semibold">{program.effectiveness}</div>
              </div>
            </div>

            <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors font-semibold">
              Start Training
            </button>
          </div>
        ))}
      </div>

      {/* Training Progress */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Your Training Progress</h4>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">47</div>
            <div className="text-white/60">Sessions Completed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">23.5h</div>
            <div className="text-white/60">Total Training Time</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">+34%</div>
            <div className="text-white/60">Cognitive Improvement</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">Level 12</div>
            <div className="text-white/60">Neural Mastery</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderThoughtInterface = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Thought-to-Action Interface</h3>
        <p className="text-white/70 text-lg">Control your digital environment with pure thought</p>
      </div>

      {/* Thought Commands */}
      <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
        <h4 className="text-xl font-semibold text-white mb-6">Active Thought Commands</h4>
        <div className="space-y-4">
          {thoughtCommands.map((command, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${
                  command.status === 'Active' ? 'bg-green-400' :
                  command.status === 'Ready' ? 'bg-blue-400' :
                  command.status === 'Learning' ? 'bg-yellow-400' : 'bg-orange-400'
                }`} />
                <span className="text-white font-medium">{command.command}</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-white/60 text-xs">Confidence</div>
                  <div className="text-green-400 font-semibold">{command.confidence}%</div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  command.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                  command.status === 'Ready' ? 'bg-blue-500/20 text-blue-400' :
                  command.status === 'Learning' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-orange-500/20 text-orange-400'
                }`}>
                  {command.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Thought Training */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-6">Thought Pattern Training</h4>
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Brain className="h-16 w-16 text-white" />
              </div>
              <p className="text-white/70 text-sm mb-4">Think of the action you want to perform</p>
              <button className="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors">
                Start Training Session
              </button>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-6">Command Accuracy</h4>
          <div className="space-y-4">
            {['Navigation', 'Text Input', 'App Control', 'Media Control'].map((category, index) => (
              <div key={category} className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70 text-sm">{category}</span>
                  <span className="text-white/70 text-sm">{85 + index * 3}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                    style={{ width: `${85 + index * 3}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderEmotionSync = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Emotion Synchronization</h3>
        <p className="text-white/70 text-lg">Monitor and enhance your emotional states</p>
      </div>

      {/* Current Emotional State */}
      <div className="bg-gradient-to-r from-pink-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Current Emotional Profile</h4>
        <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-4">
          {emotionStates.map((emotion) => (
            <div key={emotion.emotion} className="text-center">
              <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${emotion.color} mx-auto mb-3 flex items-center justify-center`}>
                <span className="text-white font-bold text-sm">{emotion.intensity}</span>
              </div>
              <div className="text-white font-medium text-sm">{emotion.emotion}</div>
              <div className="text-white/60 text-xs">{emotion.intensity}% intensity</div>
            </div>
          ))}
        </div>
      </div>

      {/* Emotion Enhancement Tools */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-6">Emotion Enhancement</h4>
          <div className="space-y-4">
            {[
              { name: 'Boost Creativity', icon: Lightbulb, color: 'from-yellow-500 to-orange-500' },
              { name: 'Increase Focus', icon: Target, color: 'from-blue-500 to-purple-500' },
              { name: 'Enhance Calm', icon: Moon, color: 'from-blue-400 to-cyan-400' },
              { name: 'Amplify Joy', icon: Sun, color: 'from-yellow-400 to-orange-400' }
            ].map((tool, index) => (
              <button key={index} className={`w-full bg-gradient-to-r ${tool.color} p-4 rounded-xl text-white hover:scale-105 transition-transform flex items-center space-x-3`}>
                <tool.icon className="h-6 w-6" />
                <span className="font-medium">{tool.name}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
          <h4 className="text-xl font-semibold text-white mb-6">Emotional Insights</h4>
          <div className="space-y-4">
            <div className="bg-blue-500/20 text-blue-400 p-4 rounded-lg">
              <div className="font-semibold mb-1">Peak Performance Window</div>
              <div className="text-sm">Your creativity levels are optimal between 2-4 PM today.</div>
            </div>
            <div className="bg-green-500/20 text-green-400 p-4 rounded-lg">
              <div className="font-semibold mb-1">Stress Management</div>
              <div className="text-sm">Consider a 5-minute breathing exercise to maintain calm levels.</div>
            </div>
            <div className="bg-purple-500/20 text-purple-400 p-4 rounded-lg">
              <div className="font-semibold mb-1">Social Connection</div>
              <div className="text-sm">Your empathy levels suggest it's a good time for team collaboration.</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMemoryPalace = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Digital Memory Palace</h3>
        <p className="text-white/70 text-lg">Store, organize, and access your memories with neural precision</p>
      </div>

      {/* Memory Palaces */}
      <div className="grid md:grid-cols-3 gap-6">
        {memoryPalaces.map((palace) => (
          <div key={palace.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-white">{palace.name}</h4>
              <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                {palace.type}
              </span>
            </div>
            
            <div className="space-y-3 mb-6 text-sm">
              <div className="flex justify-between">
                <span className="text-white/60">Memories</span>
                <span className="text-green-400 font-semibold">{palace.memories.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Storage Size</span>
                <span className="text-blue-400 font-semibold">{palace.size}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Last Accessed</span>
                <span className="text-purple-400 font-semibold">{palace.lastAccessed}</span>
              </div>
            </div>

            <div className="flex space-x-2">
              <button className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors text-sm">
                Enter Palace
              </button>
              <button className="bg-white/10 border border-white/20 text-white p-2 rounded-lg hover:bg-white/20 transition-colors">
                <Settings className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Memory Tools */}
      <div className="bg-gradient-to-r from-indigo-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Memory Enhancement Tools</h4>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Eye className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Visual Memory</h5>
            <p className="text-white/70 text-sm">Enhance visual memory encoding</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Headphones className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Audio Memory</h5>
            <p className="text-white/70 text-sm">Improve auditory recall abilities</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Semantic Memory</h5>
            <p className="text-white/70 text-sm">Strengthen conceptual understanding</p>
          </div>
          <div className="text-center">
            <div className="bg-gradient-to-r from-orange-500 to-red-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Activity className="h-6 w-6 text-white" />
            </div>
            <h5 className="text-white font-semibold mb-2">Motor Memory</h5>
            <p className="text-white/70 text-sm">Enhance muscle memory formation</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Neuro <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Interface</span>
          </h1>
          <p className="text-white/70 text-lg">Direct brain-computer interface for enhanced human-AI collaboration</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'brainwaves' && renderBrainwaves()}
          {activeTab === 'neural-training' && renderNeuralTraining()}
          {activeTab === 'thought-interface' && renderThoughtInterface()}
          {activeTab === 'emotion-sync' && renderEmotionSync()}
          {activeTab === 'memory-palace' && renderMemoryPalace()}
          {activeTab === 'consciousness' && (
            <div className="text-center py-12">
              <Lightbulb className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Consciousness Hub</h3>
              <p className="text-white/60">Explore the depths of human consciousness and AI awareness</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NeuroInterface;