import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { User } from '../models/user.model';
import { Product } from '../models/product.model';
import { Course } from '../models/course.model';
import { NFT } from '../models/nft.model';
import { Notification } from '../models/notification.model';
import { HealthMetrics, Workout, Doctor, FitnessGoals } from '../models/health.model';
import { AITool, AICredits } from '../models/ai-studio.model';
import { Wallet, DAOProposal } from '../models/blockchain.model';

// Load environment variables
dotenv.config();

const seedData = async () => {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/neurosphere';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Clear existing data
    await Promise.all([
      User.deleteMany({}),
      Product.deleteMany({}),
      Course.deleteMany({}),
      NFT.deleteMany({}),
      Notification.deleteMany({}),
      HealthMetrics.deleteMany({}),
      Workout.deleteMany({}),
      Doctor.deleteMany({}),
      FitnessGoals.deleteMany({}),
      AITool.deleteMany({}),
      AICredits.deleteMany({}),
      Wallet.deleteMany({}),
      DAOProposal.deleteMany({})
    ]);
    console.log('Cleared existing data');

    // Create admin user
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'Admin123!',
      roles: ['admin', 'creator'],
      level: 50,
      points: 100000,
      balance: 10000,
      subscriptions: ['premium', 'creator-pro'],
      verification: {
        isEmailVerified: true,
        isKYCVerified: true
      },
      profile: {
        bio: 'Platform Administrator',
        skills: ['Management', 'AI', 'Blockchain'],
        interests: ['Technology', 'Innovation']
      }
    });
    await adminUser.save();

    // Create test users
    const testUsers = [];
    for (let i = 1; i <= 5; i++) {
      const user = new User({
        name: `Test User ${i}`,
        email: `user${i}@neurosphere.com`,
        password: 'Test123!',
        roles: i <= 2 ? ['creator'] : ['user'],
        level: Math.floor(Math.random() * 20) + 1,
        points: Math.floor(Math.random() * 10000),
        balance: Math.floor(Math.random() * 1000),
        verification: {
          isEmailVerified: true
        },
        profile: {
          bio: `I'm test user ${i}`,
          skills: ['JavaScript', 'React', 'Node.js'],
          interests: ['Programming', 'AI', 'Web3']
        }
      });
      await user.save();
      testUsers.push(user);
    }

    // Create sample products
    const sampleProducts = [
      {
        name: 'AI-Powered Smart Watch',
        description: 'Revolutionary smartwatch with advanced AI capabilities',
        price: 299,
        originalPrice: 399,
        images: ['https://images.pexels.com/photos/393047/pexels-photo-393047.jpeg'],
        category: 'electronics',
        brand: 'TechNova',
        seller: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          rating: 4.8,
          verified: true
        },
        inventory: {
          stock: 50,
          sku: 'SW-001'
        },
        shipping: {
          weight: 0.2,
          dimensions: { length: 10, width: 5, height: 2 },
          freeShipping: true
        },
        status: 'active',
        featured: true
      },
      {
        name: 'Sustainable Fashion Bundle',
        description: 'Eco-friendly clothing collection made from recycled materials',
        price: 149,
        originalPrice: 199,
        images: ['https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg'],
        category: 'fashion',
        brand: 'EcoStyle',
        seller: {
          id: testUsers[1]._id,
          name: testUsers[1].name,
          avatar: testUsers[1].avatar,
          rating: 4.9,
          verified: true
        },
        inventory: {
          stock: 25,
          sku: 'FB-001'
        },
        shipping: {
          weight: 0.5,
          dimensions: { length: 30, width: 20, height: 5 },
          freeShipping: false,
          shippingCost: 9.99
        },
        status: 'active'
      }
    ];

    for (const productData of sampleProducts) {
      const product = new Product(productData);
      await product.save();
    }

    // Create sample courses
    const sampleCourses = [
      {
        title: 'Complete AI & Machine Learning Bootcamp',
        description: 'Master AI and ML from scratch with hands-on projects',
        instructor: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          bio: 'AI Expert with 10+ years experience',
          rating: 4.9
        },
        price: 299,
        originalPrice: 499,
        thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg',
        category: 'ai',
        level: 'Beginner',
        duration: 2520, // 42 hours in minutes
        skills: ['Python', 'TensorFlow', 'Neural Networks'],
        whatYouWillLearn: [
          'Build neural networks from scratch',
          'Implement machine learning algorithms',
          'Deploy AI models to production'
        ],
        isPublished: true,
        isFeatured: true,
        lessons: [
          {
            id: 'lesson-1',
            title: 'Introduction to AI',
            description: 'Overview of artificial intelligence',
            duration: 60,
            videoUrl: 'https://example.com/video1',
            isPreview: true,
            order: 1
          }
        ]
      }
    ];

    for (const courseData of sampleCourses) {
      const course = new Course(courseData);
      await course.save();
    }

    // Create sample NFTs
    const sampleNFTs = [
      {
        tokenId: 'NFT-001',
        contractAddress: '0x1234567890abcdef',
        blockchain: 'ethereum',
        name: 'Digital Art #001',
        description: 'Unique digital artwork created with AI',
        image: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg',
        creator: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          walletAddress: '0xabcdef1234567890'
        },
        owner: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          walletAddress: '0xabcdef1234567890'
        },
        collection: {
          name: 'AI Art Collection',
          symbol: 'AIAC',
          description: 'Collection of AI-generated artworks'
        },
        attributes: [
          { traitType: 'Style', value: 'Abstract' },
          { traitType: 'Color', value: 'Blue' },
          { traitType: 'Rarity', value: 'Rare' }
        ],
        metadata: {
          standard: 'ERC-721',
          ipfsHash: 'QmTest123',
          metadataUri: 'https://ipfs.io/ipfs/QmTest123'
        },
        pricing: {
          currentPrice: 0.5,
          currency: 'ETH',
          isForSale: true
        },
        royalty: {
          percentage: 10,
          recipient: '0xabcdef1234567890'
        },
        category: 'art',
        status: 'minted'
      }
    ];

    for (const nftData of sampleNFTs) {
      const nft = new NFT(nftData);
      await nft.save();
    }

    // Create sample notifications
    const sampleNotifications = [
      {
        recipient: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          email: testUsers[0].email
        },
        sender: {
          id: adminUser._id,
          name: adminUser.name,
          avatar: adminUser.avatar
        },
        type: 'success',
        category: 'general',
        title: 'Welcome to NeuroSphere!',
        message: 'Your account has been successfully created. Start exploring our amazing features!',
        priority: 'medium',
        channels: ['in-app', 'email']
      },
      {
        recipient: {
          id: testUsers[1]._id,
          name: testUsers[1].name,
          email: testUsers[1].email
        },
        type: 'order',
        category: 'order',
        title: 'Order Confirmation',
        message: 'Your order #NS-12345 has been confirmed and is being processed.',
        data: {
          orderId: 'NS-12345',
          amount: 299
        },
        priority: 'high',
        channels: ['in-app', 'email']
      },
      {
        recipient: {
          id: testUsers[2]._id,
          name: testUsers[2].name,
          email: testUsers[2].email
        },
        type: 'course',
        category: 'course',
        title: 'Course Enrollment',
        message: 'You have successfully enrolled in "AI & Machine Learning Bootcamp"',
        priority: 'medium',
        channels: ['in-app']
      }
    ];

    for (const notificationData of sampleNotifications) {
      const notification = new Notification(notificationData);
      await notification.save();
    }

    // Create sample doctors
    const sampleDoctors = [
      {
        name: 'Dr. Sarah Johnson',
        specialty: 'General Medicine',
        qualifications: ['MD', 'Internal Medicine Board Certified'],
        experience: 15,
        rating: 4.9,
        reviewCount: 234,
        avatar: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
        bio: 'Experienced general practitioner with focus on preventive care and patient education.',
        languages: ['English', 'Spanish'],
        consultationFee: 150,
        availability: [
          {
            day: 'Monday',
            slots: [
              { time: '09:00', available: true },
              { time: '10:00', available: true },
              { time: '14:00', available: false },
              { time: '15:00', available: true }
            ]
          }
        ],
        verified: true
      },
      {
        name: 'Dr. Michael Chen',
        specialty: 'Cardiology',
        qualifications: ['MD', 'Cardiology Fellowship', 'Board Certified'],
        experience: 20,
        rating: 4.8,
        reviewCount: 189,
        avatar: 'https://images.pexels.com/photos/5327656/pexels-photo-5327656.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
        bio: 'Cardiologist specializing in preventive cardiology and heart disease management.',
        languages: ['English', 'Mandarin'],
        consultationFee: 200,
        availability: [
          {
            day: 'Tuesday',
            slots: [
              { time: '10:00', available: true },
              { time: '11:00', available: true },
              { time: '16:00', available: true }
            ]
          }
        ],
        verified: true
      }
    ];

    for (const doctorData of sampleDoctors) {
      const doctor = new Doctor(doctorData);
      await doctor.save();
    }

    // Create sample workouts
    const sampleWorkouts = [
      {
        userId: testUsers[0]._id,
        name: 'Morning Cardio Blast',
        type: 'cardio',
        duration: 30,
        calories: 300,
        difficulty: 'intermediate',
        exercises: [
          { name: 'Jumping Jacks', duration: 60, restTime: 15 },
          { name: 'High Knees', duration: 45, restTime: 15 },
          { name: 'Burpees', reps: 10, restTime: 30 }
        ],
        thumbnail: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=400',
        instructions: ['Warm up for 5 minutes', 'Perform each exercise with proper form', 'Cool down and stretch'],
        equipment: ['None'],
        targetMuscles: ['Cardiovascular', 'Full Body'],
        completed: false
      },
      {
        userId: testUsers[1]._id,
        name: 'Strength Training',
        type: 'strength',
        duration: 45,
        calories: 250,
        difficulty: 'advanced',
        exercises: [
          { name: 'Push-ups', sets: 3, reps: 15, restTime: 60 },
          { name: 'Squats', sets: 3, reps: 20, restTime: 60 },
          { name: 'Plank', duration: 60, restTime: 30 }
        ],
        thumbnail: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&cs=tinysrgb&w=400',
        instructions: ['Focus on proper form', 'Control the movement', 'Breathe consistently'],
        equipment: ['None'],
        targetMuscles: ['Chest', 'Legs', 'Core'],
        completed: true,
        completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      }
    ];

    for (const workoutData of sampleWorkouts) {
      const workout = new Workout(workoutData);
      await workout.save();
    }

    // Create AI Tools
    const sampleAITools = [
      {
        name: 'Text Generator',
        type: 'text',
        description: 'Advanced text generation using GPT-4',
        model: 'gpt-4',
        version: '1.0',
        parameters: [
          { name: 'temperature', type: 'number', default: 0.7, min: 0, max: 2, required: false },
          { name: 'maxTokens', type: 'number', default: 1000, min: 1, max: 4000, required: false }
        ],
        creditsPerUse: 1,
        maxPromptLength: 2000,
        outputFormats: ['text', 'markdown'],
        subscriptionRequired: 'free'
      },
      {
        name: 'Image Creator',
        type: 'image',
        description: 'AI-powered image generation',
        model: 'dall-e-3',
        version: '1.0',
        parameters: [
          { name: 'style', type: 'select', default: 'realistic', options: ['realistic', 'artistic', 'cartoon'], required: false },
          { name: 'quality', type: 'select', default: 'standard', options: ['standard', 'hd'], required: false }
        ],
        creditsPerUse: 3,
        maxPromptLength: 1000,
        outputFormats: ['png', 'jpg'],
        subscriptionRequired: 'basic'
      }
    ];

    for (const toolData of sampleAITools) {
      const tool = new AITool(toolData);
      await tool.save();
    }

    // Create AI Credits for users
    for (const user of testUsers) {
      const credits = new AICredits({
        userId: user._id,
        totalCredits: 100,
        usedCredits: Math.floor(Math.random() * 30),
        remainingCredits: 100 - Math.floor(Math.random() * 30),
        subscriptionType: 'free'
      });
      await credits.save();
    }

    // Create sample DAO proposals
    const sampleProposals = [
      {
        proposalId: 'PROP-001',
        title: 'Increase Staking Rewards',
        description: 'Proposal to increase staking rewards by 2% across all protocols to incentivize more participation.',
        category: 'Protocol',
        proposer: adminUser._id,
        status: 'active',
        votesFor: 15420,
        votesAgainst: 3280,
        totalVotes: 18700,
        quorum: 10000,
        startDate: new Date(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      },
      {
        proposalId: 'PROP-002',
        title: 'New Protocol Integration',
        description: 'Add support for Polygon network protocols to expand our DeFi ecosystem.',
        category: 'Integration',
        proposer: testUsers[0]._id,
        status: 'pending',
        votesFor: 8950,
        votesAgainst: 1200,
        totalVotes: 10150,
        quorum: 10000,
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
      }
    ];

    for (const proposalData of sampleProposals) {
      const proposal = new DAOProposal(proposalData);
      await proposal.save();
    }

    console.log('✅ Seed data created successfully');
    console.log(`Created ${testUsers.length + 1} users`);
    console.log(`Created ${sampleProducts.length} products`);
    console.log(`Created ${sampleCourses.length} courses`);
    console.log(`Created ${sampleNFTs.length} NFTs`);
    console.log(`Created ${sampleNotifications.length} notifications`);
    console.log(`Created ${sampleDoctors.length} doctors`);
    console.log(`Created ${sampleWorkouts.length} workouts`);
    console.log(`Created ${sampleAITools.length} AI tools`);
    console.log(`Created ${testUsers.length} AI credit accounts`);
    console.log(`Created ${sampleProposals.length} DAO proposals`);

    // Close connection
    await mongoose.connection.close();
    console.log('Database connection closed');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
};

// Run seed script
if (require.main === module) {
  seedData();
}

export default seedData;
