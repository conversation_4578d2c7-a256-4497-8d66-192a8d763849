import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { User } from '../models/user.model';
import { Product } from '../models/product.model';
import { Course } from '../models/course.model';
import { NFT } from '../models/nft.model';

// Load environment variables
dotenv.config();

const seedData = async () => {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/neurosphere';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Clear existing data
    await Promise.all([
      User.deleteMany({}),
      Product.deleteMany({}),
      Course.deleteMany({}),
      NFT.deleteMany({})
    ]);
    console.log('Cleared existing data');

    // Create admin user
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'Admin123!',
      roles: ['admin', 'creator'],
      level: 50,
      points: 100000,
      balance: 10000,
      subscriptions: ['premium', 'creator-pro'],
      verification: {
        isEmailVerified: true,
        isKYCVerified: true
      },
      profile: {
        bio: 'Platform Administrator',
        skills: ['Management', 'AI', 'Blockchain'],
        interests: ['Technology', 'Innovation']
      }
    });
    await adminUser.save();

    // Create test users
    const testUsers = [];
    for (let i = 1; i <= 5; i++) {
      const user = new User({
        name: `Test User ${i}`,
        email: `user${i}@neurosphere.com`,
        password: 'Test123!',
        roles: i <= 2 ? ['creator'] : ['user'],
        level: Math.floor(Math.random() * 20) + 1,
        points: Math.floor(Math.random() * 10000),
        balance: Math.floor(Math.random() * 1000),
        verification: {
          isEmailVerified: true
        },
        profile: {
          bio: `I'm test user ${i}`,
          skills: ['JavaScript', 'React', 'Node.js'],
          interests: ['Programming', 'AI', 'Web3']
        }
      });
      await user.save();
      testUsers.push(user);
    }

    // Create sample products
    const sampleProducts = [
      {
        name: 'AI-Powered Smart Watch',
        description: 'Revolutionary smartwatch with advanced AI capabilities',
        price: 299,
        originalPrice: 399,
        images: ['https://images.pexels.com/photos/393047/pexels-photo-393047.jpeg'],
        category: 'electronics',
        brand: 'TechNova',
        seller: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          rating: 4.8,
          verified: true
        },
        inventory: {
          stock: 50,
          sku: 'SW-001'
        },
        shipping: {
          weight: 0.2,
          dimensions: { length: 10, width: 5, height: 2 },
          freeShipping: true
        },
        status: 'active',
        featured: true
      },
      {
        name: 'Sustainable Fashion Bundle',
        description: 'Eco-friendly clothing collection made from recycled materials',
        price: 149,
        originalPrice: 199,
        images: ['https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg'],
        category: 'fashion',
        brand: 'EcoStyle',
        seller: {
          id: testUsers[1]._id,
          name: testUsers[1].name,
          avatar: testUsers[1].avatar,
          rating: 4.9,
          verified: true
        },
        inventory: {
          stock: 25,
          sku: 'FB-001'
        },
        shipping: {
          weight: 0.5,
          dimensions: { length: 30, width: 20, height: 5 },
          freeShipping: false,
          shippingCost: 9.99
        },
        status: 'active'
      }
    ];

    for (const productData of sampleProducts) {
      const product = new Product(productData);
      await product.save();
    }

    // Create sample courses
    const sampleCourses = [
      {
        title: 'Complete AI & Machine Learning Bootcamp',
        description: 'Master AI and ML from scratch with hands-on projects',
        instructor: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          bio: 'AI Expert with 10+ years experience',
          rating: 4.9
        },
        price: 299,
        originalPrice: 499,
        thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg',
        category: 'ai',
        level: 'Beginner',
        duration: 2520, // 42 hours in minutes
        skills: ['Python', 'TensorFlow', 'Neural Networks'],
        whatYouWillLearn: [
          'Build neural networks from scratch',
          'Implement machine learning algorithms',
          'Deploy AI models to production'
        ],
        isPublished: true,
        isFeatured: true,
        lessons: [
          {
            id: 'lesson-1',
            title: 'Introduction to AI',
            description: 'Overview of artificial intelligence',
            duration: 60,
            videoUrl: 'https://example.com/video1',
            isPreview: true,
            order: 1
          }
        ]
      }
    ];

    for (const courseData of sampleCourses) {
      const course = new Course(courseData);
      await course.save();
    }

    // Create sample NFTs
    const sampleNFTs = [
      {
        tokenId: 'NFT-001',
        contractAddress: '0x1234567890abcdef',
        blockchain: 'ethereum',
        name: 'Digital Art #001',
        description: 'Unique digital artwork created with AI',
        image: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg',
        creator: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          walletAddress: '0xabcdef1234567890'
        },
        owner: {
          id: testUsers[0]._id,
          name: testUsers[0].name,
          avatar: testUsers[0].avatar,
          walletAddress: '0xabcdef1234567890'
        },
        collection: {
          name: 'AI Art Collection',
          symbol: 'AIAC',
          description: 'Collection of AI-generated artworks'
        },
        attributes: [
          { traitType: 'Style', value: 'Abstract' },
          { traitType: 'Color', value: 'Blue' },
          { traitType: 'Rarity', value: 'Rare' }
        ],
        metadata: {
          standard: 'ERC-721',
          ipfsHash: 'QmTest123',
          metadataUri: 'https://ipfs.io/ipfs/QmTest123'
        },
        pricing: {
          currentPrice: 0.5,
          currency: 'ETH',
          isForSale: true
        },
        royalty: {
          percentage: 10,
          recipient: '0xabcdef1234567890'
        },
        category: 'art',
        status: 'minted'
      }
    ];

    for (const nftData of sampleNFTs) {
      const nft = new NFT(nftData);
      await nft.save();
    }

    console.log('✅ Seed data created successfully');
    console.log(`Created ${testUsers.length + 1} users`);
    console.log(`Created ${sampleProducts.length} products`);
    console.log(`Created ${sampleCourses.length} courses`);
    console.log(`Created ${sampleNFTs.length} NFTs`);

    // Close connection
    await mongoose.connection.close();
    console.log('Database connection closed');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
};

// Run seed script
if (require.main === module) {
  seedData();
}

export default seedData;
