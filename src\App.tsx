
import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import NotificationSystem from './components/NotificationSystem';
import HomePage from './pages/HomePage';
import SocialCommerce from './pages/SocialCommerce';
import CreatorHub from './pages/CreatorHub';
import LearningPlatform from './pages/LearningPlatform';
import FinTechServices from './pages/FinTechServices';
import HealthWellness from './pages/HealthWellness';
import ProductivitySuite from './pages/ProductivitySuite';
import Profile from './pages/Profile';
import AICreativityStudio from './pages/AICreativityStudio';
import QuantumAI from './pages/QuantumAI';
import MetaverseHub from './pages/MetaverseHub';
import BlockchainEcosystem from './pages/BlockchainEcosystem';
import NeuroInterface from './pages/NeuroInterface';
import RealTimeChat from './components/RealTimeChat';
import LiveStreaming from './components/LiveStreaming';
import AdvancedAnalytics from './components/AdvancedAnalytics';
import SmartContracts from './components/SmartContracts';
import Stream from './pages/Stream';
import { AppProvider, useApp } from './context/AppContext';
import wsService from './services/websocket';

// Main App Component with WebSocket Integration
const AppContent: React.FC = () => {
  const { state } = useApp();

  // Initialize WebSocket connection when user is authenticated
  useEffect(() => {
    if (state.isAuthenticated && state.user) {
      const token = localStorage.getItem('authToken');
      if (token) {
        wsService.connect(token);
      }
    } else {
      wsService.disconnect();
    }

    return () => {
      wsService.disconnect();
    };
  }, [state.isAuthenticated]);

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  return (
    <Router>
      <Layout>
        {/* Notification System */}
        <NotificationSystem />

        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/commerce" element={<SocialCommerce />} />
          <Route path="/creator" element={<CreatorHub />} />
          <Route path="/learn" element={<LearningPlatform />} />
          <Route path="/fintech" element={<FinTechServices />} />
          <Route path="/health" element={<HealthWellness />} />
          <Route path="/productivity" element={<ProductivitySuite />} />
          <Route path="/ai-studio" element={<AICreativityStudio />} />
          <Route path="/quantum-ai" element={<QuantumAI />} />
          <Route path="/metaverse" element={<MetaverseHub />} />
          <Route path="/blockchain" element={<BlockchainEcosystem />} />
          <Route path="/neuro-interface" element={<NeuroInterface />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/chat" element={<RealTimeChat />} />
          <Route path="/streaming" element={<LiveStreaming />} />
          <Route path="/analytics" element={<AdvancedAnalytics />} />
          <Route path="/contracts" element={<SmartContracts />} />
          <Route path="/stream" element={<Stream />} />
        </Routes>
      </Layout>
    </Router>
  );
};

function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

export default App;