import mongoose, { Document, Schema } from 'mongoose';

export interface ICourse extends Document {
  _id: string;
  title: string;
  description: string;
  instructor: {
    id: string;
    name: string;
    avatar: string;
    bio: string;
    rating: number;
  };
  price: number;
  originalPrice: number;
  currency: string;
  thumbnail: string;
  previewVideo?: string;
  category: string;
  subcategory: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  duration: number; // in minutes
  lessons: Array<{
    id: string;
    title: string;
    description: string;
    duration: number;
    videoUrl: string;
    resources: Array<{
      title: string;
      url: string;
      type: 'pdf' | 'video' | 'audio' | 'text' | 'code';
    }>;
    quiz?: {
      questions: Array<{
        question: string;
        options: string[];
        correctAnswer: number;
        explanation: string;
      }>;
    };
    isPreview: boolean;
    order: number;
  }>;
  skills: string[];
  requirements: string[];
  whatYouWillLearn: string[];
  rating: {
    average: number;
    count: number;
    distribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
  };
  reviews: Array<{
    userId: string;
    userName: string;
    userAvatar: string;
    rating: number;
    comment: string;
    createdAt: Date;
    helpful: number;
  }>;
  enrollment: {
    totalStudents: number;
    activeStudents: number;
    completionRate: number;
  };
  certificate: {
    available: boolean;
    template: string;
    criteria: {
      minCompletionRate: number;
      minQuizScore: number;
    };
  };
  tags: string[];
  language: string;
  subtitles: string[];
  isPublished: boolean;
  isFeatured: boolean;
  publishedAt?: Date;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

const courseSchema = new Schema<ICourse>({
  title: {
    type: String,
    required: [true, 'Course title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Course description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  instructor: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: { type: String, required: true },
    avatar: String,
    bio: String,
    rating: { type: Number, default: 0, min: 0, max: 5 }
  },
  price: {
    type: Number,
    required: [true, 'Course price is required'],
    min: [0, 'Price cannot be negative']
  },
  originalPrice: {
    type: Number,
    required: [true, 'Original price is required'],
    min: [0, 'Original price cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD']
  },
  thumbnail: {
    type: String,
    required: [true, 'Course thumbnail is required']
  },
  previewVideo: String,
  category: {
    type: String,
    required: [true, 'Course category is required'],
    enum: ['ai', 'blockchain', 'business', 'design', 'tech', 'marketing', 'health', 'finance']
  },
  subcategory: String,
  level: {
    type: String,
    required: [true, 'Course level is required'],
    enum: ['Beginner', 'Intermediate', 'Advanced', 'Expert']
  },
  duration: {
    type: Number,
    required: [true, 'Course duration is required'],
    min: [1, 'Duration must be at least 1 minute']
  },
  lessons: [{
    id: { type: String, required: true },
    title: { type: String, required: true },
    description: String,
    duration: { type: Number, required: true },
    videoUrl: { type: String, required: true },
    resources: [{
      title: String,
      url: String,
      type: {
        type: String,
        enum: ['pdf', 'video', 'audio', 'text', 'code']
      }
    }],
    quiz: {
      questions: [{
        question: String,
        options: [String],
        correctAnswer: Number,
        explanation: String
      }]
    },
    isPreview: { type: Boolean, default: false },
    order: { type: Number, required: true }
  }],
  skills: [String],
  requirements: [String],
  whatYouWillLearn: [String],
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0 },
    distribution: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 }
    }
  },
  reviews: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    userName: String,
    userAvatar: String,
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    createdAt: { type: Date, default: Date.now },
    helpful: { type: Number, default: 0 }
  }],
  enrollment: {
    totalStudents: { type: Number, default: 0 },
    activeStudents: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0, min: 0, max: 100 }
  },
  certificate: {
    available: { type: Boolean, default: false },
    template: String,
    criteria: {
      minCompletionRate: { type: Number, default: 80 },
      minQuizScore: { type: Number, default: 70 }
    }
  },
  tags: [String],
  language: { type: String, default: 'en' },
  subtitles: [String],
  isPublished: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  publishedAt: Date,
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
courseSchema.index({ category: 1, level: 1 });
courseSchema.index({ 'rating.average': -1 });
courseSchema.index({ price: 1 });
courseSchema.index({ createdAt: -1 });
courseSchema.index({ isPublished: 1, isFeatured: -1 });
courseSchema.index({ 'instructor.id': 1 });

// Virtual for discount percentage
courseSchema.virtual('discountPercentage').get(function() {
  if (this.originalPrice > this.price) {
    return Math.round((1 - this.price / this.originalPrice) * 100);
  }
  return 0;
});

// Virtual for total lessons count
courseSchema.virtual('totalLessons').get(function() {
  return this.lessons.length;
});

export const Course = mongoose.model<ICourse>('Course', courseSchema);
