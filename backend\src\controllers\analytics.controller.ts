import { Request, Response } from 'express';
import { User } from '../models/user.model';
import { Product } from '../models/product.model';
import { Course } from '../models/course.model';
import { Order } from '../models/order.model';
import { NFT } from '../models/nft.model';

// Get platform overview statistics
export const getPlatformStats = async (req: Request, res: Response) => {
  try {
    if (!req.user || !req.user.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const [
      totalUsers,
      activeUsers,
      totalProducts,
      totalCourses,
      totalOrders,
      totalNFTs,
      totalRevenue
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ 
        'activity.lastActive': { 
          $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) 
        } 
      }),
      Product.countDocuments({ status: 'active' }),
      Course.countDocuments({ isPublished: true }),
      Order.countDocuments(),
      NFT.countDocuments({ status: { $ne: 'burned' } }),
      Order.aggregate([
        { $match: { 'payment.status': 'completed' } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ])
    ]);

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers,
        growth: 0 // Would calculate from historical data
      },
      products: {
        total: totalProducts,
        growth: 0
      },
      courses: {
        total: totalCourses,
        growth: 0
      },
      orders: {
        total: totalOrders,
        growth: 0
      },
      nfts: {
        total: totalNFTs,
        growth: 0
      },
      revenue: {
        total: totalRevenue[0]?.total || 0,
        growth: 0
      }
    };

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error: any) {
    console.error('Get platform stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch platform statistics',
      error: error.message
    });
  }
};

// Get user dashboard analytics
export const getUserDashboard = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userId = req.user._id;

    // Get user's products, courses, orders, and NFTs
    const [
      userProducts,
      userCourses,
      userOrders,
      userNFTs,
      recentActivity
    ] = await Promise.all([
      Product.countDocuments({ 'seller.id': userId }),
      Course.countDocuments({ 'instructor.id': userId }),
      Order.countDocuments({ 'user.id': userId }),
      NFT.countDocuments({ 'owner.id': userId }),
      // Mock recent activity - in real app would come from activity collection
      Promise.resolve([
        {
          type: 'order',
          description: 'New order received',
          timestamp: new Date(),
          amount: 299
        },
        {
          type: 'course',
          description: 'Student enrolled in your course',
          timestamp: new Date(Date.now() - 3600000),
          amount: 99
        }
      ])
    ]);

    // Calculate earnings (simplified)
    const earnings = await Order.aggregate([
      { 
        $match: { 
          'user.id': userId,
          'payment.status': 'completed'
        } 
      },
      { 
        $group: { 
          _id: null, 
          total: { $sum: '$pricing.total' },
          thisMonth: {
            $sum: {
              $cond: [
                {
                  $gte: [
                    '$createdAt',
                    new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                  ]
                },
                '$pricing.total',
                0
              ]
            }
          }
        } 
      }
    ]);

    const dashboard = {
      overview: {
        products: userProducts,
        courses: userCourses,
        orders: userOrders,
        nfts: userNFTs
      },
      earnings: {
        total: earnings[0]?.total || 0,
        thisMonth: earnings[0]?.thisMonth || 0
      },
      user: {
        level: req.user.level,
        points: req.user.points,
        balance: req.user.balance,
        subscriptions: req.user.subscriptions
      },
      recentActivity
    };

    res.json({
      success: true,
      data: { dashboard }
    });
  } catch (error: any) {
    console.error('Get user dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data',
      error: error.message
    });
  }
};

// Get sales analytics
export const getSalesAnalytics = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { period = '30d' } = req.query;
    
    // Calculate date range
    let startDate: Date;
    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get sales data
    const salesData = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          'payment.status': 'completed'
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          sales: { $sum: '$pricing.total' },
          orders: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get top products
    const topProducts = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          'payment.status': 'completed'
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.productId',
          name: { $first: '$items.productName' },
          sales: { $sum: '$items.totalPrice' },
          quantity: { $sum: '$items.quantity' }
        }
      },
      { $sort: { sales: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        salesData,
        topProducts,
        period
      }
    });
  } catch (error: any) {
    console.error('Get sales analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sales analytics',
      error: error.message
    });
  }
};

// Get course analytics
export const getCourseAnalytics = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get course performance data
    const courseStats = await Course.aggregate([
      {
        $match: {
          'instructor.id': req.user._id,
          isPublished: true
        }
      },
      {
        $group: {
          _id: null,
          totalCourses: { $sum: 1 },
          totalStudents: { $sum: '$enrollment.totalStudents' },
          averageRating: { $avg: '$rating.average' },
          totalRevenue: { $sum: { $multiply: ['$price', '$enrollment.totalStudents'] } }
        }
      }
    ]);

    // Get top courses
    const topCourses = await Course.find({
      'instructor.id': req.user._id,
      isPublished: true
    })
    .sort({ 'enrollment.totalStudents': -1 })
    .limit(5)
    .select('title enrollment.totalStudents rating.average price');

    res.json({
      success: true,
      data: {
        overview: courseStats[0] || {
          totalCourses: 0,
          totalStudents: 0,
          averageRating: 0,
          totalRevenue: 0
        },
        topCourses
      }
    });
  } catch (error: any) {
    console.error('Get course analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch course analytics',
      error: error.message
    });
  }
};

// Get NFT analytics
export const getNFTAnalytics = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get NFT statistics
    const nftStats = await NFT.aggregate([
      {
        $match: {
          'creator.id': req.user._id
        }
      },
      {
        $group: {
          _id: null,
          totalNFTs: { $sum: 1 },
          totalViews: { $sum: '$trading.views' },
          totalLikes: { $sum: '$trading.likes' },
          totalSales: { $sum: '$trading.totalSales' },
          totalRevenue: { $sum: '$trading.highestSale' }
        }
      }
    ]);

    // Get top NFTs
    const topNFTs = await NFT.find({
      'creator.id': req.user._id
    })
    .sort({ 'trading.views': -1 })
    .limit(5)
    .select('name trading category pricing.currentPrice');

    res.json({
      success: true,
      data: {
        overview: nftStats[0] || {
          totalNFTs: 0,
          totalViews: 0,
          totalLikes: 0,
          totalSales: 0,
          totalRevenue: 0
        },
        topNFTs
      }
    });
  } catch (error: any) {
    console.error('Get NFT analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch NFT analytics',
      error: error.message
    });
  }
};
