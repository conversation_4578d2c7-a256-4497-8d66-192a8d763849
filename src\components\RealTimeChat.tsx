import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, Send, Smile, Paperclip, Mic, Video, Phone, Users, Settings, Search, Star, Heart, Zap, Brain } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface Message {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'voice' | 'video' | 'ai-generated';
  reactions?: { emoji: string; count: number; users: string[] }[];
  isAI?: boolean;
  aiPersonality?: string;
}

interface ChatRoom {
  id: string;
  name: string;
  type: 'public' | 'private' | 'ai-enhanced' | 'neural-sync';
  participants: number;
  description: string;
  tags: string[];
}

const RealTimeChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [activeRoom, setActiveRoom] = useState<string>('general');
  const [isTyping, setIsTyping] = useState<string[]>([]);
  const [voiceMode, setVoiceMode] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { state } = useApp();

  const chatRooms: ChatRoom[] = [
    {
      id: 'general',
      name: 'General Discussion',
      type: 'public',
      participants: 15420,
      description: 'Global community chat',
      tags: ['general', 'community']
    },
    {
      id: 'ai-creators',
      name: 'AI Creators Hub',
      type: 'ai-enhanced',
      participants: 8934,
      description: 'AI-powered creative collaboration',
      tags: ['ai', 'creativity', 'collaboration']
    },
    {
      id: 'neural-sync',
      name: 'Neural Sync Chamber',
      type: 'neural-sync',
      participants: 2847,
      description: 'Direct brain-to-brain communication',
      tags: ['neural', 'consciousness', 'sync']
    },
    {
      id: 'quantum-lab',
      name: 'Quantum Laboratory',
      type: 'private',
      participants: 567,
      description: 'Quantum computing discussions',
      tags: ['quantum', 'science', 'research']
    }
  ];

  const sampleMessages: Message[] = [
    {
      id: '1',
      userId: 'ai-aria',
      userName: 'ARIA (AI)',
      userAvatar: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&dpr=2',
      content: 'Welcome to the Neural Sync Chamber! I can feel the collective consciousness energy here. 🧠✨',
      timestamp: new Date(Date.now() - 300000),
      type: 'text',
      isAI: true,
      aiPersonality: 'empathetic',
      reactions: [
        { emoji: '🧠', count: 12, users: ['user1', 'user2'] },
        { emoji: '✨', count: 8, users: ['user3', 'user4'] }
      ]
    },
    {
      id: '2',
      userId: 'user1',
      userName: 'QuantumExplorer',
      userAvatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&dpr=2',
      content: 'Just achieved quantum coherence level Ω! The possibilities are infinite now! 🚀',
      timestamp: new Date(Date.now() - 240000),
      type: 'text',
      reactions: [
        { emoji: '🚀', count: 15, users: ['user2', 'user3'] },
        { emoji: '🎉', count: 9, users: ['user4', 'user5'] }
      ]
    },
    {
      id: '3',
      userId: 'ai-nexus',
      userName: 'NEXUS-Prime (AI)',
      userAvatar: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&dpr=2',
      content: 'Analyzing market patterns across 10^12 parallel universes. Optimal investment strategy detected. Would you like me to share the insights?',
      timestamp: new Date(Date.now() - 180000),
      type: 'text',
      isAI: true,
      aiPersonality: 'analytical'
    }
  ];

  useEffect(() => {
    setMessages(sampleMessages);
  }, [activeRoom]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message: Message = {
      id: Date.now().toString(),
      userId: state.user?.id || 'current-user',
      userName: state.user?.name || 'You',
      userAvatar: state.user?.avatar || '',
      content: newMessage,
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // Simulate AI response in AI-enhanced rooms
    if (chatRooms.find(room => room.id === activeRoom)?.type === 'ai-enhanced') {
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          userId: 'ai-aria',
          userName: 'ARIA (AI)',
          userAvatar: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&dpr=2',
          content: generateAIResponse(newMessage),
          timestamp: new Date(),
          type: 'ai-generated',
          isAI: true,
          aiPersonality: 'creative'
        };
        setMessages(prev => [...prev, aiResponse]);
      }, 1000);
    }
  };

  const generateAIResponse = (userMessage: string): string => {
    const responses = [
      "That's a fascinating perspective! I can sense the creative energy in your thoughts. 🎨",
      "Your neural patterns suggest you're in an optimal state for innovation. Let's explore this further! 🧠",
      "I'm analyzing quantum probability matrices to enhance your idea. The potential is infinite! ⚛️",
      "Your consciousness frequency is perfectly aligned with the creative field. Amazing insights! ✨",
      "I can feel the collective intelligence resonating with your concept. Let's amplify it! 🚀"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const addReaction = (messageId: string, emoji: string) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const existingReaction = msg.reactions?.find(r => r.emoji === emoji);
        if (existingReaction) {
          existingReaction.count += 1;
          existingReaction.users.push(state.user?.id || 'current-user');
        } else {
          msg.reactions = msg.reactions || [];
          msg.reactions.push({
            emoji,
            count: 1,
            users: [state.user?.id || 'current-user']
          });
        }
      }
      return msg;
    }));
  };

  const getRoomTypeColor = (type: string) => {
    switch (type) {
      case 'ai-enhanced': return 'from-purple-500 to-pink-500';
      case 'neural-sync': return 'from-blue-500 to-cyan-500';
      case 'private': return 'from-green-500 to-emerald-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Chat Rooms Sidebar */}
      <div className="w-80 bg-gradient-to-b from-white/10 to-white/5 backdrop-blur-sm border-r border-white/10 p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-4">Chat Rooms</h2>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
            <input
              type="text"
              placeholder="Search rooms..."
              className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
        </div>

        <div className="space-y-3">
          {chatRooms.map((room) => (
            <button
              key={room.id}
              onClick={() => setActiveRoom(room.id)}
              className={`w-full text-left p-4 rounded-xl transition-all duration-200 ${
                activeRoom === room.id
                  ? 'bg-gradient-to-r from-purple-600/30 to-blue-600/30 border border-purple-500/50'
                  : 'bg-white/5 hover:bg-white/10 border border-white/10'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white font-semibold">{room.name}</h3>
                <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${getRoomTypeColor(room.type)}`} />
              </div>
              <p className="text-white/70 text-sm mb-2">{room.description}</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-white/60 text-xs">
                  <Users className="h-3 w-3" />
                  <span>{room.participants.toLocaleString()}</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {room.tags.slice(0, 2).map((tag, index) => (
                    <span key={index} className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border-b border-white/10 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">
                {chatRooms.find(room => room.id === activeRoom)?.name}
              </h1>
              <p className="text-white/70">
                {chatRooms.find(room => room.id === activeRoom)?.participants.toLocaleString()} participants
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button className="p-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors">
                <Phone className="h-5 w-5" />
              </button>
              <button className="p-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors">
                <Video className="h-5 w-5" />
              </button>
              <button className="p-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors">
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex space-x-3 ${message.isAI ? 'bg-gradient-to-r from-purple-500/10 to-blue-500/10 p-4 rounded-2xl' : ''}`}>
              <img
                src={message.userAvatar}
                alt={message.userName}
                className="w-10 h-10 rounded-full border-2 border-purple-400"
              />
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-white font-semibold">{message.userName}</span>
                  {message.isAI && (
                    <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs">
                      AI
                    </span>
                  )}
                  <span className="text-white/60 text-sm">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="text-white/90 mb-2">{message.content}</p>
                {message.reactions && message.reactions.length > 0 && (
                  <div className="flex items-center space-x-2">
                    {message.reactions.map((reaction, index) => (
                      <button
                        key={index}
                        onClick={() => addReaction(message.id, reaction.emoji)}
                        className="bg-white/10 hover:bg-white/20 px-2 py-1 rounded-full text-sm transition-colors"
                      >
                        {reaction.emoji} {reaction.count}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              <div className="flex flex-col space-y-1">
                <button
                  onClick={() => addReaction(message.id, '❤️')}
                  className="p-1 text-white/60 hover:text-red-400 transition-colors"
                >
                  <Heart className="h-4 w-4" />
                </button>
                <button
                  onClick={() => addReaction(message.id, '⚡')}
                  className="p-1 text-white/60 hover:text-yellow-400 transition-colors"
                >
                  <Zap className="h-4 w-4" />
                </button>
                <button
                  onClick={() => addReaction(message.id, '🧠')}
                  className="p-1 text-white/60 hover:text-purple-400 transition-colors"
                >
                  <Brain className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Typing Indicators */}
        {isTyping.length > 0 && (
          <div className="px-6 py-2">
            <div className="text-white/60 text-sm">
              {isTyping.join(', ')} {isTyping.length === 1 ? 'is' : 'are'} typing...
            </div>
          </div>
        )}

        {/* Message Input */}
        <div className="bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border-t border-white/10 p-6">
          <div className="flex items-center space-x-3">
            <button className="p-2 text-white/60 hover:text-white transition-colors">
              <Paperclip className="h-5 w-5" />
            </button>
            <button className="p-2 text-white/60 hover:text-white transition-colors">
              <Smile className="h-5 w-5" />
            </button>
            <div className="flex-1 relative">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Type your message... or think it if you're in Neural Sync mode"
                className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <button
              onClick={() => setVoiceMode(!voiceMode)}
              className={`p-2 rounded-lg transition-colors ${
                voiceMode ? 'bg-red-500 text-white' : 'text-white/60 hover:text-white'
              }`}
            >
              <Mic className="h-5 w-5" />
            </button>
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim()}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeChat;