import mongoose, { Document, Schema } from 'mongoose';

// AI Creation Interface
export interface IAICreation extends Document {
  userId: mongoose.Types.ObjectId;
  title: string;
  description: string;
  type: 'text' | 'image' | 'music' | 'video' | 'voice' | 'design';
  prompt: string;
  content: {
    text?: string;
    imageUrl?: string;
    audioUrl?: string;
    videoUrl?: string;
    designData?: any;
  };
  metadata: {
    model: string;
    parameters: any;
    generationTime: number;
    creditsUsed: number;
  };
  tags: string[];
  isPublic: boolean;
  isForSale: boolean;
  price?: number;
  likes: number;
  views: number;
  downloads: number;
  nftTokenId?: string;
  collaborators: mongoose.Types.ObjectId[];
  status: 'generating' | 'completed' | 'failed' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

// AI Generation History Interface
export interface IAIGenerationHistory extends Document {
  userId: mongoose.Types.ObjectId;
  type: 'text' | 'image' | 'music' | 'video' | 'voice' | 'design';
  prompt: string;
  model: string;
  parameters: any;
  result: any;
  creditsUsed: number;
  generationTime: number;
  status: 'success' | 'failed' | 'pending';
  errorMessage?: string;
  createdAt: Date;
}

// AI Credits Interface
export interface IAICredits extends Document {
  userId: mongoose.Types.ObjectId;
  totalCredits: number;
  usedCredits: number;
  remainingCredits: number;
  subscriptionType: 'free' | 'basic' | 'pro' | 'enterprise';
  subscriptionExpiry?: Date;
  transactions: Array<{
    type: 'purchase' | 'usage' | 'refund' | 'bonus';
    amount: number;
    description: string;
    date: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

// Collaboration Project Interface
export interface ICollaborationProject extends Document {
  title: string;
  description: string;
  owner: mongoose.Types.ObjectId;
  collaborators: Array<{
    userId: mongoose.Types.ObjectId;
    role: 'editor' | 'viewer' | 'admin';
    joinedAt: Date;
    permissions: string[];
  }>;
  type: 'text' | 'image' | 'music' | 'video' | 'mixed';
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  assets: Array<{
    id: string;
    type: string;
    url: string;
    createdBy: mongoose.Types.ObjectId;
    createdAt: Date;
  }>;
  settings: {
    isPublic: boolean;
    allowInvites: boolean;
    maxCollaborators: number;
  };
  deadline?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// AI Marketplace Item Interface
export interface IAIMarketplaceItem extends Document {
  creationId: mongoose.Types.ObjectId;
  sellerId: mongoose.Types.ObjectId;
  title: string;
  description: string;
  type: 'text' | 'image' | 'music' | 'video' | 'voice' | 'design';
  price: number;
  currency: 'USD' | 'ETH' | 'NEURO';
  category: string;
  tags: string[];
  previewUrl: string;
  fullContentUrl: string;
  license: 'personal' | 'commercial' | 'extended';
  sales: number;
  rating: number;
  reviews: Array<{
    userId: mongoose.Types.ObjectId;
    rating: number;
    comment: string;
    date: Date;
  }>;
  isActive: boolean;
  isFeatured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// AI Tool Configuration Interface
export interface IAITool extends Document {
  name: string;
  type: 'text' | 'image' | 'music' | 'video' | 'voice' | 'design';
  description: string;
  model: string;
  version: string;
  parameters: {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'select';
    default: any;
    options?: any[];
    min?: number;
    max?: number;
    required: boolean;
  }[];
  creditsPerUse: number;
  maxPromptLength: number;
  outputFormats: string[];
  isActive: boolean;
  subscriptionRequired: 'free' | 'basic' | 'pro' | 'enterprise';
  createdAt: Date;
  updatedAt: Date;
}

// Schemas
const AICreationSchema = new Schema<IAICreation>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['text', 'image', 'music', 'video', 'voice', 'design'],
    required: true 
  },
  prompt: { type: String, required: true },
  content: {
    text: String,
    imageUrl: String,
    audioUrl: String,
    videoUrl: String,
    designData: Schema.Types.Mixed
  },
  metadata: {
    model: { type: String, required: true },
    parameters: { type: Schema.Types.Mixed, required: true },
    generationTime: { type: Number, required: true },
    creditsUsed: { type: Number, required: true }
  },
  tags: [String],
  isPublic: { type: Boolean, default: false },
  isForSale: { type: Boolean, default: false },
  price: Number,
  likes: { type: Number, default: 0 },
  views: { type: Number, default: 0 },
  downloads: { type: Number, default: 0 },
  nftTokenId: String,
  collaborators: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  status: { 
    type: String, 
    enum: ['generating', 'completed', 'failed', 'archived'],
    default: 'generating'
  }
}, {
  timestamps: true
});

const AIGenerationHistorySchema = new Schema<IAIGenerationHistory>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { 
    type: String, 
    enum: ['text', 'image', 'music', 'video', 'voice', 'design'],
    required: true 
  },
  prompt: { type: String, required: true },
  model: { type: String, required: true },
  parameters: { type: Schema.Types.Mixed, required: true },
  result: { type: Schema.Types.Mixed },
  creditsUsed: { type: Number, required: true },
  generationTime: { type: Number, required: true },
  status: { 
    type: String, 
    enum: ['success', 'failed', 'pending'],
    required: true 
  },
  errorMessage: String
}, {
  timestamps: true
});

const AICreditsSchema = new Schema<IAICredits>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
  totalCredits: { type: Number, default: 0 },
  usedCredits: { type: Number, default: 0 },
  remainingCredits: { type: Number, default: 0 },
  subscriptionType: { 
    type: String, 
    enum: ['free', 'basic', 'pro', 'enterprise'],
    default: 'free'
  },
  subscriptionExpiry: Date,
  transactions: [{
    type: { 
      type: String, 
      enum: ['purchase', 'usage', 'refund', 'bonus'],
      required: true 
    },
    amount: { type: Number, required: true },
    description: { type: String, required: true },
    date: { type: Date, default: Date.now }
  }]
}, {
  timestamps: true
});

const CollaborationProjectSchema = new Schema<ICollaborationProject>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  collaborators: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    role: { 
      type: String, 
      enum: ['editor', 'viewer', 'admin'],
      default: 'editor'
    },
    joinedAt: { type: Date, default: Date.now },
    permissions: [String]
  }],
  type: { 
    type: String, 
    enum: ['text', 'image', 'music', 'video', 'mixed'],
    required: true 
  },
  status: { 
    type: String, 
    enum: ['active', 'completed', 'paused', 'cancelled'],
    default: 'active'
  },
  assets: [{
    id: { type: String, required: true },
    type: { type: String, required: true },
    url: { type: String, required: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    createdAt: { type: Date, default: Date.now }
  }],
  settings: {
    isPublic: { type: Boolean, default: false },
    allowInvites: { type: Boolean, default: true },
    maxCollaborators: { type: Number, default: 10 }
  },
  deadline: Date
}, {
  timestamps: true
});

const AIMarketplaceItemSchema = new Schema<IAIMarketplaceItem>({
  creationId: { type: Schema.Types.ObjectId, ref: 'AICreation', required: true },
  sellerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['text', 'image', 'music', 'video', 'voice', 'design'],
    required: true 
  },
  price: { type: Number, required: true },
  currency: { 
    type: String, 
    enum: ['USD', 'ETH', 'NEURO'],
    default: 'USD'
  },
  category: { type: String, required: true },
  tags: [String],
  previewUrl: { type: String, required: true },
  fullContentUrl: { type: String, required: true },
  license: { 
    type: String, 
    enum: ['personal', 'commercial', 'extended'],
    default: 'personal'
  },
  sales: { type: Number, default: 0 },
  rating: { type: Number, default: 0 },
  reviews: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    rating: { type: Number, min: 1, max: 5, required: true },
    comment: { type: String, required: true },
    date: { type: Date, default: Date.now }
  }],
  isActive: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false }
}, {
  timestamps: true
});

const AIToolSchema = new Schema<IAITool>({
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['text', 'image', 'music', 'video', 'voice', 'design'],
    required: true 
  },
  description: { type: String, required: true },
  model: { type: String, required: true },
  version: { type: String, required: true },
  parameters: [{
    name: { type: String, required: true },
    type: { 
      type: String, 
      enum: ['string', 'number', 'boolean', 'select'],
      required: true 
    },
    default: { type: Schema.Types.Mixed, required: true },
    options: [Schema.Types.Mixed],
    min: Number,
    max: Number,
    required: { type: Boolean, default: false }
  }],
  creditsPerUse: { type: Number, required: true },
  maxPromptLength: { type: Number, required: true },
  outputFormats: [String],
  isActive: { type: Boolean, default: true },
  subscriptionRequired: { 
    type: String, 
    enum: ['free', 'basic', 'pro', 'enterprise'],
    default: 'free'
  }
}, {
  timestamps: true
});

// Create indexes
AICreationSchema.index({ userId: 1, type: 1, createdAt: -1 });
AICreationSchema.index({ isPublic: 1, isForSale: 1 });
AIGenerationHistorySchema.index({ userId: 1, createdAt: -1 });
AICreditsSchema.index({ userId: 1 });
CollaborationProjectSchema.index({ owner: 1, status: 1 });
AIMarketplaceItemSchema.index({ type: 1, category: 1, isActive: 1 });
AIToolSchema.index({ type: 1, isActive: 1 });

// Export models
export const AICreation = mongoose.model<IAICreation>('AICreation', AICreationSchema);
export const AIGenerationHistory = mongoose.model<IAIGenerationHistory>('AIGenerationHistory', AIGenerationHistorySchema);
export const AICredits = mongoose.model<IAICredits>('AICredits', AICreditsSchema);
export const CollaborationProject = mongoose.model<ICollaborationProject>('CollaborationProject', CollaborationProjectSchema);
export const AIMarketplaceItem = mongoose.model<IAIMarketplaceItem>('AIMarketplaceItem', AIMarketplaceItemSchema);
export const AITool = mongoose.model<IAITool>('AITool', AIToolSchema);
