import mongoose, { Document, Schema } from 'mongoose';

// Quantum Computer Interface
export interface IQuantumComputer extends Document {
  computerId: string;
  name: string;
  owner: mongoose.Types.ObjectId;
  
  // Quantum Specifications
  specifications: {
    qubits: number;
    coherenceTime: number; // microseconds
    gateTime: number; // nanoseconds
    errorRate: number;
    connectivity: string; // topology
    temperature: number; // millikelvin
    fidelity: number;
  };
  
  // Quantum Algorithms
  algorithms: Array<{
    name: string;
    type: 'optimization' | 'simulation' | 'cryptography' | 'ml' | 'search';
    complexity: string;
    implementation: any;
    performance: number;
  }>;
  
  // Computing Power
  power: {
    quantumVolume: number;
    classicalEquivalent: number; // FLOPS
    speedup: number;
    efficiency: number;
    utilization: number;
  };
  
  // Applications
  applications: Array<{
    id: string;
    name: string;
    type: string;
    status: 'running' | 'queued' | 'completed' | 'failed';
    priority: number;
    resources: any;
    results: any;
  }>;
  
  // Network & Entanglement
  network: {
    connectedComputers: mongoose.Types.ObjectId[];
    entanglements: Array<{
      targetId: mongoose.Types.ObjectId;
      strength: number;
      type: 'computational' | 'communication' | 'storage';
    }>;
    quantumInternet: boolean;
  };
  
  // Economics
  economics: {
    operatingCost: number;
    revenue: number;
    utilizationRate: number;
    pricingModel: 'per-qubit' | 'per-gate' | 'per-algorithm' | 'subscription';
    marketValue: number;
  };
  
  status: 'operational' | 'maintenance' | 'upgrading' | 'offline';
  
  createdAt: Date;
  updatedAt: Date;
}

// Time Machine Interface
export interface ITimeMachine extends Document {
  machineId: string;
  name: string;
  owner: mongoose.Types.ObjectId;
  
  // Temporal Specifications
  capabilities: {
    maxTimeJump: number; // years
    precision: number; // seconds
    paradoxPrevention: boolean;
    timelineStability: number;
    energyEfficiency: number;
  };
  
  // Time Travel Records
  journeys: Array<{
    id: string;
    destination: {
      year: number;
      month: number;
      day: number;
      hour: number;
      minute: number;
      second: number;
    };
    duration: number; // minutes in past/future
    purpose: string;
    outcome: string;
    paradoxRisk: number;
    energyCost: number;
    timestamp: Date;
  }>;
  
  // Timeline Management
  timelines: Array<{
    id: string;
    name: string;
    divergencePoint: Date;
    stability: number;
    population: number;
    events: any[];
    probability: number;
  }>;
  
  // Temporal Economics
  economics: {
    informationValue: number;
    paradoxInsurance: number;
    timelineRoyalties: number;
    temporalTradingProfit: number;
  };
  
  // Safety Systems
  safety: {
    paradoxDetection: boolean;
    timelineMonitoring: boolean;
    emergencyReturn: boolean;
    causalityProtection: boolean;
    quantumShielding: boolean;
  };
  
  status: 'operational' | 'charging' | 'maintenance' | 'temporal-lock';
  
  createdAt: Date;
  updatedAt: Date;
}

// Consciousness Transfer Interface
export interface IConsciousnessTransfer extends Document {
  transferId: string;
  sourceUser: mongoose.Types.ObjectId;
  targetUser?: mongoose.Types.ObjectId;
  
  // Transfer Details
  transferType: 'backup' | 'clone' | 'merge' | 'split' | 'enhance' | 'restore';
  
  // Consciousness Data
  consciousnessData: {
    memories: any[];
    personality: any;
    skills: any[];
    emotions: any;
    experiences: any[];
    knowledge: any[];
    relationships: any[];
  };
  
  // Transfer Process
  process: {
    method: 'quantum' | 'neural' | 'digital' | 'biological' | 'hybrid';
    fidelity: number; // 0-100%
    completeness: number; // 0-100%
    integrity: number; // 0-100%
    duration: number; // hours
    energyCost: number;
  };
  
  // Target Medium
  target: {
    type: 'biological' | 'digital' | 'quantum' | 'hybrid' | 'synthetic';
    specifications: any;
    compatibility: number;
    capacity: number;
    limitations: any[];
  };
  
  // Verification & Quality
  verification: {
    memoryIntegrity: number;
    personalityMatch: number;
    skillRetention: number;
    emotionalStability: number;
    cognitiveFunction: number;
  };
  
  // Legal & Ethical
  legal: {
    consent: boolean;
    ownership: string;
    rights: any[];
    restrictions: any[];
    liability: any;
  };
  
  // Economics
  economics: {
    cost: number;
    insurance: number;
    value: number;
    royalties: number;
  };
  
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'verified';
  
  createdAt: Date;
  updatedAt: Date;
}

// Dimensional Portal Interface
export interface IDimensionalPortal extends Document {
  portalId: string;
  name: string;
  owner: mongoose.Types.ObjectId;
  
  // Portal Specifications
  specifications: {
    maxRange: number; // light-years
    dimensions: number; // 3D, 4D, 5D, etc.
    stability: number;
    energyRequirement: number;
    throughput: number; // entities per hour
    accuracy: number;
  };
  
  // Connected Dimensions
  connections: Array<{
    dimensionId: string;
    name: string;
    type: 'parallel' | 'alternate' | 'pocket' | 'higher' | 'lower';
    distance: number;
    stability: number;
    accessibility: number;
    inhabitants: any[];
    resources: any[];
    dangers: any[];
  }>;
  
  // Travel Records
  travels: Array<{
    id: string;
    traveler: mongoose.Types.ObjectId;
    destination: string;
    purpose: string;
    duration: number;
    outcome: string;
    discoveries: any[];
    timestamp: Date;
  }>;
  
  // Dimensional Trade
  trade: {
    imports: any[];
    exports: any[];
    tradingPartners: any[];
    revenue: number;
    regulations: any[];
  };
  
  // Security & Safety
  security: {
    accessControl: boolean;
    quarantine: boolean;
    monitoring: boolean;
    emergencyShutdown: boolean;
    dimensionalShielding: boolean;
  };
  
  // Research Data
  research: {
    discoveries: any[];
    experiments: any[];
    publications: any[];
    collaborations: any[];
    breakthroughs: any[];
  };
  
  status: 'active' | 'inactive' | 'maintenance' | 'emergency-sealed';
  
  createdAt: Date;
  updatedAt: Date;
}

// Nano Technology Interface
export interface INanoTechnology extends Document {
  nanoId: string;
  name: string;
  owner: mongoose.Types.ObjectId;
  
  // Nano Specifications
  specifications: {
    size: number; // nanometers
    type: 'medical' | 'manufacturing' | 'environmental' | 'military' | 'research';
    material: string;
    functionality: string[];
    lifespan: number; // hours
    replication: boolean;
  };
  
  // Capabilities
  capabilities: {
    selfAssembly: boolean;
    selfRepair: boolean;
    selfReplication: boolean;
    programmability: boolean;
    networking: boolean;
    intelligence: number;
  };
  
  // Applications
  applications: Array<{
    id: string;
    name: string;
    target: string;
    objective: string;
    progress: number;
    results: any[];
    sideEffects: any[];
  }>;
  
  // Swarm Intelligence
  swarm: {
    size: number; // number of nanobots
    coordination: number;
    efficiency: number;
    emergentBehavior: any[];
    collectiveIntelligence: number;
  };
  
  // Safety & Control
  safety: {
    killSwitch: boolean;
    containment: boolean;
    monitoring: boolean;
    biocompatibility: number;
    environmentalImpact: number;
  };
  
  // Economics
  economics: {
    productionCost: number;
    marketValue: number;
    applications: any[];
    revenue: number;
    patents: any[];
  };
  
  status: 'development' | 'testing' | 'deployed' | 'recalled' | 'evolved';
  
  createdAt: Date;
  updatedAt: Date;
}

// Schemas
const QuantumComputerSchema = new Schema<IQuantumComputer>({
  computerId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  
  specifications: {
    qubits: { type: Number, required: true },
    coherenceTime: { type: Number, required: true },
    gateTime: { type: Number, required: true },
    errorRate: { type: Number, required: true },
    connectivity: { type: String, required: true },
    temperature: { type: Number, required: true },
    fidelity: { type: Number, min: 0, max: 1, required: true }
  },
  
  algorithms: [{
    name: { type: String, required: true },
    type: { 
      type: String, 
      enum: ['optimization', 'simulation', 'cryptography', 'ml', 'search'],
      required: true 
    },
    complexity: String,
    implementation: Schema.Types.Mixed,
    performance: { type: Number, default: 0 }
  }],
  
  power: {
    quantumVolume: { type: Number, required: true },
    classicalEquivalent: { type: Number, required: true },
    speedup: { type: Number, default: 1 },
    efficiency: { type: Number, min: 0, max: 100, default: 50 },
    utilization: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  applications: [{
    id: String,
    name: String,
    type: String,
    status: { 
      type: String, 
      enum: ['running', 'queued', 'completed', 'failed'],
      default: 'queued'
    },
    priority: { type: Number, min: 1, max: 10, default: 5 },
    resources: Schema.Types.Mixed,
    results: Schema.Types.Mixed
  }],
  
  network: {
    connectedComputers: [{ type: Schema.Types.ObjectId, ref: 'QuantumComputer' }],
    entanglements: [{
      targetId: { type: Schema.Types.ObjectId, ref: 'QuantumComputer' },
      strength: { type: Number, min: 0, max: 1 },
      type: { 
        type: String, 
        enum: ['computational', 'communication', 'storage']
      }
    }],
    quantumInternet: { type: Boolean, default: false }
  },
  
  economics: {
    operatingCost: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    utilizationRate: { type: Number, min: 0, max: 100, default: 0 },
    pricingModel: { 
      type: String, 
      enum: ['per-qubit', 'per-gate', 'per-algorithm', 'subscription'],
      default: 'per-algorithm'
    },
    marketValue: { type: Number, default: 0 }
  },
  
  status: { 
    type: String, 
    enum: ['operational', 'maintenance', 'upgrading', 'offline'],
    default: 'operational'
  }
}, {
  timestamps: true
});

const TimeMachineSchema = new Schema<ITimeMachine>({
  machineId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  
  capabilities: {
    maxTimeJump: { type: Number, required: true },
    precision: { type: Number, required: true },
    paradoxPrevention: { type: Boolean, default: true },
    timelineStability: { type: Number, min: 0, max: 100, default: 95 },
    energyEfficiency: { type: Number, min: 0, max: 100, default: 50 }
  },
  
  journeys: [{
    id: String,
    destination: {
      year: Number,
      month: Number,
      day: Number,
      hour: Number,
      minute: Number,
      second: Number
    },
    duration: Number,
    purpose: String,
    outcome: String,
    paradoxRisk: { type: Number, min: 0, max: 100 },
    energyCost: Number,
    timestamp: { type: Date, default: Date.now }
  }],
  
  timelines: [{
    id: String,
    name: String,
    divergencePoint: Date,
    stability: { type: Number, min: 0, max: 100 },
    population: Number,
    events: [Schema.Types.Mixed],
    probability: { type: Number, min: 0, max: 1 }
  }],
  
  economics: {
    informationValue: { type: Number, default: 0 },
    paradoxInsurance: { type: Number, default: 0 },
    timelineRoyalties: { type: Number, default: 0 },
    temporalTradingProfit: { type: Number, default: 0 }
  },
  
  safety: {
    paradoxDetection: { type: Boolean, default: true },
    timelineMonitoring: { type: Boolean, default: true },
    emergencyReturn: { type: Boolean, default: true },
    causalityProtection: { type: Boolean, default: true },
    quantumShielding: { type: Boolean, default: true }
  },
  
  status: { 
    type: String, 
    enum: ['operational', 'charging', 'maintenance', 'temporal-lock'],
    default: 'operational'
  }
}, {
  timestamps: true
});

const ConsciousnessTransferSchema = new Schema<IConsciousnessTransfer>({
  transferId: { type: String, required: true, unique: true },
  sourceUser: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  targetUser: { type: Schema.Types.ObjectId, ref: 'User' },
  
  transferType: { 
    type: String, 
    enum: ['backup', 'clone', 'merge', 'split', 'enhance', 'restore'],
    required: true 
  },
  
  consciousnessData: {
    memories: [Schema.Types.Mixed],
    personality: Schema.Types.Mixed,
    skills: [Schema.Types.Mixed],
    emotions: Schema.Types.Mixed,
    experiences: [Schema.Types.Mixed],
    knowledge: [Schema.Types.Mixed],
    relationships: [Schema.Types.Mixed]
  },
  
  process: {
    method: { 
      type: String, 
      enum: ['quantum', 'neural', 'digital', 'biological', 'hybrid'],
      required: true 
    },
    fidelity: { type: Number, min: 0, max: 100, required: true },
    completeness: { type: Number, min: 0, max: 100, required: true },
    integrity: { type: Number, min: 0, max: 100, required: true },
    duration: { type: Number, required: true },
    energyCost: { type: Number, required: true }
  },
  
  target: {
    type: { 
      type: String, 
      enum: ['biological', 'digital', 'quantum', 'hybrid', 'synthetic'],
      required: true 
    },
    specifications: Schema.Types.Mixed,
    compatibility: { type: Number, min: 0, max: 100 },
    capacity: Number,
    limitations: [Schema.Types.Mixed]
  },
  
  verification: {
    memoryIntegrity: { type: Number, min: 0, max: 100 },
    personalityMatch: { type: Number, min: 0, max: 100 },
    skillRetention: { type: Number, min: 0, max: 100 },
    emotionalStability: { type: Number, min: 0, max: 100 },
    cognitiveFunction: { type: Number, min: 0, max: 100 }
  },
  
  legal: {
    consent: { type: Boolean, required: true },
    ownership: String,
    rights: [Schema.Types.Mixed],
    restrictions: [Schema.Types.Mixed],
    liability: Schema.Types.Mixed
  },
  
  economics: {
    cost: { type: Number, required: true },
    insurance: { type: Number, default: 0 },
    value: { type: Number, default: 0 },
    royalties: { type: Number, default: 0 }
  },
  
  status: { 
    type: String, 
    enum: ['pending', 'processing', 'completed', 'failed', 'verified'],
    default: 'pending'
  }
}, {
  timestamps: true
});

const DimensionalPortalSchema = new Schema<IDimensionalPortal>({
  portalId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  
  specifications: {
    maxRange: { type: Number, required: true },
    dimensions: { type: Number, min: 3, max: 11, default: 3 },
    stability: { type: Number, min: 0, max: 100, required: true },
    energyRequirement: { type: Number, required: true },
    throughput: { type: Number, required: true },
    accuracy: { type: Number, min: 0, max: 100, required: true }
  },
  
  connections: [{
    dimensionId: String,
    name: String,
    type: { 
      type: String, 
      enum: ['parallel', 'alternate', 'pocket', 'higher', 'lower']
    },
    distance: Number,
    stability: { type: Number, min: 0, max: 100 },
    accessibility: { type: Number, min: 0, max: 100 },
    inhabitants: [Schema.Types.Mixed],
    resources: [Schema.Types.Mixed],
    dangers: [Schema.Types.Mixed]
  }],
  
  travels: [{
    id: String,
    traveler: { type: Schema.Types.ObjectId, ref: 'User' },
    destination: String,
    purpose: String,
    duration: Number,
    outcome: String,
    discoveries: [Schema.Types.Mixed],
    timestamp: { type: Date, default: Date.now }
  }],
  
  trade: {
    imports: [Schema.Types.Mixed],
    exports: [Schema.Types.Mixed],
    tradingPartners: [Schema.Types.Mixed],
    revenue: { type: Number, default: 0 },
    regulations: [Schema.Types.Mixed]
  },
  
  security: {
    accessControl: { type: Boolean, default: true },
    quarantine: { type: Boolean, default: true },
    monitoring: { type: Boolean, default: true },
    emergencyShutdown: { type: Boolean, default: true },
    dimensionalShielding: { type: Boolean, default: true }
  },
  
  research: {
    discoveries: [Schema.Types.Mixed],
    experiments: [Schema.Types.Mixed],
    publications: [Schema.Types.Mixed],
    collaborations: [Schema.Types.Mixed],
    breakthroughs: [Schema.Types.Mixed]
  },
  
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'maintenance', 'emergency-sealed'],
    default: 'inactive'
  }
}, {
  timestamps: true
});

const NanoTechnologySchema = new Schema<INanoTechnology>({
  nanoId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  
  specifications: {
    size: { type: Number, required: true },
    type: { 
      type: String, 
      enum: ['medical', 'manufacturing', 'environmental', 'military', 'research'],
      required: true 
    },
    material: { type: String, required: true },
    functionality: [String],
    lifespan: { type: Number, required: true },
    replication: { type: Boolean, default: false }
  },
  
  capabilities: {
    selfAssembly: { type: Boolean, default: false },
    selfRepair: { type: Boolean, default: false },
    selfReplication: { type: Boolean, default: false },
    programmability: { type: Boolean, default: false },
    networking: { type: Boolean, default: false },
    intelligence: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  applications: [{
    id: String,
    name: String,
    target: String,
    objective: String,
    progress: { type: Number, min: 0, max: 100, default: 0 },
    results: [Schema.Types.Mixed],
    sideEffects: [Schema.Types.Mixed]
  }],
  
  swarm: {
    size: { type: Number, default: 1 },
    coordination: { type: Number, min: 0, max: 100, default: 0 },
    efficiency: { type: Number, min: 0, max: 100, default: 50 },
    emergentBehavior: [Schema.Types.Mixed],
    collectiveIntelligence: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  safety: {
    killSwitch: { type: Boolean, default: true },
    containment: { type: Boolean, default: true },
    monitoring: { type: Boolean, default: true },
    biocompatibility: { type: Number, min: 0, max: 100, default: 50 },
    environmentalImpact: { type: Number, min: 0, max: 100, default: 50 }
  },
  
  economics: {
    productionCost: { type: Number, required: true },
    marketValue: { type: Number, default: 0 },
    applications: [Schema.Types.Mixed],
    revenue: { type: Number, default: 0 },
    patents: [Schema.Types.Mixed]
  },
  
  status: { 
    type: String, 
    enum: ['development', 'testing', 'deployed', 'recalled', 'evolved'],
    default: 'development'
  }
}, {
  timestamps: true
});

// Create indexes
QuantumComputerSchema.index({ computerId: 1 });
QuantumComputerSchema.index({ owner: 1, status: 1 });
TimeMachineSchema.index({ machineId: 1 });
TimeMachineSchema.index({ owner: 1, status: 1 });
ConsciousnessTransferSchema.index({ transferId: 1 });
ConsciousnessTransferSchema.index({ sourceUser: 1, status: 1 });
DimensionalPortalSchema.index({ portalId: 1 });
DimensionalPortalSchema.index({ owner: 1, status: 1 });
NanoTechnologySchema.index({ nanoId: 1 });
NanoTechnologySchema.index({ owner: 1, 'specifications.type': 1 });

// Export models
export const QuantumComputer = mongoose.model<IQuantumComputer>('QuantumComputer', QuantumComputerSchema);
export const TimeMachine = mongoose.model<ITimeMachine>('TimeMachine', TimeMachineSchema);
export const ConsciousnessTransfer = mongoose.model<IConsciousnessTransfer>('ConsciousnessTransfer', ConsciousnessTransferSchema);
export const DimensionalPortal = mongoose.model<IDimensionalPortal>('DimensionalPortal', DimensionalPortalSchema);
export const NanoTechnology = mongoose.model<INanoTechnology>('NanoTechnology', NanoTechnologySchema);
