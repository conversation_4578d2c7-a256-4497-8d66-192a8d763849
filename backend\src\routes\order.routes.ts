import { Router } from 'express';
import {
  createOrder,
  getUserOrders,
  getOrderById,
  updateOrderStatus,
  cancelOrder
} from '../controllers/order.controller';
import { authenticateToken, requireAdmin } from '../middleware/auth.middleware';
import { validate, orderValidation } from '../middleware/validation.middleware';

const router = Router();

// All order routes require authentication
router.use(authenticateToken);

// User order routes
router.post('/', validate(orderValidation.create), createOrder);
router.get('/', getUserOrders);
router.get('/:id', getOrderById);
router.put('/:id/cancel', cancelOrder);

// Admin routes
router.put('/:id/status', requireAdmin, updateOrderStatus);

export default router;
