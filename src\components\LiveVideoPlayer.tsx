import React from 'react';

interface LiveVideoPlayerProps {
  streamUrl: string;
}

const LiveVideoPlayer: React.FC<LiveVideoPlayerProps> = ({ streamUrl }) => {
  return (
    <div className="rounded-xl overflow-hidden shadow-lg bg-black relative">
      <video
        src={streamUrl}
        controls
        autoPlay
        className="w-full aspect-video bg-black"
        poster="https://dummyimage.com/1280x720/222/fff&text=Live+Stream"
      />
      <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow">LIVE</div>
    </div>
  );
};

export default LiveVideoPlayer; 