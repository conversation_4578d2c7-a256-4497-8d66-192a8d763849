import { Router } from 'express';
import {
  register,
  login,
  logout,
  getProfile,
  updateProfile,
  changePassword
} from '../controllers/auth.controller';
import { authenticateToken, checkAccountLock } from '../middleware/auth.middleware';
import { validate, userValidation } from '../middleware/validation.middleware';

const router = Router();

// Public routes
router.post('/register', validate(userValidation.register), register);
router.post('/login', checkAccountLock, validate(userValidation.login), login);
router.post('/logout', logout);

// Protected routes
router.get('/profile', authenticateToken, getProfile);
router.put('/profile', authenticateToken, validate(userValidation.updateProfile), updateProfile);
router.put('/change-password', authenticateToken, validate(userValidation.changePassword), changePassword);

export default router;
