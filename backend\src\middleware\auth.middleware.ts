import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User, IUser } from '../models/user.model';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
    }
  }
}

interface JWTPayload {
  id: string;
  email: string;
  iat: number;
  exp: number;
}

// Verify JWT token
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      return res.status(500).json({
        success: false,
        message: 'JWT secret not configured'
      });
    }

    const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    
    // Get user from database
    const user = await User.findById(decoded.id).select('+password');
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token or user not found'
      });
    }

    // Update last active timestamp
    user.activity.lastActive = new Date();
    await user.save();

    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// Optional authentication (doesn't fail if no token)
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const jwtSecret = process.env.JWT_SECRET;
      if (jwtSecret) {
        const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
        const user = await User.findById(decoded.id);
        if (user && user.isActive) {
          req.user = user;
        }
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

// Check if user has specific role
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userRoles = req.user.roles;
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    const hasRole = requiredRoles.some(role => userRoles.includes(role));
    
    if (!hasRole) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Check if user is admin
export const requireAdmin = requireRole('admin');

// Check if user is creator
export const requireCreator = requireRole(['creator', 'admin']);

// Check if user is moderator or admin
export const requireModerator = requireRole(['moderator', 'admin']);

// Rate limiting for login attempts
export const checkAccountLock = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return next();
    }

    const user = await User.findOne({ email }).select('+security');
    
    if (user && user.security.lockUntil && user.security.lockUntil > new Date()) {
      const lockTimeRemaining = Math.ceil((user.security.lockUntil.getTime() - Date.now()) / 1000 / 60);
      return res.status(423).json({
        success: false,
        message: `Account locked. Try again in ${lockTimeRemaining} minutes.`
      });
    }

    next();
  } catch (error) {
    next();
  }
};

// Generate JWT token
export const generateToken = (user: IUser): string => {
  const jwtSecret = process.env.JWT_SECRET;
  const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
  
  if (!jwtSecret) {
    throw new Error('JWT secret not configured');
  }

  return jwt.sign(
    {
      id: user._id,
      email: user.email
    },
    jwtSecret,
    { expiresIn: jwtExpiresIn }
  );
};

// Generate refresh token
export const generateRefreshToken = (user: IUser): string => {
  const refreshSecret = process.env.JWT_REFRESH_SECRET;
  const refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d';
  
  if (!refreshSecret) {
    throw new Error('JWT refresh secret not configured');
  }

  return jwt.sign(
    {
      id: user._id,
      email: user.email,
      type: 'refresh'
    },
    refreshSecret,
    { expiresIn: refreshExpiresIn }
  );
};

// Check if user has premium subscription
export const requirePremium = (req: AuthRequest, res: Response, next: NextFunction) => {
  if (!req.user || (!req.user.isPremium && req.user.role !== 'admin')) {
    return res.status(403).json({
      success: false,
      message: 'Premium subscription required'
    });
  }
  next();
};

// Check if user is verified
export const requireVerified = (req: AuthRequest, res: Response, next: NextFunction) => {
  if (!req.user || !req.user.isVerified) {
    return res.status(403).json({
      success: false,
      message: 'Email verification required'
    });
  }
  next();
};

// Check if user owns the resource
export const requireOwnership = (resourceField: string = 'userId') => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const resourceId = req.params.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // This is a simplified check - in practice, you'd query the specific resource
      // and check if the user owns it
      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Authorization check failed'
      });
    }
  };
};
