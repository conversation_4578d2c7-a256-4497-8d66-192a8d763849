import { Router } from 'express';
import {
  upload,
  uploadSingleFile,
  uploadMultipleFiles,
  deleteFile,
  getFileInfo,
  serveFile,
  processImage
} from '../controllers/upload.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// File upload routes (require authentication)
router.post('/single', authenticateToken, upload.single('file'), uploadSingleFile);
router.post('/multiple', authenticateToken, upload.array('files', 5), uploadMultipleFiles);

// File management routes
router.get('/info/:filename', getFileInfo);
router.delete('/:filename', authenticateToken, deleteFile);

// File serving routes (public)
router.get('/:filename', serveFile);

// Image processing routes
router.get('/process/:filename', processImage);

export default router;
