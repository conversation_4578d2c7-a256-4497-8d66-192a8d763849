import mongoose, { Document, Schema } from 'mongoose';

export interface INFT extends Document {
  _id: string;
  tokenId: string;
  contractAddress: string;
  blockchain: 'ethereum' | 'polygon' | 'binance' | 'solana';
  name: string;
  description: string;
  image: string;
  animationUrl?: string;
  externalUrl?: string;
  creator: {
    id: string;
    name: string;
    avatar: string;
    walletAddress: string;
  };
  owner: {
    id: string;
    name: string;
    avatar: string;
    walletAddress: string;
  };
  collection: {
    id?: string;
    name: string;
    symbol: string;
    description: string;
    image: string;
  };
  attributes: Array<{
    traitType: string;
    value: string | number;
    displayType?: 'number' | 'boost_percentage' | 'boost_number' | 'date';
    rarity?: number;
  }>;
  metadata: {
    standard: 'ERC-721' | 'ERC-1155';
    ipfsHash: string;
    metadataUri: string;
  };
  pricing: {
    currentPrice?: number;
    currency: string;
    isForSale: boolean;
    auctionEndTime?: Date;
    reservePrice?: number;
    buyNowPrice?: number;
  };
  trading: {
    totalSales: number;
    highestSale: number;
    lastSalePrice?: number;
    lastSaleDate?: Date;
    views: number;
    likes: number;
    shares: number;
  };
  royalty: {
    percentage: number;
    recipient: string;
  };
  verification: {
    isVerified: boolean;
    verifiedBy?: string;
    verificationDate?: Date;
  };
  tags: string[];
  category: 'art' | 'music' | 'video' | 'gaming' | 'photography' | 'utility' | 'collectible';
  rarity: {
    rank?: number;
    score?: number;
    tier: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  };
  status: 'minting' | 'minted' | 'listed' | 'sold' | 'burned';
  mintedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const nftSchema = new Schema<INFT>({
  tokenId: {
    type: String,
    required: true,
    unique: true
  },
  contractAddress: {
    type: String,
    required: true
  },
  blockchain: {
    type: String,
    required: true,
    enum: ['ethereum', 'polygon', 'binance', 'solana']
  },
  name: {
    type: String,
    required: [true, 'NFT name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'NFT description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  image: {
    type: String,
    required: [true, 'NFT image is required']
  },
  animationUrl: String,
  externalUrl: String,
  creator: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: { type: String, required: true },
    avatar: String,
    walletAddress: { type: String, required: true }
  },
  owner: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: { type: String, required: true },
    avatar: String,
    walletAddress: { type: String, required: true }
  },
  collection: {
    id: {
      type: Schema.Types.ObjectId,
      ref: 'Collection'
    },
    name: { type: String, required: true },
    symbol: { type: String, required: true },
    description: String,
    image: String
  },
  attributes: [{
    traitType: { type: String, required: true },
    value: { type: Schema.Types.Mixed, required: true },
    displayType: {
      type: String,
      enum: ['number', 'boost_percentage', 'boost_number', 'date']
    },
    rarity: Number
  }],
  metadata: {
    standard: {
      type: String,
      required: true,
      enum: ['ERC-721', 'ERC-1155']
    },
    ipfsHash: { type: String, required: true },
    metadataUri: { type: String, required: true }
  },
  pricing: {
    currentPrice: Number,
    currency: { type: String, default: 'ETH' },
    isForSale: { type: Boolean, default: false },
    auctionEndTime: Date,
    reservePrice: Number,
    buyNowPrice: Number
  },
  trading: {
    totalSales: { type: Number, default: 0 },
    highestSale: { type: Number, default: 0 },
    lastSalePrice: Number,
    lastSaleDate: Date,
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    shares: { type: Number, default: 0 }
  },
  royalty: {
    percentage: { type: Number, required: true, min: 0, max: 50 },
    recipient: { type: String, required: true }
  },
  verification: {
    isVerified: { type: Boolean, default: false },
    verifiedBy: String,
    verificationDate: Date
  },
  tags: [String],
  category: {
    type: String,
    required: true,
    enum: ['art', 'music', 'video', 'gaming', 'photography', 'utility', 'collectible']
  },
  rarity: {
    rank: Number,
    score: Number,
    tier: {
      type: String,
      enum: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
      default: 'common'
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['minting', 'minted', 'listed', 'sold', 'burned'],
    default: 'minting'
  },
  mintedAt: { type: Date, default: Date.now }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
nftSchema.index({ tokenId: 1, contractAddress: 1 });
nftSchema.index({ 'creator.id': 1 });
nftSchema.index({ 'owner.id': 1 });
nftSchema.index({ category: 1, status: 1 });
nftSchema.index({ 'pricing.isForSale': 1, 'pricing.currentPrice': 1 });
nftSchema.index({ 'trading.views': -1 });
nftSchema.index({ mintedAt: -1 });

// Virtual for current USD price (would need external price feed)
nftSchema.virtual('priceUSD').get(function() {
  // This would typically fetch current exchange rates
  return this.pricing.currentPrice ? this.pricing.currentPrice * 2000 : 0; // Placeholder
});

export const NFT = mongoose.model<INFT>('NFT', nftSchema);
