import mongoose, { Document, Schema } from 'mongoose';

// Virtual World Interface
export interface IVirtualWorld extends Document {
  worldId: string;
  name: string;
  description: string;
  creator: mongoose.Types.ObjectId;

  // World Properties
  type: 'social' | 'gaming' | 'business' | 'educational' | 'artistic' | 'infinite';
  genre: string;
  theme: string;

  // Spatial Configuration
  dimensions: {
    x: number;
    y: number;
    z: number;
    time?: number; // 4D worlds
    quantum?: boolean; // Quantum superposition spaces
  };

  // Physics Engine
  physics: {
    gravity: number;
    friction: number;
    elasticity: number;
    fluidDynamics: boolean;
    quantumMechanics: boolean;
    timeFlow: number; // Time dilation factor
    customLaws: any[];
  };

  // Environment
  environment: {
    lighting: any;
    weather: any;
    atmosphere: any;
    soundscape: any;
    climate: any;
    seasons: boolean;
    dayNightCycle: boolean;
  };

  // Economy
  economy: {
    currency: string;
    gdp: number;
    inflation: number;
    marketCap: number;
    tradingVolume: number;
    economicModel: 'capitalist' | 'socialist' | 'mixed' | 'post-scarcity' | 'quantum';
  };

  // Governance
  governance: {
    type: 'democracy' | 'republic' | 'monarchy' | 'anarchy' | 'ai-governed' | 'dao';
    laws: any[];
    constitution: string;
    votingSystem: any;
    enforcement: any;
  };

  // Population & Demographics
  population: {
    totalUsers: number;
    activeUsers: number;
    npcCount: number;
    aiEntities: number;
    demographics: any;
    culturalGroups: any[];
  };

  // Infrastructure
  infrastructure: {
    buildings: any[];
    transportation: any[];
    communication: any[];
    utilities: any[];
    services: any[];
  };

  // Content & Assets
  assets: {
    models: any[];
    textures: any[];
    animations: any[];
    sounds: any[];
    scripts: any[];
    nfts: any[];
  };

  // AI Integration
  aiSystems: {
    worldAI: mongoose.Types.ObjectId; // World's governing AI
    npcAIs: mongoose.Types.ObjectId[];
    environmentAI: mongoose.Types.ObjectId;
    economyAI: mongoose.Types.ObjectId;
  };

  // Metrics
  metrics: {
    userEngagement: number;
    economicActivity: number;
    contentCreation: number;
    socialInteraction: number;
    innovation: number;
    sustainability: number;
  };

  // Access & Permissions
  access: {
    public: boolean;
    inviteOnly: boolean;
    premium: boolean;
    ageRestriction: number;
    contentRating: string;
  };

  // Technical Specs
  technical: {
    maxConcurrentUsers: number;
    serverRegions: string[];
    bandwidth: number;
    latency: number;
    renderQuality: string;
    vr: boolean;
    ar: boolean;
    brain: boolean; // Direct neural interface
  };

  status: 'development' | 'beta' | 'live' | 'maintenance' | 'archived';
  version: string;

  createdAt: Date;
  updatedAt: Date;
}

// Virtual Avatar Interface
export interface IVirtualAvatar extends Document {
  avatarId: string;
  userId: mongoose.Types.ObjectId;
  worldId: mongoose.Types.ObjectId;

  // Identity
  name: string;
  displayName: string;
  bio: string;

  // Appearance
  appearance: {
    species: 'human' | 'alien' | 'robot' | 'fantasy' | 'abstract' | 'energy';
    gender: string;
    age: number;
    height: number;
    build: string;

    // Physical Features
    face: any;
    hair: any;
    eyes: any;
    skin: any;

    // Clothing & Accessories
    clothing: any[];
    accessories: any[];
    jewelry: any[];

    // Special Features
    wings: boolean;
    tail: boolean;
    horns: boolean;
    aura: any;
    particles: any[];
  };

  // Abilities & Skills
  abilities: {
    physical: {
      strength: number;
      agility: number;
      endurance: number;
      speed: number;
      flexibility: number;
    };
    mental: {
      intelligence: number;
      wisdom: number;
      creativity: number;
      memory: number;
      focus: number;
    };
    social: {
      charisma: number;
      empathy: number;
      leadership: number;
      communication: number;
      influence: number;
    };
    special: {
      magic: number;
      technology: number;
      psychic: number;
      quantum: number;
      divine: number;
    };
  };

  // Inventory & Assets
  inventory: {
    items: any[];
    currency: Map<string, number>;
    nfts: any[];
    properties: any[];
    vehicles: any[];
    pets: any[];
  };

  // Relationships
  relationships: {
    friends: mongoose.Types.ObjectId[];
    family: mongoose.Types.ObjectId[];
    romantic: mongoose.Types.ObjectId[];
    business: mongoose.Types.ObjectId[];
    rivals: mongoose.Types.ObjectId[];
    mentors: mongoose.Types.ObjectId[];
    students: mongoose.Types.ObjectId[];
  };

  // Achievements & Progress
  achievements: {
    titles: string[];
    badges: any[];
    levels: Map<string, number>;
    experience: Map<string, number>;
    reputation: Map<string, number>;
    rankings: any[];
  };

  // AI Integration
  aiPersonality: {
    brainId: mongoose.Types.ObjectId;
    autonomyLevel: number; // How much AI controls the avatar
    learningEnabled: boolean;
    emotionalState: any;
    goals: any[];
    memories: any[];
  };

  // Behavior & Preferences
  behavior: {
    personality: any;
    preferences: Map<string, any>;
    habits: any[];
    routines: any[];
    communication: any;
  };

  // Location & Movement
  location: {
    worldId: mongoose.Types.ObjectId;
    position: {
      x: number;
      y: number;
      z: number;
      rotation: any;
    };
    region: string;
    building: string;
    room: string;
  };

  // Status
  status: 'online' | 'offline' | 'away' | 'busy' | 'invisible';
  lastActive: Date;

  createdAt: Date;
  updatedAt: Date;
}

// Virtual Property Interface
export interface IVirtualProperty extends Document {
  propertyId: string;
  worldId: mongoose.Types.ObjectId;
  owner: mongoose.Types.ObjectId;

  // Property Details
  type: 'land' | 'building' | 'structure' | 'space' | 'dimension';
  name: string;
  description: string;

  // Location & Dimensions
  coordinates: {
    x: number;
    y: number;
    z: number;
    width: number;
    height: number;
    depth: number;
  };

  // Features & Amenities
  features: {
    rooms: any[];
    utilities: any[];
    security: any[];
    entertainment: any[];
    business: any[];
    special: any[];
  };

  // Economic Data
  economics: {
    purchasePrice: number;
    currentValue: number;
    marketValue: number;
    rentalIncome: number;
    expenses: number;
    roi: number;
    appreciation: number;
  };

  // Usage & Permissions
  usage: {
    residential: boolean;
    commercial: boolean;
    industrial: boolean;
    entertainment: boolean;
    educational: boolean;
    mixed: boolean;
  };

  permissions: {
    public: boolean;
    friends: boolean;
    invited: boolean;
    private: boolean;
    commercial: boolean;
  };

  // Tenants & Visitors
  occupancy: {
    tenants: mongoose.Types.ObjectId[];
    visitors: mongoose.Types.ObjectId[];
    maxOccupancy: number;
    currentOccupancy: number;
  };

  // Customization
  customization: {
    theme: string;
    style: string;
    decorations: any[];
    furniture: any[];
    art: any[];
    technology: any[];
  };

  // AI Integration
  smartSystems: {
    aiButler: mongoose.Types.ObjectId;
    automation: any[];
    security: any[];
    entertainment: any[];
    maintenance: any[];
  };

  // Metrics
  metrics: {
    visitors: number;
    events: number;
    revenue: number;
    satisfaction: number;
    popularity: number;
  };

  status: 'active' | 'inactive' | 'maintenance' | 'for-sale' | 'for-rent';

  createdAt: Date;
  updatedAt: Date;
}

// Schemas
const VirtualWorldSchema = new Schema<IVirtualWorld>({
  worldId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  creator: { type: Schema.Types.ObjectId, ref: 'User', required: true },

  type: {
    type: String,
    enum: ['social', 'gaming', 'business', 'educational', 'artistic', 'infinite'],
    required: true
  },
  genre: String,
  theme: String,

  dimensions: {
    x: { type: Number, required: true },
    y: { type: Number, required: true },
    z: { type: Number, required: true },
    time: Number,
    quantum: { type: Boolean, default: false }
  },

  physics: {
    gravity: { type: Number, default: 9.81 },
    friction: { type: Number, default: 0.5 },
    elasticity: { type: Number, default: 0.8 },
    fluidDynamics: { type: Boolean, default: false },
    quantumMechanics: { type: Boolean, default: false },
    timeFlow: { type: Number, default: 1 },
    customLaws: [Schema.Types.Mixed]
  },

  environment: {
    lighting: Schema.Types.Mixed,
    weather: Schema.Types.Mixed,
    atmosphere: Schema.Types.Mixed,
    soundscape: Schema.Types.Mixed,
    climate: Schema.Types.Mixed,
    seasons: { type: Boolean, default: false },
    dayNightCycle: { type: Boolean, default: true }
  },

  economy: {
    currency: { type: String, default: 'NEURO' },
    gdp: { type: Number, default: 0 },
    inflation: { type: Number, default: 0 },
    marketCap: { type: Number, default: 0 },
    tradingVolume: { type: Number, default: 0 },
    economicModel: {
      type: String,
      enum: ['capitalist', 'socialist', 'mixed', 'post-scarcity', 'quantum'],
      default: 'mixed'
    }
  },

  governance: {
    type: {
      type: String,
      enum: ['democracy', 'republic', 'monarchy', 'anarchy', 'ai-governed', 'dao'],
      default: 'dao'
    },
    laws: [Schema.Types.Mixed],
    constitution: String,
    votingSystem: Schema.Types.Mixed,
    enforcement: Schema.Types.Mixed
  },

  population: {
    totalUsers: { type: Number, default: 0 },
    activeUsers: { type: Number, default: 0 },
    npcCount: { type: Number, default: 0 },
    aiEntities: { type: Number, default: 0 },
    demographics: Schema.Types.Mixed,
    culturalGroups: [Schema.Types.Mixed]
  },

  infrastructure: {
    buildings: [Schema.Types.Mixed],
    transportation: [Schema.Types.Mixed],
    communication: [Schema.Types.Mixed],
    utilities: [Schema.Types.Mixed],
    services: [Schema.Types.Mixed]
  },

  assets: {
    models: [Schema.Types.Mixed],
    textures: [Schema.Types.Mixed],
    animations: [Schema.Types.Mixed],
    sounds: [Schema.Types.Mixed],
    scripts: [Schema.Types.Mixed],
    nfts: [Schema.Types.Mixed]
  },

  aiSystems: {
    worldAI: { type: Schema.Types.ObjectId, ref: 'NeuroBrain' },
    npcAIs: [{ type: Schema.Types.ObjectId, ref: 'NeuroBrain' }],
    environmentAI: { type: Schema.Types.ObjectId, ref: 'NeuroBrain' },
    economyAI: { type: Schema.Types.ObjectId, ref: 'NeuroBrain' }
  },

  metrics: {
    userEngagement: { type: Number, default: 0 },
    economicActivity: { type: Number, default: 0 },
    contentCreation: { type: Number, default: 0 },
    socialInteraction: { type: Number, default: 0 },
    innovation: { type: Number, default: 0 },
    sustainability: { type: Number, default: 0 }
  },

  access: {
    public: { type: Boolean, default: true },
    inviteOnly: { type: Boolean, default: false },
    premium: { type: Boolean, default: false },
    ageRestriction: { type: Number, default: 0 },
    contentRating: { type: String, default: 'E' }
  },

  technical: {
    maxConcurrentUsers: { type: Number, default: 1000 },
    serverRegions: [String],
    bandwidth: { type: Number, default: 100 },
    latency: { type: Number, default: 50 },
    renderQuality: { type: String, default: 'high' },
    vr: { type: Boolean, default: true },
    ar: { type: Boolean, default: true },
    brain: { type: Boolean, default: false }
  },

  status: {
    type: String,
    enum: ['development', 'beta', 'live', 'maintenance', 'archived'],
    default: 'development'
  },
  version: { type: String, default: '1.0.0' }
}, {
  timestamps: true
});

const VirtualAvatarSchema = new Schema<IVirtualAvatar>({
  avatarId: { type: String, required: true, unique: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  worldId: { type: Schema.Types.ObjectId, ref: 'VirtualWorld', required: true },

  name: { type: String, required: true },
  displayName: String,
  bio: String,

  appearance: {
    species: {
      type: String,
      enum: ['human', 'alien', 'robot', 'fantasy', 'abstract', 'energy'],
      default: 'human'
    },
    gender: String,
    age: { type: Number, default: 25 },
    height: { type: Number, default: 170 },
    build: String,

    face: Schema.Types.Mixed,
    hair: Schema.Types.Mixed,
    eyes: Schema.Types.Mixed,
    skin: Schema.Types.Mixed,

    clothing: [Schema.Types.Mixed],
    accessories: [Schema.Types.Mixed],
    jewelry: [Schema.Types.Mixed],

    wings: { type: Boolean, default: false },
    tail: { type: Boolean, default: false },
    horns: { type: Boolean, default: false },
    aura: Schema.Types.Mixed,
    particles: [Schema.Types.Mixed]
  },

  abilities: {
    physical: {
      strength: { type: Number, min: 0, max: 100, default: 50 },
      agility: { type: Number, min: 0, max: 100, default: 50 },
      endurance: { type: Number, min: 0, max: 100, default: 50 },
      speed: { type: Number, min: 0, max: 100, default: 50 },
      flexibility: { type: Number, min: 0, max: 100, default: 50 }
    },
    mental: {
      intelligence: { type: Number, min: 0, max: 100, default: 50 },
      wisdom: { type: Number, min: 0, max: 100, default: 50 },
      creativity: { type: Number, min: 0, max: 100, default: 50 },
      memory: { type: Number, min: 0, max: 100, default: 50 },
      focus: { type: Number, min: 0, max: 100, default: 50 }
    },
    social: {
      charisma: { type: Number, min: 0, max: 100, default: 50 },
      empathy: { type: Number, min: 0, max: 100, default: 50 },
      leadership: { type: Number, min: 0, max: 100, default: 50 },
      communication: { type: Number, min: 0, max: 100, default: 50 },
      influence: { type: Number, min: 0, max: 100, default: 50 }
    },
    special: {
      magic: { type: Number, min: 0, max: 100, default: 0 },
      technology: { type: Number, min: 0, max: 100, default: 0 },
      psychic: { type: Number, min: 0, max: 100, default: 0 },
      quantum: { type: Number, min: 0, max: 100, default: 0 },
      divine: { type: Number, min: 0, max: 100, default: 0 }
    }
  },

  inventory: {
    items: [Schema.Types.Mixed],
    currency: {
      type: Map,
      of: Number,
      default: new Map()
    },
    nfts: [Schema.Types.Mixed],
    properties: [Schema.Types.Mixed],
    vehicles: [Schema.Types.Mixed],
    pets: [Schema.Types.Mixed]
  },

  relationships: {
    friends: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    family: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    romantic: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    business: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    rivals: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    mentors: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    students: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }]
  },

  achievements: {
    titles: [String],
    badges: [Schema.Types.Mixed],
    levels: {
      type: Map,
      of: Number,
      default: new Map()
    },
    experience: {
      type: Map,
      of: Number,
      default: new Map()
    },
    reputation: {
      type: Map,
      of: Number,
      default: new Map()
    },
    rankings: [Schema.Types.Mixed]
  },

  aiPersonality: {
    brainId: { type: Schema.Types.ObjectId, ref: 'NeuroBrain' },
    autonomyLevel: { type: Number, min: 0, max: 100, default: 0 },
    learningEnabled: { type: Boolean, default: false },
    emotionalState: Schema.Types.Mixed,
    goals: [Schema.Types.Mixed],
    memories: [Schema.Types.Mixed]
  },

  behavior: {
    personality: Schema.Types.Mixed,
    preferences: {
      type: Map,
      of: Schema.Types.Mixed
    },
    habits: [Schema.Types.Mixed],
    routines: [Schema.Types.Mixed],
    communication: Schema.Types.Mixed
  },

  location: {
    worldId: { type: Schema.Types.ObjectId, ref: 'VirtualWorld' },
    position: {
      x: { type: Number, default: 0 },
      y: { type: Number, default: 0 },
      z: { type: Number, default: 0 },
      rotation: Schema.Types.Mixed
    },
    region: String,
    building: String,
    room: String
  },

  status: {
    type: String,
    enum: ['online', 'offline', 'away', 'busy', 'invisible'],
    default: 'offline'
  },
  lastActive: { type: Date, default: Date.now }
}, {
  timestamps: true
});

const VirtualPropertySchema = new Schema<IVirtualProperty>({
  propertyId: { type: String, required: true, unique: true },
  worldId: { type: Schema.Types.ObjectId, ref: 'VirtualWorld', required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },

  type: {
    type: String,
    enum: ['land', 'building', 'structure', 'space', 'dimension'],
    required: true
  },
  name: { type: String, required: true },
  description: String,

  coordinates: {
    x: { type: Number, required: true },
    y: { type: Number, required: true },
    z: { type: Number, required: true },
    width: { type: Number, required: true },
    height: { type: Number, required: true },
    depth: { type: Number, required: true }
  },

  features: {
    rooms: [Schema.Types.Mixed],
    utilities: [Schema.Types.Mixed],
    security: [Schema.Types.Mixed],
    entertainment: [Schema.Types.Mixed],
    business: [Schema.Types.Mixed],
    special: [Schema.Types.Mixed]
  },

  economics: {
    purchasePrice: { type: Number, default: 0 },
    currentValue: { type: Number, default: 0 },
    marketValue: { type: Number, default: 0 },
    rentalIncome: { type: Number, default: 0 },
    expenses: { type: Number, default: 0 },
    roi: { type: Number, default: 0 },
    appreciation: { type: Number, default: 0 }
  },

  usage: {
    residential: { type: Boolean, default: false },
    commercial: { type: Boolean, default: false },
    industrial: { type: Boolean, default: false },
    entertainment: { type: Boolean, default: false },
    educational: { type: Boolean, default: false },
    mixed: { type: Boolean, default: false }
  },

  permissions: {
    public: { type: Boolean, default: false },
    friends: { type: Boolean, default: false },
    invited: { type: Boolean, default: false },
    private: { type: Boolean, default: true },
    commercial: { type: Boolean, default: false }
  },

  occupancy: {
    tenants: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    visitors: [{ type: Schema.Types.ObjectId, ref: 'VirtualAvatar' }],
    maxOccupancy: { type: Number, default: 10 },
    currentOccupancy: { type: Number, default: 0 }
  },

  customization: {
    theme: String,
    style: String,
    decorations: [Schema.Types.Mixed],
    furniture: [Schema.Types.Mixed],
    art: [Schema.Types.Mixed],
    technology: [Schema.Types.Mixed]
  },

  smartSystems: {
    aiButler: { type: Schema.Types.ObjectId, ref: 'NeuroBrain' },
    automation: [Schema.Types.Mixed],
    security: [Schema.Types.Mixed],
    entertainment: [Schema.Types.Mixed],
    maintenance: [Schema.Types.Mixed]
  },

  metrics: {
    visitors: { type: Number, default: 0 },
    events: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    satisfaction: { type: Number, default: 0 },
    popularity: { type: Number, default: 0 }
  },

  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'for-sale', 'for-rent'],
    default: 'active'
  }
}, {
  timestamps: true
});

// Create indexes
VirtualWorldSchema.index({ worldId: 1 });
VirtualWorldSchema.index({ creator: 1, type: 1 });
VirtualWorldSchema.index({ 'population.activeUsers': -1 });
VirtualAvatarSchema.index({ userId: 1, worldId: 1 });
VirtualAvatarSchema.index({ avatarId: 1 });
VirtualPropertySchema.index({ worldId: 1, owner: 1 });
VirtualPropertySchema.index({ propertyId: 1 });

// Export models
export const VirtualWorld = mongoose.model<IVirtualWorld>('VirtualWorld', VirtualWorldSchema);
export const VirtualAvatar = mongoose.model<IVirtualAvatar>('VirtualAvatar', VirtualAvatarSchema);
export const VirtualProperty = mongoose.model<IVirtualProperty>('VirtualProperty', VirtualPropertySchema);