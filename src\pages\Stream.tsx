import React, { useState } from 'react';
import LiveVideoPlayer from '../components/LiveVideoPlayer';
import LiveChat from '../components/LiveChat';

const Stream: React.FC = () => {
  // mock直播流地址（可替换为真实流媒体URL）
  const [streamUrl] = useState('https://www.w3schools.com/html/mov_bbb.mp4'); // 可用测试视频流

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-blue-900 to-purple-900 flex flex-col items-center pt-8">
      <h1 className="text-3xl font-bold text-white mb-2">直播流媒体</h1>
      <p className="text-white/70 mb-6">体验AI驱动的实时内容广播</p>
      <div className="flex flex-col md:flex-row gap-8 w-full max-w-6xl">
        <div className="flex-1">
          <LiveVideoPlayer streamUrl={streamUrl} />
        </div>
        <div className="w-full md:w-96">
          <LiveChat />
        </div>
      </div>
    </div>
  );
};

export default Stream; 