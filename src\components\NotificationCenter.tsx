import React, { useState } from 'react';
import { Bell, X, Check, Star, Zap, Heart, Coins, Brain, Globe, Users, TrendingUp, AlertCircle, Info, CheckCircle } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface Notification {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error' | 'achievement' | 'social' | 'financial';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  icon?: React.ComponentType<any>;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

const NotificationCenter: React.FC = () => {
  const { state, dispatch } = useApp();
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  const notifications: Notification[] = [
    {
      id: '1',
      type: 'achievement',
      title: 'Quantum Level Unlocked!',
      message: 'You\'ve reached Consciousness Level Ω. New neural abilities available.',
      timestamp: new Date(Date.now() - 300000),
      read: false,
      icon: Brain,
      priority: 'high'
    },
    {
      id: '2',
      type: 'financial',
      title: 'Portfolio Alert',
      message: 'Your NEURO tokens gained +23.4% in the last hour.',
      timestamp: new Date(Date.now() - 600000),
      read: false,
      icon: TrendingUp,
      priority: 'medium'
    },
    {
      id: '3',
      type: 'social',
      title: 'Collaboration Request',
      message: 'QuantumArtist wants to collaborate on a metaverse project.',
      timestamp: new Date(Date.now() - 900000),
      read: true,
      icon: Users,
      priority: 'medium'
    },
    {
      id: '4',
      type: 'info',
      title: 'Neural Training Complete',
      message: 'Your focus enhancement session improved cognitive performance by 15%.',
      timestamp: new Date(Date.now() - 1800000),
      read: true,
      icon: Zap,
      priority: 'low'
    },
    {
      id: '5',
      type: 'success',
      title: 'NFT Sale Completed',
      message: 'Your "Quantum Dreams" NFT sold for 12.5 ETH.',
      timestamp: new Date(Date.now() - 3600000),
      read: false,
      icon: Coins,
      priority: 'high'
    }
  ];

  const getNotificationIcon = (notification: Notification) => {
    if (notification.icon) {
      return notification.icon;
    }
    
    switch (notification.type) {
      case 'success': return CheckCircle;
      case 'info': return Info;
      case 'warning': return AlertCircle;
      case 'error': return AlertCircle;
      case 'achievement': return Star;
      case 'social': return Users;
      case 'financial': return Coins;
      default: return Bell;
    }
  };

  const getNotificationColor = (notification: Notification) => {
    switch (notification.type) {
      case 'success': return 'from-green-500 to-emerald-500';
      case 'info': return 'from-blue-500 to-cyan-500';
      case 'warning': return 'from-yellow-500 to-orange-500';
      case 'error': return 'from-red-500 to-pink-500';
      case 'achievement': return 'from-purple-500 to-pink-500';
      case 'social': return 'from-blue-500 to-purple-500';
      case 'financial': return 'from-green-500 to-blue-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const getPriorityBorder = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-red-500';
      case 'high': return 'border-orange-500';
      case 'medium': return 'border-blue-500';
      case 'low': return 'border-gray-500';
      default: return 'border-white/10';
    }
  };

  const markAsRead = (notificationId: string) => {
    // Mark notification as read logic
  };

  const markAllAsRead = () => {
    // Mark all notifications as read logic
  };

  const deleteNotification = (notificationId: string) => {
    // Delete notification logic
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-white/80 hover:text-white transition-colors"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
            {unreadCount}
          </span>
        )}
      </button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute top-12 right-0 w-96 bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl z-50">
          {/* Header */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">Notifications</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 text-white/60 hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Filter Tabs */}
            <div className="flex space-x-2 overflow-x-auto">
              {[
                { id: 'all', name: 'All' },
                { id: 'unread', name: 'Unread' },
                { id: 'achievement', name: 'Achievements' },
                { id: 'financial', name: 'Financial' },
                { id: 'social', name: 'Social' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setFilter(tab.id)}
                  className={`px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors ${
                    filter === tab.id
                      ? 'bg-purple-600 text-white'
                      : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="px-6 py-3 border-b border-white/10">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">
                {filteredNotifications.length} notifications
              </span>
              <button
                onClick={markAllAsRead}
                className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors"
              >
                Mark all read
              </button>
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-6 text-center">
                <Bell className="h-12 w-12 text-white/40 mx-auto mb-3" />
                <p className="text-white/60">No notifications</p>
              </div>
            ) : (
              <div className="space-y-1">
                {filteredNotifications.map((notification) => {
                  const IconComponent = getNotificationIcon(notification);
                  return (
                    <div
                      key={notification.id}
                      className={`p-4 border-l-4 ${getPriorityBorder(notification.priority)} hover:bg-white/5 transition-colors cursor-pointer ${
                        !notification.read ? 'bg-white/5' : ''
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${getNotificationColor(notification)}`}>
                          <IconComponent className="h-4 w-4 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className={`text-sm font-medium ${notification.read ? 'text-white/80' : 'text-white'}`}>
                              {notification.title}
                            </h4>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-white/50">
                                {formatTimeAgo(notification.timestamp)}
                              </span>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteNotification(notification.id);
                                }}
                                className="p-1 text-white/40 hover:text-white/60 transition-colors"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </div>
                          </div>
                          <p className={`text-sm mt-1 ${notification.read ? 'text-white/60' : 'text-white/80'}`}>
                            {notification.message}
                          </p>
                          {notification.actionUrl && (
                            <button className="text-purple-400 hover:text-purple-300 text-xs mt-2 font-medium">
                              View Details →
                            </button>
                          )}
                        </div>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-white/10">
            <button className="w-full text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
              View All Notifications
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;