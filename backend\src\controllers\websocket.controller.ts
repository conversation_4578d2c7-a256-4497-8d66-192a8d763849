import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { User } from '../models/user.model';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
}

export const initializeWebSocket = (server: HTTPServer) => {
  const io = new SocketIOServer(server, {
    cors: {
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true
    }
  });

  // Authentication middleware for WebSocket
  io.use(async (socket: any, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        return next(new Error('JWT secret not configured'));
      }

      const decoded = jwt.verify(token, jwtSecret) as any;
      const user = await User.findById(decoded.id);
      
      if (!user || !user.isActive) {
        return next(new Error('Invalid token or user not found'));
      }

      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  // Connection handler
  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User ${socket.user?.name} connected: ${socket.id}`);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Handle live streaming events
    socket.on('join-stream', (streamId: string) => {
      socket.join(`stream:${streamId}`);
      socket.to(`stream:${streamId}`).emit('user-joined-stream', {
        userId: socket.userId,
        userName: socket.user?.name
      });
    });

    socket.on('leave-stream', (streamId: string) => {
      socket.leave(`stream:${streamId}`);
      socket.to(`stream:${streamId}`).emit('user-left-stream', {
        userId: socket.userId,
        userName: socket.user?.name
      });
    });

    socket.on('stream-message', (data: { streamId: string; message: string }) => {
      const messageData = {
        id: Date.now().toString(),
        userId: socket.userId,
        userName: socket.user?.name,
        userAvatar: socket.user?.avatar,
        message: data.message,
        timestamp: new Date()
      };
      
      io.to(`stream:${data.streamId}`).emit('stream-message', messageData);
    });

    // Handle chat events
    socket.on('join-chat', (chatId: string) => {
      socket.join(`chat:${chatId}`);
    });

    socket.on('leave-chat', (chatId: string) => {
      socket.leave(`chat:${chatId}`);
    });

    socket.on('send-message', (data: { chatId: string; message: string; type?: string }) => {
      const messageData = {
        id: Date.now().toString(),
        userId: socket.userId,
        userName: socket.user?.name,
        userAvatar: socket.user?.avatar,
        message: data.message,
        type: data.type || 'text',
        timestamp: new Date()
      };
      
      io.to(`chat:${data.chatId}`).emit('new-message', messageData);
    });

    // Handle typing indicators
    socket.on('typing-start', (data: { chatId: string }) => {
      socket.to(`chat:${data.chatId}`).emit('user-typing', {
        userId: socket.userId,
        userName: socket.user?.name
      });
    });

    socket.on('typing-stop', (data: { chatId: string }) => {
      socket.to(`chat:${data.chatId}`).emit('user-stopped-typing', {
        userId: socket.userId
      });
    });

    // Handle notifications
    socket.on('mark-notification-read', (notificationId: string) => {
      // In a real app, you'd update the notification in the database
      socket.emit('notification-marked-read', { notificationId });
    });

    // Handle live commerce events
    socket.on('join-product-room', (productId: string) => {
      socket.join(`product:${productId}`);
    });

    socket.on('leave-product-room', (productId: string) => {
      socket.leave(`product:${productId}`);
    });

    socket.on('product-question', (data: { productId: string; question: string }) => {
      const questionData = {
        id: Date.now().toString(),
        userId: socket.userId,
        userName: socket.user?.name,
        question: data.question,
        timestamp: new Date()
      };
      
      io.to(`product:${data.productId}`).emit('new-product-question', questionData);
    });

    // Handle course events
    socket.on('join-course', (courseId: string) => {
      socket.join(`course:${courseId}`);
    });

    socket.on('leave-course', (courseId: string) => {
      socket.leave(`course:${courseId}`);
    });

    socket.on('course-progress', (data: { courseId: string; lessonId: string; progress: number }) => {
      // Update progress in database (simplified)
      socket.emit('progress-updated', {
        courseId: data.courseId,
        lessonId: data.lessonId,
        progress: data.progress
      });
    });

    // Handle NFT events
    socket.on('join-nft-auction', (nftId: string) => {
      socket.join(`nft:${nftId}`);
    });

    socket.on('place-bid', (data: { nftId: string; amount: number }) => {
      const bidData = {
        id: Date.now().toString(),
        userId: socket.userId,
        userName: socket.user?.name,
        amount: data.amount,
        timestamp: new Date()
      };
      
      io.to(`nft:${data.nftId}`).emit('new-bid', bidData);
    });

    // Handle AI assistant events
    socket.on('ai-query', (data: { query: string; context?: string }) => {
      // In a real app, you'd process this with an AI service
      const response = {
        id: Date.now().toString(),
        query: data.query,
        response: `AI response to: ${data.query}`,
        timestamp: new Date()
      };
      
      socket.emit('ai-response', response);
    });

    // Handle user activity updates
    socket.on('user-activity', (activity: string) => {
      // Update user's last active timestamp
      if (socket.user) {
        socket.user.activity.lastActive = new Date();
        socket.user.save().catch(console.error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`User ${socket.user?.name} disconnected: ${reason}`);
      
      // Update user's last active timestamp
      if (socket.user) {
        socket.user.activity.lastActive = new Date();
        socket.user.save().catch(console.error);
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });

  return io;
};

// Helper functions to emit events from other parts of the application
export const emitToUser = (io: SocketIOServer, userId: string, event: string, data: any) => {
  io.to(`user:${userId}`).emit(event, data);
};

export const emitToStream = (io: SocketIOServer, streamId: string, event: string, data: any) => {
  io.to(`stream:${streamId}`).emit(event, data);
};

export const emitToProduct = (io: SocketIOServer, productId: string, event: string, data: any) => {
  io.to(`product:${productId}`).emit(event, data);
};

export const emitToCourse = (io: SocketIOServer, courseId: string, event: string, data: any) => {
  io.to(`course:${courseId}`).emit(event, data);
};

export const emitToNFT = (io: SocketIOServer, nftId: string, event: string, data: any) => {
  io.to(`nft:${nftId}`).emit(event, data);
};
