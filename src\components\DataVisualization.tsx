import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, BarChart3, PieChart, Activity, DollarSign } from 'lucide-react';

interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }>;
}

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  color: string;
}

interface DataVisualizationProps {
  type: 'sales' | 'analytics' | 'performance' | 'overview';
  data?: any;
  timeRange?: '7d' | '30d' | '90d' | '1y';
}

const DataVisualization: React.FC<DataVisualizationProps> = ({ 
  type, 
  data, 
  timeRange = '30d' 
}) => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [metrics, setMetrics] = useState<MetricCard[]>([]);
  const [loading, setLoading] = useState(true);

  // Generate mock data based on type
  const generateMockData = () => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
    const labels = Array.from({ length: days }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });

    switch (type) {
      case 'sales':
        return {
          chartData: {
            labels,
            datasets: [
              {
                label: 'Revenue',
                data: Array.from({ length: days }, () => Math.floor(Math.random() * 5000) + 1000),
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 2
              },
              {
                label: 'Orders',
                data: Array.from({ length: days }, () => Math.floor(Math.random() * 100) + 20),
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderColor: 'rgb(16, 185, 129)',
                borderWidth: 2
              }
            ]
          },
          metrics: [
            {
              title: 'Total Revenue',
              value: '$124,563',
              change: 12.5,
              changeType: 'increase' as const,
              icon: <DollarSign className="h-6 w-6" />,
              color: 'text-green-400'
            },
            {
              title: 'Total Orders',
              value: '2,847',
              change: 8.2,
              changeType: 'increase' as const,
              icon: <BarChart3 className="h-6 w-6" />,
              color: 'text-blue-400'
            },
            {
              title: 'Average Order Value',
              value: '$43.75',
              change: -2.1,
              changeType: 'decrease' as const,
              icon: <TrendingUp className="h-6 w-6" />,
              color: 'text-purple-400'
            },
            {
              title: 'Conversion Rate',
              value: '3.2%',
              change: 0.5,
              changeType: 'increase' as const,
              icon: <Activity className="h-6 w-6" />,
              color: 'text-yellow-400'
            }
          ]
        };

      case 'analytics':
        return {
          chartData: {
            labels,
            datasets: [
              {
                label: 'Page Views',
                data: Array.from({ length: days }, () => Math.floor(Math.random() * 10000) + 2000),
                backgroundColor: 'rgba(168, 85, 247, 0.1)',
                borderColor: 'rgb(168, 85, 247)',
                borderWidth: 2
              },
              {
                label: 'Unique Visitors',
                data: Array.from({ length: days }, () => Math.floor(Math.random() * 5000) + 1000),
                backgroundColor: 'rgba(236, 72, 153, 0.1)',
                borderColor: 'rgb(236, 72, 153)',
                borderWidth: 2
              }
            ]
          },
          metrics: [
            {
              title: 'Total Views',
              value: '847,293',
              change: 15.3,
              changeType: 'increase' as const,
              icon: <Activity className="h-6 w-6" />,
              color: 'text-purple-400'
            },
            {
              title: 'Unique Users',
              value: '124,567',
              change: 7.8,
              changeType: 'increase' as const,
              icon: <TrendingUp className="h-6 w-6" />,
              color: 'text-pink-400'
            },
            {
              title: 'Bounce Rate',
              value: '32.4%',
              change: -5.2,
              changeType: 'decrease' as const,
              icon: <TrendingDown className="h-6 w-6" />,
              color: 'text-green-400'
            },
            {
              title: 'Avg. Session',
              value: '4m 32s',
              change: 12.1,
              changeType: 'increase' as const,
              icon: <BarChart3 className="h-6 w-6" />,
              color: 'text-blue-400'
            }
          ]
        };

      default:
        return {
          chartData: {
            labels,
            datasets: [
              {
                label: 'Performance',
                data: Array.from({ length: days }, () => Math.floor(Math.random() * 100) + 20),
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 2
              }
            ]
          },
          metrics: []
        };
    }
  };

  // Simple line chart component
  const LineChart: React.FC<{ data: ChartData }> = ({ data }) => {
    const maxValue = Math.max(...data.datasets.flatMap(d => d.data));
    const minValue = Math.min(...data.datasets.flatMap(d => d.data));
    const range = maxValue - minValue;

    return (
      <div className="relative h-64 w-full">
        <svg className="w-full h-full" viewBox="0 0 800 200">
          {/* Grid lines */}
          {[0, 1, 2, 3, 4].map(i => (
            <line
              key={i}
              x1="0"
              y1={i * 40}
              x2="800"
              y2={i * 40}
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="1"
            />
          ))}
          
          {/* Data lines */}
          {data.datasets.map((dataset, datasetIndex) => {
            const points = dataset.data.map((value, index) => {
              const x = (index / (dataset.data.length - 1)) * 800;
              const y = 200 - ((value - minValue) / range) * 200;
              return `${x},${y}`;
            }).join(' ');

            return (
              <g key={datasetIndex}>
                {/* Area fill */}
                <path
                  d={`M 0,200 L ${points} L 800,200 Z`}
                  fill={dataset.backgroundColor}
                  opacity="0.3"
                />
                {/* Line */}
                <polyline
                  points={points}
                  fill="none"
                  stroke={dataset.borderColor}
                  strokeWidth={dataset.borderWidth || 2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                {/* Data points */}
                {dataset.data.map((value, index) => {
                  const x = (index / (dataset.data.length - 1)) * 800;
                  const y = 200 - ((value - minValue) / range) * 200;
                  return (
                    <circle
                      key={index}
                      cx={x}
                      cy={y}
                      r="3"
                      fill={dataset.borderColor}
                    />
                  );
                })}
              </g>
            );
          })}
        </svg>
        
        {/* Legend */}
        <div className="absolute top-4 right-4 space-y-2">
          {data.datasets.map((dataset, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: dataset.borderColor }}
              />
              <span className="text-sm text-white/80">{dataset.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Metric card component
  const MetricCard: React.FC<{ metric: MetricCard }> = ({ metric }) => (
    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-white/60 mb-1">{metric.title}</p>
          <p className="text-2xl font-bold text-white">{metric.value}</p>
        </div>
        <div className={`${metric.color}`}>
          {metric.icon}
        </div>
      </div>
      
      <div className="flex items-center mt-4">
        {metric.changeType === 'increase' ? (
          <TrendingUp className="h-4 w-4 text-green-400 mr-1" />
        ) : metric.changeType === 'decrease' ? (
          <TrendingDown className="h-4 w-4 text-red-400 mr-1" />
        ) : (
          <Activity className="h-4 w-4 text-gray-400 mr-1" />
        )}
        <span className={`text-sm ${
          metric.changeType === 'increase' ? 'text-green-400' : 
          metric.changeType === 'decrease' ? 'text-red-400' : 'text-gray-400'
        }`}>
          {metric.change > 0 ? '+' : ''}{metric.change}%
        </span>
        <span className="text-sm text-white/60 ml-1">vs last period</span>
      </div>
    </div>
  );

  // Load data on mount
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockData = generateMockData();
      setChartData(mockData.chartData);
      setMetrics(mockData.metrics);
      setLoading(false);
    }, 1000);
  }, [type, timeRange]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 animate-pulse">
              <div className="h-4 bg-white/10 rounded mb-2"></div>
              <div className="h-8 bg-white/10 rounded mb-4"></div>
              <div className="h-4 bg-white/10 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
          <div className="h-64 bg-white/10 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      {metrics.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <MetricCard key={index} metric={metric} />
          ))}
        </div>
      )}

      {/* Chart */}
      {chartData && (
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">
              {type === 'sales' ? 'Sales Overview' : 
               type === 'analytics' ? 'Analytics Overview' : 
               'Performance Overview'}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-white/60">
              <BarChart3 className="h-4 w-4" />
              <span>Last {timeRange}</span>
            </div>
          </div>
          
          <LineChart data={chartData} />
        </div>
      )}
    </div>
  );
};

export default DataVisualization;
