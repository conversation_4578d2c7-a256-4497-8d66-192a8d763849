import { useState, useEffect, useCallback } from 'react';

interface QuantumState {
  coherence: number;
  entanglement: number;
  superposition: number;
  decoherence: number;
  probability: number[];
}

interface QuantumOperations {
  collapse: () => void;
  entangle: (targetState: QuantumState) => void;
  superpose: (states: QuantumState[]) => void;
  measure: () => number;
  reset: () => void;
}

export const useQuantumState = (initialState?: Partial<QuantumState>) => {
  const [quantumState, setQuantumState] = useState<QuantumState>({
    coherence: initialState?.coherence ?? 1.0,
    entanglement: initialState?.entanglement ?? 0.0,
    superposition: initialState?.superposition ?? 0.0,
    decoherence: initialState?.decoherence ?? 0.0,
    probability: initialState?.probability ?? [0.5, 0.5]
  });

  const [isObserved, setIsObserved] = useState(false);
  const [measurementHistory, setMeasurementHistory] = useState<number[]>([]);

  // Quantum decoherence simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setQuantumState(prev => ({
        ...prev,
        decoherence: Math.min(prev.decoherence + 0.001, 1.0),
        coherence: Math.max(prev.coherence - 0.001, 0.0)
      }));
    }, 100);

    return () => clearInterval(interval);
  }, []);

  const collapse = useCallback(() => {
    setQuantumState(prev => {
      const collapsed = Math.random() < prev.probability[0] ? 0 : 1;
      return {
        ...prev,
        superposition: 0,
        probability: collapsed === 0 ? [1, 0] : [0, 1],
        coherence: 0
      };
    });
    setIsObserved(true);
  }, []);

  const entangle = useCallback((targetState: QuantumState) => {
    setQuantumState(prev => ({
      ...prev,
      entanglement: Math.min(prev.entanglement + 0.5, 1.0),
      coherence: (prev.coherence + targetState.coherence) / 2
    }));
  }, []);

  const superpose = useCallback((states: QuantumState[]) => {
    const avgCoherence = states.reduce((sum, state) => sum + state.coherence, 0) / states.length;
    const avgSuperposition = states.reduce((sum, state) => sum + state.superposition, 0) / states.length;
    
    setQuantumState(prev => ({
      ...prev,
      superposition: Math.min(avgSuperposition + 0.3, 1.0),
      coherence: avgCoherence,
      probability: [0.5, 0.5] // Equal superposition
    }));
  }, []);

  const measure = useCallback(() => {
    const measurement = Math.random();
    setMeasurementHistory(prev => [...prev.slice(-9), measurement]);
    collapse();
    return measurement;
  }, [collapse]);

  const reset = useCallback(() => {
    setQuantumState({
      coherence: 1.0,
      entanglement: 0.0,
      superposition: 0.0,
      decoherence: 0.0,
      probability: [0.5, 0.5]
    });
    setIsObserved(false);
    setMeasurementHistory([]);
  }, []);

  const operations: QuantumOperations = {
    collapse,
    entangle,
    superpose,
    measure,
    reset
  };

  return {
    quantumState,
    isObserved,
    measurementHistory,
    operations
  };
};