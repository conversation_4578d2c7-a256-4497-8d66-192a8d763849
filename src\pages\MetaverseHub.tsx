import React, { useState, useEffect } from 'react';
import { 
  Globe, 
  Users, 
  Gamepad2, 
  Building, 
  Palette, 
  Zap, 
  Crown, 
  Gem,
  Rocket,
  Star,
  Eye,
  Heart,
  MessageCircle,
  Share2,
  Play,
  Settings,
  Map,
  Compass,
  Camera,
  Mic,
  Video,
  Headphones,
  Wifi,
  Battery,
  Signal
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const MetaverseHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState('worlds');
  const [vrMode, setVrMode] = useState(false);
  const [avatarRotation, setAvatarRotation] = useState(0);
  const { state, dispatch } = useApp();

  useEffect(() => {
    const interval = setInterval(() => {
      setAvatarRotation(prev => (prev + 1) % 360);
    }, 50);
    return () => clearInterval(interval);
  }, []);

  const tabs = [
    { id: 'worlds', name: 'Virtual Worlds', icon: Globe },
    { id: 'avatar', name: 'Avatar Studio', icon: Users },
    { id: 'experiences', name: 'Experiences', icon: Gamepad2 },
    { id: 'real-estate', name: 'Virtual Real Estate', icon: Building },
    { id: 'creator-tools', name: 'Creator Tools', icon: Palette },
    { id: 'social', name: 'Social Spaces', icon: Heart }
  ];

  const virtualWorlds = [
    {
      id: '1',
      name: 'NeuroCity Prime',
      description: 'Futuristic cyberpunk metropolis with AI-driven NPCs',
      users: 2847293,
      rating: 4.9,
      thumbnail: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Cyberpunk',
      economy: '$12.4M',
      landPrices: '0.5-50 ETH',
      features: ['AI NPCs', 'Quantum Physics', 'Neural Interface', 'Time Dilation']
    },
    {
      id: '2',
      name: 'Ethereal Gardens',
      description: 'Mystical fantasy realm with magic and ancient mysteries',
      users: 1923847,
      rating: 4.8,
      thumbnail: 'https://images.pexels.com/photos/3965545/pexels-photo-3965545.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Fantasy',
      economy: '$8.7M',
      landPrices: '0.3-25 ETH',
      features: ['Magic System', 'Dragon Companions', 'Spell Crafting', 'Realm Portals']
    },
    {
      id: '3',
      name: 'Quantum Labs',
      description: 'Scientific research facility for collaborative experiments',
      users: 847293,
      rating: 4.9,
      thumbnail: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Science',
      economy: '$15.2M',
      landPrices: '1-100 ETH',
      features: ['Real Physics', 'Lab Equipment', 'Collaboration Tools', 'Data Visualization']
    },
    {
      id: '4',
      name: 'Cosmic Arena',
      description: 'Intergalactic sports and competition hub',
      users: 3847293,
      rating: 4.7,
      thumbnail: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=400',
      category: 'Sports',
      economy: '$22.1M',
      landPrices: '0.8-75 ETH',
      features: ['Zero Gravity', 'Esports Arenas', 'Tournaments', 'Spectator Modes']
    }
  ];

  const avatarAssets = [
    { category: 'Bodies', items: 247, rarity: 'Common to Legendary' },
    { category: 'Faces', items: 189, rarity: 'Common to Mythic' },
    { category: 'Hair', items: 156, rarity: 'Common to Epic' },
    { category: 'Clothing', items: 892, rarity: 'Common to Legendary' },
    { category: 'Accessories', items: 445, rarity: 'Common to Mythic' },
    { category: 'Animations', items: 234, rarity: 'Rare to Legendary' }
  ];

  const experiences = [
    {
      id: '1',
      name: 'Neural Concert Hall',
      type: 'Music Experience',
      participants: 50000,
      duration: '2 hours',
      price: '0.1 ETH',
      rating: 4.9,
      features: ['Synesthetic Visuals', 'Emotional Resonance', 'Collective Consciousness']
    },
    {
      id: '2',
      name: 'Quantum Art Gallery',
      type: 'Art Exhibition',
      participants: 25000,
      duration: '1 hour',
      price: '0.05 ETH',
      rating: 4.8,
      features: ['Interactive Art', 'AI Curation', 'Creator Meetups']
    },
    {
      id: '3',
      name: 'Time Travel Academy',
      type: 'Educational',
      participants: 75000,
      duration: '3 hours',
      price: '0.2 ETH',
      rating: 4.9,
      features: ['Historical Simulation', 'Time Paradox Games', 'Expert Guides']
    }
  ];

  const realEstateListings = [
    {
      id: '1',
      location: 'NeuroCity Prime - Downtown',
      size: '1000 m²',
      price: '25 ETH',
      type: 'Commercial',
      features: ['High Traffic', 'Holographic Displays', 'AI Integration'],
      roi: '+15% monthly'
    },
    {
      id: '2',
      location: 'Ethereal Gardens - Mystic Grove',
      size: '2500 m²',
      price: '18 ETH',
      type: 'Residential',
      features: ['Magical Ambiance', 'Dragon Nest', 'Spell Components'],
      roi: '+12% monthly'
    },
    {
      id: '3',
      location: 'Quantum Labs - Research Wing',
      size: '500 m²',
      price: '45 ETH',
      type: 'Laboratory',
      features: ['Quantum Equipment', 'Collaboration Space', 'Data Access'],
      roi: '+20% monthly'
    }
  ];

  const socialSpaces = [
    {
      id: '1',
      name: 'Consciousness Café',
      type: 'Social Hub',
      users: 15420,
      description: 'Meet minds from across the multiverse',
      features: ['Thought Sharing', 'Emotion Sync', 'Memory Exchange']
    },
    {
      id: '2',
      name: 'Creator Collective',
      type: 'Workspace',
      users: 8934,
      description: 'Collaborative creation space for artists',
      features: ['Real-time Collaboration', 'Asset Sharing', 'Mentorship']
    },
    {
      id: '3',
      name: 'Quantum Lounge',
      type: 'Entertainment',
      users: 23456,
      description: 'Relax in probability-defying environments',
      features: ['Reality Shifting', 'Time Dilation', 'Parallel Conversations']
    }
  ];

  const renderWorlds = () => (
    <div className="space-y-8">
      {/* VR Mode Toggle */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-bold text-white">Explore Virtual Worlds</h3>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-white/70">VR Mode</span>
            <button
              onClick={() => setVrMode(!vrMode)}
              className={`w-12 h-6 rounded-full p-1 transition-colors ${
                vrMode ? 'bg-blue-600' : 'bg-white/20'
              }`}
            >
              <div className={`w-4 h-4 rounded-full bg-white transition-transform ${
                vrMode ? 'translate-x-6' : 'translate-x-0'
              }`} />
            </button>
          </div>
          <div className="flex items-center space-x-2 text-white/60">
            <Signal className="h-4 w-4" />
            <Battery className="h-4 w-4" />
            <Wifi className="h-4 w-4" />
          </div>
        </div>
      </div>

      {/* World Grid */}
      <div className="grid md:grid-cols-2 gap-6">
        {virtualWorlds.map((world) => (
          <div key={world.id} className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105">
            <div className="relative">
              <img
                src={world.thumbnail}
                alt={world.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-3 left-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                {world.category}
              </div>
              <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                <Users className="h-3 w-3" />
                <span>{(world.users / 1000000).toFixed(1)}M</span>
              </div>
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2">
                  <Play className="h-5 w-5" />
                  <span>Enter World</span>
                </button>
              </div>
            </div>
            <div className="p-6">
              <h4 className="text-xl font-semibold text-white mb-2">{world.name}</h4>
              <p className="text-white/70 text-sm mb-4">{world.description}</p>
              
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <div className="text-white/60">Economy</div>
                  <div className="text-green-400 font-semibold">{world.economy}</div>
                </div>
                <div>
                  <div className="text-white/60">Land Prices</div>
                  <div className="text-blue-400 font-semibold">{world.landPrices}</div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                {world.features.map((feature, index) => (
                  <span key={index} className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full text-xs">
                    {feature}
                  </span>
                ))}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-white/80 text-sm">{world.rating}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-white/60 hover:text-white transition-colors">
                    <Heart className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-white/60 hover:text-white transition-colors">
                    <Share2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* World Statistics */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Metaverse Statistics</h4>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">847</div>
            <div className="text-white/60">Active Worlds</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">12.4M</div>
            <div className="text-white/60">Daily Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">$2.8B</div>
            <div className="text-white/60">Virtual Economy</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">∞</div>
            <div className="text-white/60">Possibilities</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAvatar = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Avatar Creation Studio</h3>
        <p className="text-white/70 text-lg">Design your perfect digital identity</p>
      </div>

      {/* Avatar Preview */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8">
          <h4 className="text-xl font-semibold text-white mb-6 text-center">Avatar Preview</h4>
          <div className="relative h-64 bg-black/40 rounded-2xl flex items-center justify-center overflow-hidden">
            <div 
              className="w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center transform transition-transform duration-1000"
              style={{ transform: `rotate(${avatarRotation}deg)` }}
            >
              <Users className="h-16 w-16 text-white" />
            </div>
            <div className="absolute bottom-4 left-4 right-4 flex justify-center space-x-2">
              <button className="bg-white/20 hover:bg-white/30 text-white p-2 rounded-lg transition-colors">
                <Camera className="h-4 w-4" />
              </button>
              <button className="bg-white/20 hover:bg-white/30 text-white p-2 rounded-lg transition-colors">
                <Video className="h-4 w-4" />
              </button>
              <button className="bg-white/20 hover:bg-white/30 text-white p-2 rounded-lg transition-colors">
                <Settings className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <h4 className="text-xl font-semibold text-white">Customization Options</h4>
          {avatarAssets.map((asset, index) => (
            <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="text-white font-semibold">{asset.category}</h5>
                  <p className="text-white/60 text-sm">{asset.items} items available</p>
                </div>
                <div className="text-right">
                  <div className="text-purple-400 text-sm">{asset.rarity}</div>
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm mt-1">
                    Customize
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Avatar Marketplace */}
      <div>
        <h4 className="text-2xl font-bold text-white mb-6">Avatar Marketplace</h4>
        <div className="grid md:grid-cols-4 gap-6">
          {[
            { name: 'Cyber Punk Suit', price: '0.5 ETH', rarity: 'Epic' },
            { name: 'Dragon Wings', price: '1.2 ETH', rarity: 'Legendary' },
            { name: 'Neural Interface', price: '2.5 ETH', rarity: 'Mythic' },
            { name: 'Quantum Aura', price: '5.0 ETH', rarity: 'Transcendent' }
          ].map((item, index) => (
            <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Gem className="h-8 w-8 text-white" />
              </div>
              <h5 className="text-white font-semibold mb-2">{item.name}</h5>
              <div className="text-2xl font-bold text-green-400 mb-2">{item.price}</div>
              <div className="text-purple-400 text-sm mb-4">{item.rarity}</div>
              <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors">
                Purchase
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderExperiences = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Immersive Experiences</h3>
        <p className="text-white/70 text-lg">Discover mind-bending virtual experiences</p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {experiences.map((experience) => (
          <div key={experience.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-white">{experience.name}</h4>
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-white/80 text-sm">{experience.rating}</span>
              </div>
            </div>
            
            <div className="space-y-3 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-white/60">Type</span>
                <span className="text-blue-400">{experience.type}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-white/60">Participants</span>
                <span className="text-green-400">{experience.participants.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-white/60">Duration</span>
                <span className="text-purple-400">{experience.duration}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-white/60">Price</span>
                <span className="text-orange-400">{experience.price}</span>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              {experience.features.map((feature, index) => (
                <span key={index} className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                  {feature}
                </span>
              ))}
            </div>

            <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors font-semibold">
              Join Experience
            </button>
          </div>
        ))}
      </div>
    </div>
  );

  const renderRealEstate = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">Virtual Real Estate</h3>
        <p className="text-white/70 text-lg">Invest in prime virtual properties</p>
      </div>

      <div className="space-y-6">
        {realEstateListings.map((listing) => (
          <div key={listing.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-white mb-2">{listing.location}</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="text-white/60">Size</div>
                    <div className="text-white font-semibold">{listing.size}</div>
                  </div>
                  <div>
                    <div className="text-white/60">Type</div>
                    <div className="text-blue-400 font-semibold">{listing.type}</div>
                  </div>
                  <div>
                    <div className="text-white/60">ROI</div>
                    <div className="text-green-400 font-semibold">{listing.roi}</div>
                  </div>
                  <div>
                    <div className="text-white/60">Price</div>
                    <div className="text-orange-400 font-semibold">{listing.price}</div>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mt-4">
                  {listing.features.map((feature, index) => (
                    <span key={index} className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs">
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
              <div className="lg:w-48 flex flex-col space-y-2">
                <button className="bg-gradient-to-r from-green-600 to-green-700 text-white py-2 px-4 rounded-lg hover:from-green-700 hover:to-green-800 transition-colors">
                  Purchase
                </button>
                <button className="bg-white/10 border border-white/20 text-white py-2 px-4 rounded-lg hover:bg-white/20 transition-colors">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Market Statistics */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
        <h4 className="text-2xl font-bold text-white mb-6 text-center">Market Overview</h4>
        <div className="grid md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">$2.8B</div>
            <div className="text-white/60">Total Market Value</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">+24%</div>
            <div className="text-white/60">Monthly Growth</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">156K</div>
            <div className="text-white/60">Properties Sold</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">18%</div>
            <div className="text-white/60">Average ROI</div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Metaverse <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Hub</span>
          </h1>
          <p className="text-white/70 text-lg">Enter infinite virtual worlds and experiences</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              } backdrop-blur-sm`}
            >
              <tab.icon className="h-5 w-5" />
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'worlds' && renderWorlds()}
          {activeTab === 'avatar' && renderAvatar()}
          {activeTab === 'experiences' && renderExperiences()}
          {activeTab === 'real-estate' && renderRealEstate()}
          {activeTab === 'creator-tools' && (
            <div className="text-center py-12">
              <Palette className="h-16 w-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Creator Tools Suite</h3>
              <p className="text-white/60">Build and monetize your own virtual worlds and experiences</p>
            </div>
          )}
          {activeTab === 'social' && (
            <div className="space-y-8">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-4">Social Spaces</h3>
                <p className="text-white/70 text-lg">Connect with others in immersive social environments</p>
              </div>
              <div className="grid md:grid-cols-3 gap-6">
                {socialSpaces.map((space) => (
                  <div key={space.id} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                    <h4 className="text-lg font-semibold text-white mb-2">{space.name}</h4>
                    <p className="text-white/70 text-sm mb-4">{space.description}</p>
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-blue-400 text-sm">{space.type}</span>
                      <span className="text-green-400 text-sm">{space.users.toLocaleString()} users</span>
                    </div>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {space.features.map((feature, index) => (
                        <span key={index} className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full text-xs">
                          {feature}
                        </span>
                      ))}
                    </div>
                    <button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 text-white py-2 rounded-lg hover:from-pink-700 hover:to-purple-700 transition-colors">
                      Join Space
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MetaverseHub;