import mongoose, { Document, Schema } from 'mongoose';

// Wallet Interface
export interface IWallet extends Document {
  userId: mongoose.Types.ObjectId;
  address: string;
  network: string;
  walletType: 'metamask' | 'walletconnect' | 'coinbase' | 'phantom';
  balance: Map<string, number>;
  isConnected: boolean;
  lastConnected: Date;
  transactions: mongoose.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

// Staking Position Interface
export interface IStakingPosition extends Document {
  userId: mongoose.Types.ObjectId;
  walletAddress: string;
  protocol: string;
  protocolId: string;
  token: string;
  amount: number;
  apy: number;
  rewards: number;
  startDate: Date;
  endDate?: Date;
  status: 'active' | 'unstaked' | 'pending';
  transactionHash: string;
  createdAt: Date;
  updatedAt: Date;
}

// DAO Proposal Interface
export interface IDAOProposal extends Document {
  proposalId: string;
  title: string;
  description: string;
  category: string;
  proposer: mongoose.Types.ObjectId;
  status: 'pending' | 'active' | 'passed' | 'rejected' | 'executed';
  votesFor: number;
  votesAgainst: number;
  totalVotes: number;
  quorum: number;
  startDate: Date;
  endDate: Date;
  executionDate?: Date;
  votes: Array<{
    voter: mongoose.Types.ObjectId;
    vote: 'for' | 'against';
    votingPower: number;
    timestamp: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

// Transaction Interface
export interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId;
  walletAddress: string;
  type: 'stake' | 'unstake' | 'swap' | 'transfer' | 'mint' | 'burn' | 'vote';
  fromToken?: string;
  toToken?: string;
  fromAmount?: number;
  toAmount?: number;
  fee: number;
  gasPrice: number;
  gasUsed: number;
  hash: string;
  blockNumber: number;
  network: string;
  status: 'pending' | 'confirmed' | 'failed';
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Liquidity Position Interface
export interface ILiquidityPosition extends Document {
  userId: mongoose.Types.ObjectId;
  walletAddress: string;
  protocol: string;
  tokenA: string;
  tokenB: string;
  amountA: number;
  amountB: number;
  lpTokens: number;
  apy: number;
  fees: number;
  status: 'active' | 'removed';
  transactionHash: string;
  createdAt: Date;
  updatedAt: Date;
}

// Yield Farm Position Interface
export interface IYieldFarmPosition extends Document {
  userId: mongoose.Types.ObjectId;
  walletAddress: string;
  farmId: string;
  farmName: string;
  lpToken: string;
  stakedAmount: number;
  rewards: number;
  apy: number;
  multiplier: number;
  status: 'active' | 'exited';
  transactionHash: string;
  createdAt: Date;
  updatedAt: Date;
}

// Schemas
const WalletSchema = new Schema<IWallet>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  address: { type: String, required: true },
  network: { type: String, required: true },
  walletType: { 
    type: String, 
    enum: ['metamask', 'walletconnect', 'coinbase', 'phantom'],
    required: true 
  },
  balance: {
    type: Map,
    of: Number,
    default: new Map()
  },
  isConnected: { type: Boolean, default: true },
  lastConnected: { type: Date, default: Date.now },
  transactions: [{ type: Schema.Types.ObjectId, ref: 'Transaction' }]
}, {
  timestamps: true
});

const StakingPositionSchema = new Schema<IStakingPosition>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  walletAddress: { type: String, required: true },
  protocol: { type: String, required: true },
  protocolId: { type: String, required: true },
  token: { type: String, required: true },
  amount: { type: Number, required: true },
  apy: { type: Number, required: true },
  rewards: { type: Number, default: 0 },
  startDate: { type: Date, required: true },
  endDate: Date,
  status: { 
    type: String, 
    enum: ['active', 'unstaked', 'pending'],
    default: 'active'
  },
  transactionHash: { type: String, required: true }
}, {
  timestamps: true
});

const DAOProposalSchema = new Schema<IDAOProposal>({
  proposalId: { type: String, required: true, unique: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  category: { type: String, required: true },
  proposer: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  status: { 
    type: String, 
    enum: ['pending', 'active', 'passed', 'rejected', 'executed'],
    default: 'pending'
  },
  votesFor: { type: Number, default: 0 },
  votesAgainst: { type: Number, default: 0 },
  totalVotes: { type: Number, default: 0 },
  quorum: { type: Number, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  executionDate: Date,
  votes: [{
    voter: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    vote: { type: String, enum: ['for', 'against'], required: true },
    votingPower: { type: Number, required: true },
    timestamp: { type: Date, default: Date.now }
  }]
}, {
  timestamps: true
});

const TransactionSchema = new Schema<ITransaction>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  walletAddress: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['stake', 'unstake', 'swap', 'transfer', 'mint', 'burn', 'vote'],
    required: true 
  },
  fromToken: String,
  toToken: String,
  fromAmount: Number,
  toAmount: Number,
  fee: { type: Number, required: true },
  gasPrice: { type: Number, required: true },
  gasUsed: { type: Number, required: true },
  hash: { type: String, required: true, unique: true },
  blockNumber: { type: Number, required: true },
  network: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['pending', 'confirmed', 'failed'],
    default: 'pending'
  },
  timestamp: { type: Date, required: true }
}, {
  timestamps: true
});

const LiquidityPositionSchema = new Schema<ILiquidityPosition>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  walletAddress: { type: String, required: true },
  protocol: { type: String, required: true },
  tokenA: { type: String, required: true },
  tokenB: { type: String, required: true },
  amountA: { type: Number, required: true },
  amountB: { type: Number, required: true },
  lpTokens: { type: Number, required: true },
  apy: { type: Number, required: true },
  fees: { type: Number, default: 0 },
  status: { 
    type: String, 
    enum: ['active', 'removed'],
    default: 'active'
  },
  transactionHash: { type: String, required: true }
}, {
  timestamps: true
});

const YieldFarmPositionSchema = new Schema<IYieldFarmPosition>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  walletAddress: { type: String, required: true },
  farmId: { type: String, required: true },
  farmName: { type: String, required: true },
  lpToken: { type: String, required: true },
  stakedAmount: { type: Number, required: true },
  rewards: { type: Number, default: 0 },
  apy: { type: Number, required: true },
  multiplier: { type: Number, default: 1 },
  status: { 
    type: String, 
    enum: ['active', 'exited'],
    default: 'active'
  },
  transactionHash: { type: String, required: true }
}, {
  timestamps: true
});

// Create indexes
WalletSchema.index({ userId: 1, address: 1 });
StakingPositionSchema.index({ userId: 1, status: 1 });
DAOProposalSchema.index({ status: 1, endDate: 1 });
TransactionSchema.index({ userId: 1, timestamp: -1 });
TransactionSchema.index({ hash: 1 });
LiquidityPositionSchema.index({ userId: 1, status: 1 });
YieldFarmPositionSchema.index({ userId: 1, status: 1 });

// Export models
export const Wallet = mongoose.model<IWallet>('Wallet', WalletSchema);
export const StakingPosition = mongoose.model<IStakingPosition>('StakingPosition', StakingPositionSchema);
export const DAOProposal = mongoose.model<IDAOProposal>('DAOProposal', DAOProposalSchema);
export const Transaction = mongoose.model<ITransaction>('Transaction', TransactionSchema);
export const LiquidityPosition = mongoose.model<ILiquidityPosition>('LiquidityPosition', LiquidityPositionSchema);
export const YieldFarmPosition = mongoose.model<IYieldFarmPosition>('YieldFarmPosition', YieldFarmPositionSchema);
