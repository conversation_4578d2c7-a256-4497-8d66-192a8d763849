import React, { useState, useEffect } from 'react';
import { Activity, Cpu, HardDrive, Wifi, Battery, Zap, Brain, Globe, Shield, TrendingUp } from 'lucide-react';

interface SystemMetrics {
  cpu: number;
  memory: number;
  network: number;
  battery: number;
  neuralSync: number;
  quantumCoherence: number;
  realityStability: number;
  consciousnessLevel: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    network: 0,
    battery: 0,
    neuralSync: 0,
    quantumCoherence: 0,
    realityStability: 0,
    consciousnessLevel: 0
  });

  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics({
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        network: Math.random() * 100,
        battery: 85 + Math.random() * 15,
        neuralSync: 90 + Math.random() * 10,
        quantumCoherence: 95 + Math.random() * 5,
        realityStability: 98 + Math.random() * 2,
        consciousnessLevel: 92 + Math.random() * 8
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (value: number, thresholds = { good: 80, warning: 60 }) => {
    if (value >= thresholds.good) return 'text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getBarColor = (value: number, thresholds = { good: 80, warning: 60 }) => {
    if (value >= thresholds.good) return 'from-green-500 to-emerald-500';
    if (value >= thresholds.warning) return 'from-yellow-500 to-orange-500';
    return 'from-red-500 to-pink-500';
  };

  const systemMetrics = [
    { label: 'CPU', value: metrics.cpu, icon: Cpu, unit: '%' },
    { label: 'Memory', value: metrics.memory, icon: HardDrive, unit: '%' },
    { label: 'Network', value: metrics.network, icon: Wifi, unit: '%' },
    { label: 'Battery', value: metrics.battery, icon: Battery, unit: '%' }
  ];

  const quantumMetrics = [
    { label: 'Neural Sync', value: metrics.neuralSync, icon: Brain, unit: '%' },
    { label: 'Quantum Coherence', value: metrics.quantumCoherence, icon: Zap, unit: '%' },
    { label: 'Reality Stability', value: metrics.realityStability, icon: Shield, unit: '%' },
    { label: 'Consciousness', value: metrics.consciousnessLevel, icon: Globe, unit: '%' }
  ];

  return (
    <div className="fixed top-24 right-6 z-30">
      <div className="bg-gradient-to-br from-slate-900/90 to-purple-900/90 backdrop-blur-md border border-white/20 rounded-2xl overflow-hidden">
        {/* Compact View */}
        <div 
          className="p-4 cursor-pointer hover:bg-white/5 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center space-x-3">
            <Activity className="h-5 w-5 text-purple-400" />
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Cpu className="h-4 w-4 text-blue-400" />
                <span className={`text-sm font-medium ${getStatusColor(metrics.cpu)}`}>
                  {metrics.cpu.toFixed(0)}%
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <Brain className="h-4 w-4 text-purple-400" />
                <span className={`text-sm font-medium ${getStatusColor(metrics.neuralSync)}`}>
                  {metrics.neuralSync.toFixed(0)}%
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <Battery className="h-4 w-4 text-green-400" />
                <span className={`text-sm font-medium ${getStatusColor(metrics.battery)}`}>
                  {metrics.battery.toFixed(0)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Expanded View */}
        {isExpanded && (
          <div className="border-t border-white/10 p-4 w-80">
            {/* System Metrics */}
            <div className="mb-6">
              <h4 className="text-white font-semibold mb-3 flex items-center space-x-2">
                <Cpu className="h-4 w-4" />
                <span>System Performance</span>
              </h4>
              <div className="space-y-3">
                {systemMetrics.map((metric, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <metric.icon className="h-4 w-4 text-white/60" />
                        <span className="text-white/80 text-sm">{metric.label}</span>
                      </div>
                      <span className={`text-sm font-medium ${getStatusColor(metric.value)}`}>
                        {metric.value.toFixed(1)}{metric.unit}
                      </span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full bg-gradient-to-r ${getBarColor(metric.value)} transition-all duration-300`}
                        style={{ width: `${metric.value}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quantum Metrics */}
            <div>
              <h4 className="text-white font-semibold mb-3 flex items-center space-x-2">
                <Zap className="h-4 w-4" />
                <span>Quantum Systems</span>
              </h4>
              <div className="space-y-3">
                {quantumMetrics.map((metric, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <metric.icon className="h-4 w-4 text-white/60" />
                        <span className="text-white/80 text-sm">{metric.label}</span>
                      </div>
                      <span className={`text-sm font-medium ${getStatusColor(metric.value, { good: 90, warning: 80 })}`}>
                        {metric.value.toFixed(1)}{metric.unit}
                      </span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full bg-gradient-to-r ${getBarColor(metric.value, { good: 90, warning: 80 })} transition-all duration-300`}
                        style={{ width: `${metric.value}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Status Indicators */}
            <div className="mt-6 pt-4 border-t border-white/10">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-green-500/20 text-green-400 p-2 rounded-lg text-center">
                  <div className="text-xs font-medium">System Status</div>
                  <div className="text-sm">Optimal</div>
                </div>
                <div className="bg-blue-500/20 text-blue-400 p-2 rounded-lg text-center">
                  <div className="text-xs font-medium">Quantum State</div>
                  <div className="text-sm">Coherent</div>
                </div>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="mt-4 bg-purple-500/20 text-purple-400 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-1">
                <TrendingUp className="h-4 w-4" />
                <span className="text-sm font-medium">Performance Tip</span>
              </div>
              <p className="text-xs">
                Neural sync is optimal. Perfect time for consciousness expansion exercises.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;