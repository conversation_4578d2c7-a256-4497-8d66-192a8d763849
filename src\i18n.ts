import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: {
    translation: {
      aria_title: 'ARIA',
      aria_subtitle: 'Quantum AI Assistant',
      ask_me_anything: 'Ask me anything...',
      voice_interface: 'Voice Interface',
      ready_to_listen: 'Ready to listen',
      listening: 'Listening...',
      speaking: 'Speaking...',
      ready: 'Ready',
      recent_commands: 'Recent Commands',
      quick_commands: 'Quick Commands',
    }
  },
  zh: {
    translation: {
      aria_title: '量子AI助手',
      aria_subtitle: '量子意识AI助手',
      ask_me_anything: '问我任何问题...',
      voice_interface: '语音界面',
      ready_to_listen: '准备聆听',
      listening: '正在聆听...',
      speaking: '正在说话...',
      ready: '就绪',
      recent_commands: '最近命令',
      quick_commands: '快捷命令',
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n; 