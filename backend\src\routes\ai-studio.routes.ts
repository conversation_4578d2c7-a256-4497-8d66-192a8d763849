import { Router } from 'express';
import {
  generateContent,
  getGenerationHistory,
  saveCreation,
  getCreations,
  deleteCreation,
  shareCreation,
  mintAsNFT,
  getAITools,
  getAICredits,
  purchaseCredits,
  getCollaborations,
  createCollaboration,
  joinCollaboration,
  getMarketplace,
  sellCreation,
  buyCreation,
  getTrendingContent,
  getAIInsights
} from '../controllers/ai-studio.controller';
import { authenticateToken, requireCreator } from '../middleware/auth.middleware';
import { validateAIRequest } from '../middleware/validation.middleware';

const router = Router();

// All AI studio routes require authentication
router.use(authenticateToken);

// Content Generation
router.post('/generate', validateAIRequest, generateContent);
router.get('/generation-history', getGenerationHistory);

// Creation Management
router.get('/creations', getCreations);
router.post('/creations', saveCreation);
router.delete('/creations/:id', deleteCreation);
router.post('/creations/:id/share', shareCreation);

// NFT Integration
router.post('/creations/:id/mint-nft', requireCreator, mintAsNFT);

// AI Tools & Credits
router.get('/tools', getAITools);
router.get('/credits', getAICredits);
router.post('/credits/purchase', purchaseCredits);

// Collaboration
router.get('/collaborations', getCollaborations);
router.post('/collaborations', createCollaboration);
router.post('/collaborations/:id/join', joinCollaboration);

// Marketplace
router.get('/marketplace', getMarketplace);
router.post('/marketplace/sell', requireCreator, sellCreation);
router.post('/marketplace/buy/:id', buyCreation);

// Analytics & Insights
router.get('/trending', getTrendingContent);
router.get('/insights', getAIInsights);

export default router;
