import { Request, Response } from 'express';
import { Product } from '../models/product.model';

// 假数据
let products: Product[] = [
  {
    id: '1',
    name: 'AI-Powered Smart Watch',
    price: 299,
    originalPrice: 399,
    image: 'https://images.pexels.com/photos/393047/pexels-photo-393047.jpeg?auto=compress&cs=tinysrgb&w=400',
    rating: 4.8,
    reviews: 1247,
    seller: 'TechNova',
    isLive: true,
    viewers: 2847,
    category: 'electronics'
  },
  {
    id: '2',
    name: 'Sustainable Fashion Bundle',
    price: 149,
    originalPrice: 199,
    image: 'https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=400',
    rating: 4.9,
    reviews: 892,
    seller: 'EcoStyle',
    isLive: false,
    viewers: 0,
    category: 'fashion'
  },
  {
    id: '3',
    name: 'Premium Skincare Set',
    price: 89,
    originalPrice: 129,
    image: 'https://images.pexels.com/photos/3685530/pexels-photo-3685530.jpeg?auto=compress&cs=tinysrgb&w=400',
    rating: 4.7,
    reviews: 634,
    seller: 'GlowBeauty',
    isLive: true,
    viewers: 1523,
    category: 'beauty'
  },
  {
    id: '4',
    name: 'Smart Home Starter Kit',
    price: 199,
    originalPrice: 299,
    image: 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=400',
    rating: 4.6,
    reviews: 445,
    seller: 'HomeAI',
    isLive: false,
    viewers: 0,
    category: 'home'
  },
  {
    id: '5',
    name: 'Wireless Gaming Headset',
    price: 179,
    originalPrice: 229,
    image: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=400',
    rating: 4.8,
    reviews: 1089,
    seller: 'GamePro',
    isLive: true,
    viewers: 3241,
    category: 'electronics'
  },
  {
    id: '6',
    name: 'Organic Tea Collection',
    price: 45,
    originalPrice: 65,
    image: 'https://images.pexels.com/photos/1638280/pexels-photo-1638280.jpeg?auto=compress&cs=tinysrgb&w=400',
    rating: 4.9,
    reviews: 723,
    seller: 'PureTea',
    isLive: false,
    viewers: 0,
    category: 'home'
  }
];

export const getProducts = (req: Request, res: Response) => {
  res.json(products);
};

export const getProductById = (req: Request, res: Response) => {
  const product = products.find(p => p.id === req.params.id);
  if (!product) return res.status(404).json({ message: 'Product not found' });
  res.json(product);
}; 