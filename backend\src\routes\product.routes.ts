import { Router } from 'express';
import {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  addProductReview
} from '../controllers/product.controller';
import { authenticateToken, requireCreator } from '../middleware/auth.middleware';
import { validate, productValidation } from '../middleware/validation.middleware';

const router = Router();

// Public routes
router.get('/', getProducts);
router.get('/:id', getProductById);

// Protected routes
router.post('/', authenticateToken, requireCreator, validate(productValidation.create), createProduct);
router.put('/:id', authenticateToken, validate(productValidation.update), updateProduct);
router.delete('/:id', authenticateToken, deleteProduct);

// Review routes
router.post('/:id/reviews', authenticateToken, addProductReview);

export default router;