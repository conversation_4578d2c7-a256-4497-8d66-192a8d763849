import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

// Generic validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }
    
    next();
  };
};

// User validation schemas
export const userValidation = {
  register: Joi.object({
    name: Joi.string().min(2).max(100).required().messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 100 characters',
      'any.required': 'Name is required'
    }),
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])')).required().messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
      'any.required': 'Password is required'
    }),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required().messages({
      'any.only': 'Passwords do not match',
      'any.required': 'Password confirmation is required'
    })
  }),

  login: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string().required().messages({
      'any.required': 'Password is required'
    })
  }),

  updateProfile: Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    bio: Joi.string().max(500).optional(),
    location: Joi.string().max(100).optional(),
    website: Joi.string().uri().optional(),
    socialLinks: Joi.object({
      twitter: Joi.string().optional(),
      linkedin: Joi.string().optional(),
      github: Joi.string().optional(),
      instagram: Joi.string().optional()
    }).optional(),
    skills: Joi.array().items(Joi.string()).optional(),
    interests: Joi.array().items(Joi.string()).optional()
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'Current password is required'
    }),
    newPassword: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])')).required().messages({
      'string.min': 'New password must be at least 8 characters long',
      'string.pattern.base': 'New password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
      'any.required': 'New password is required'
    }),
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
      'any.only': 'Passwords do not match',
      'any.required': 'Password confirmation is required'
    })
  })
};

// Product validation schemas
export const productValidation = {
  create: Joi.object({
    name: Joi.string().min(2).max(200).required(),
    description: Joi.string().min(10).max(2000).required(),
    price: Joi.number().min(0).required(),
    originalPrice: Joi.number().min(0).required(),
    category: Joi.string().valid('electronics', 'fashion', 'home', 'beauty', 'sports', 'books', 'toys', 'automotive').required(),
    subcategory: Joi.string().optional(),
    brand: Joi.string().required(),
    specifications: Joi.array().items(
      Joi.object({
        key: Joi.string().required(),
        value: Joi.string().required()
      })
    ).optional(),
    inventory: Joi.object({
      stock: Joi.number().min(0).required(),
      sku: Joi.string().required()
    }).required(),
    shipping: Joi.object({
      weight: Joi.number().min(0).required(),
      dimensions: Joi.object({
        length: Joi.number().min(0).required(),
        width: Joi.number().min(0).required(),
        height: Joi.number().min(0).required()
      }).required(),
      freeShipping: Joi.boolean().optional(),
      shippingCost: Joi.number().min(0).optional()
    }).required(),
    tags: Joi.array().items(Joi.string()).optional()
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(200).optional(),
    description: Joi.string().min(10).max(2000).optional(),
    price: Joi.number().min(0).optional(),
    originalPrice: Joi.number().min(0).optional(),
    category: Joi.string().valid('electronics', 'fashion', 'home', 'beauty', 'sports', 'books', 'toys', 'automotive').optional(),
    subcategory: Joi.string().optional(),
    brand: Joi.string().optional(),
    specifications: Joi.array().items(
      Joi.object({
        key: Joi.string().required(),
        value: Joi.string().required()
      })
    ).optional(),
    inventory: Joi.object({
      stock: Joi.number().min(0).optional(),
      sku: Joi.string().optional()
    }).optional(),
    shipping: Joi.object({
      weight: Joi.number().min(0).optional(),
      dimensions: Joi.object({
        length: Joi.number().min(0).optional(),
        width: Joi.number().min(0).optional(),
        height: Joi.number().min(0).optional()
      }).optional(),
      freeShipping: Joi.boolean().optional(),
      shippingCost: Joi.number().min(0).optional()
    }).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    status: Joi.string().valid('draft', 'active', 'inactive', 'out_of_stock').optional()
  })
};

// Course validation schemas
export const courseValidation = {
  create: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().min(20).max(2000).required(),
    price: Joi.number().min(0).required(),
    originalPrice: Joi.number().min(0).required(),
    category: Joi.string().valid('ai', 'blockchain', 'business', 'design', 'tech', 'marketing', 'health', 'finance').required(),
    subcategory: Joi.string().optional(),
    level: Joi.string().valid('Beginner', 'Intermediate', 'Advanced', 'Expert').required(),
    skills: Joi.array().items(Joi.string()).required(),
    requirements: Joi.array().items(Joi.string()).optional(),
    whatYouWillLearn: Joi.array().items(Joi.string()).required(),
    language: Joi.string().default('en'),
    tags: Joi.array().items(Joi.string()).optional()
  })
};

// Order validation schemas
export const orderValidation = {
  create: Joi.object({
    items: Joi.array().items(
      Joi.object({
        productId: Joi.string().required(),
        variantId: Joi.string().optional(),
        quantity: Joi.number().min(1).required()
      })
    ).min(1).required(),
    shipping: Joi.object({
      address: Joi.object({
        fullName: Joi.string().required(),
        addressLine1: Joi.string().required(),
        addressLine2: Joi.string().optional(),
        city: Joi.string().required(),
        state: Joi.string().required(),
        postalCode: Joi.string().required(),
        country: Joi.string().required(),
        phone: Joi.string().required()
      }).required(),
      method: Joi.string().required()
    }).required(),
    payment: Joi.object({
      method: Joi.string().valid('credit_card', 'paypal', 'stripe', 'crypto', 'bank_transfer').required()
    }).required()
  })
};

// NFT validation schemas
export const nftValidation = {
  create: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    description: Joi.string().min(1).max(1000).required(),
    category: Joi.string().valid('art', 'music', 'video', 'gaming', 'photography', 'utility', 'collectible').required(),
    attributes: Joi.array().items(
      Joi.object({
        traitType: Joi.string().required(),
        value: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
        displayType: Joi.string().valid('number', 'boost_percentage', 'boost_number', 'date').optional()
      })
    ).optional(),
    royalty: Joi.object({
      percentage: Joi.number().min(0).max(50).required(),
      recipient: Joi.string().required()
    }).required(),
    tags: Joi.array().items(Joi.string()).optional()
  })
};
