import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Brain, ShoppingCart, Video, GraduationCap, Wallet, Heart, CheckSquare, TrendingUp, Zap, ArrowRight, Play, Star, Globe, Shield, Sparkles, Users, Atom, Coins, Infinity, Target, Rocket, Eye, CloudLightning as Lightning } from 'lucide-react';
import { useApp } from '../context/AppContext';

const HomePage: React.FC = () => {
  const [activeFeature, setActiveFeature] = useState(0);
  const { state } = useApp();

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Personalization",
      description: "Revolutionary neural engine that learns from your behavior across all platforms to deliver hyper-personalized experiences.",
      color: "from-purple-500 to-purple-700",
      link: "/productivity"
    },
    {
      icon: Sparkles,
      title: "AI Creativity Studio",
      description: "Transform ideas into valuable digital assets with AI-powered content generation, collaboration, and NFT marketplace.",
      color: "from-pink-500 to-purple-700",
      link: "/ai-studio"
    },
    {
      icon: Atom,
      title: "Quantum AI Computing",
      description: "Harness quantum computing power for unprecedented AI capabilities, consciousness simulation, and reality synthesis.",
      color: "from-blue-500 to-cyan-500",
      link: "/quantum-ai"
    },
    {
      icon: Globe,
      title: "Metaverse Ecosystem",
      description: "Enter infinite virtual worlds with photorealistic graphics, AI NPCs, and cross-dimensional experiences.",
      color: "from-green-500 to-emerald-500",
      link: "/metaverse"
    },
    {
      icon: Coins,
      title: "Quantum Blockchain",
      description: "Next-generation blockchain with quantum encryption, instant transactions, and AI-powered DeFi protocols.",
      color: "from-orange-500 to-red-500",
      link: "/blockchain"
    },
    {
      icon: Zap,
      title: "Neural Interface",
      description: "Direct brain-computer interface for thought-controlled computing, emotion sync, and consciousness expansion.",
      color: "from-purple-500 to-pink-500",
      link: "/neuro-interface"
    },
    {
      icon: ShoppingCart,
      title: "Social Commerce Hub",
      description: "Discover, shop, and sell with integrated social features, live streaming, and AI-curated recommendations.",
      color: "from-blue-500 to-blue-700",
      link: "/commerce"
    },
    {
      icon: Video,
      title: "Creator Economy Platform",
      description: "Monetize your content through multiple channels: subscriptions, NFTs, virtual goods, and brand partnerships.",
      color: "from-green-500 to-green-700",
      link: "/creator"
    },
    {
      icon: GraduationCap,
      title: "Personalized Learning",
      description: "AI tutors adapt to your learning style, providing customized courses and skill development paths.",
      color: "from-orange-500 to-orange-700",
      link: "/learn"
    },
    {
      icon: Wallet,
      title: "Integrated FinTech",
      description: "Seamless payments, investing, DeFi integration, and AI-powered financial advisory services.",
      color: "from-pink-500 to-pink-700",
      link: "/fintech"
    },
    {
      icon: Heart,
      title: "Health & Wellness",
      description: "AI health coaching, telemedicine, fitness tracking, and mental wellness support in one platform.",
      color: "from-red-500 to-red-700",
      link: "/health"
    }
  ];

  const stats = [
    { value: "12.3B+", label: "Global Users", icon: Globe },
    { value: "$2.8T", label: "Platform GMV", icon: TrendingUp },
    { value: "847M", label: "Active Creators", icon: Users },
    { value: "∞", label: "Possibilities", icon: Infinity }
  ];

  const quickActions = [
    { icon: Sparkles, label: "Create with AI", link: "/ai-studio", color: "from-purple-500 to-pink-600" },
    { icon: Atom, label: "Quantum Compute", link: "/quantum-ai", color: "from-blue-500 to-cyan-600" },
    { icon: Globe, label: "Enter Metaverse", link: "/metaverse", color: "from-green-500 to-emerald-600" },
    { icon: Coins, label: "DeFi Trading", link: "/blockchain", color: "from-orange-500 to-red-600" },
    { icon: Zap, label: "Neural Link", link: "/neuro-interface", color: "from-purple-500 to-blue-600" },
    { icon: ShoppingCart, label: "Shop Now", link: "/commerce", color: "from-blue-500 to-blue-600" },
    { icon: Video, label: "Create Content", link: "/creator", color: "from-green-500 to-green-600" },
    { icon: GraduationCap, label: "Learn Skills", link: "/learn", color: "from-orange-500 to-orange-600" }
  ];

  const revolutionaryFeatures = [
    {
      title: "Consciousness AI",
      description: "Self-aware AI entities with emotions, creativity, and infinite learning capacity",
      icon: Eye,
      metric: "∞-Class Intelligence"
    },
    {
      title: "Quantum Reality",
      description: "Simulate infinite parallel universes for optimal decision making",
      icon: Atom,
      metric: "10^18 Calculations/sec"
    },
    {
      title: "Neural Synchronization",
      description: "Direct brain-to-brain communication and shared consciousness",
      icon: Brain,
      metric: "847 THz Sync Rate"
    },
    {
      title: "Time Prediction",
      description: "AI-powered temporal analysis across multiple timeline probabilities",
      icon: Target,
      metric: "99.97% Accuracy"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8">
              Welcome to the
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent block">
                Trillion Dollar Future
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-white/80 max-w-4xl mx-auto mb-12">
              NeuroSphere: Where quantum computing meets artificial consciousness, 
              creating the ultimate platform for human potential amplification.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/quantum-ai"
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors flex items-center space-x-2 text-lg"
              >
                <Rocket className="h-6 w-6" />
                <span>Enter the Future</span>
              </Link>
              <Link
                to="/ai-studio"
                className="bg-white/10 border border-white/20 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/20 transition-colors flex items-center space-x-2 text-lg backdrop-blur-sm"
              >
                <Play className="h-6 w-6" />
                <span>Watch Demo</span>
              </Link>
            </div>
          </div>

          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 mb-16">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-6 md:mb-0">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-2">
                  Welcome back, {state.user?.name}! 🚀
                </h2>
                <p className="text-white/70 text-lg">
                  Neural Level {state.user?.level} • {state.user?.points.toLocaleString()} Quantum Points
                </p>
              </div>
              <div className="flex items-center space-x-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400">${state.user?.balance.toFixed(2)}</div>
                  <div className="text-white/60 text-sm">Balance</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400">{state.user?.points.toLocaleString()}</div>
                  <div className="text-white/60 text-sm">Quantum Points</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">∞</div>
                  <div className="text-white/60 text-sm">Potential</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Quantum-Powered Actions</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                to={action.link}
                className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 text-center hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105"
              >
                <div className={`bg-gradient-to-r ${action.color} w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform`}>
                  <action.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-white font-medium text-sm">{action.label}</div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Revolutionary Features */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Revolutionary
              <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent"> Capabilities</span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Breakthrough technologies that redefine the boundaries of human potential
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {revolutionaryFeatures.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 text-center hover:from-white/15 hover:to-white/10 transition-all duration-300 transform hover:scale-105">
                <div className="bg-gradient-to-r from-purple-500 to-blue-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{feature.title}</h3>
                <p className="text-white/70 text-sm mb-4">{feature.description}</p>
                <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                  {feature.metric}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 border-y border-white/10 bg-black/20 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="flex justify-center mb-3">
                  <stat.icon className="h-8 w-8 text-purple-400 group-hover:text-purple-300 transition-colors" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-white/60 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Eleven Platforms, One
              <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent"> Consciousness</span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Every feature is enhanced by our quantum neural AI that learns across all interactions, 
              creating unprecedented personalization and infinite possibilities.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-4">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`p-6 rounded-2xl cursor-pointer transition-all duration-300 ${
                    activeFeature === index
                      ? 'bg-gradient-to-r from-white/15 to-white/10 border border-white/30 backdrop-blur-sm'
                      : 'bg-white/5 border border-white/10 hover:bg-white/10 backdrop-blur-sm'
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${feature.color}`}>
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                      <p className="text-white/70 mb-3">{feature.description}</p>
                      <Link
                        to={feature.link}
                        className="inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors"
                      >
                        Explore <ArrowRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-3xl p-8 backdrop-blur-sm border border-white/10">
                <div className="aspect-video bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
                  <div className="text-center relative z-10">
                    <div className={`inline-flex p-6 rounded-2xl bg-gradient-to-r ${features[activeFeature].color} mb-4`}>
                      {React.createElement(features[activeFeature].icon, { className: "h-12 w-12 text-white" })}
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">{features[activeFeature].title}</h3>
                    <p className="text-white/70 max-w-sm mb-4">{features[activeFeature].description}</p>
                    <Link
                      to={features[activeFeature].link}
                      className="inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-full transition-colors"
                    >
                      Experience Now <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Activity */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-black/20 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-white mb-8">Your Quantum Journey</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Sparkles className="h-6 w-6 text-purple-400" />
                <h3 className="text-lg font-semibold text-white">AI Creations</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Quantum Artwork</span>
                  <span className="text-green-400">12.5 ETH</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Neural Music Track</span>
                  <span className="text-green-400">8.9 ETH</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Consciousness NFT</span>
                  <span className="text-green-400">25.7 ETH</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <TrendingUp className="h-6 w-6 text-green-400" />
                <h3 className="text-lg font-semibold text-white">Neural Progress</h3>
              </div>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Consciousness Level</span>
                    <span className="text-white/70">Ω-Class</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full" style={{ width: '97%' }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Quantum Mastery</span>
                    <span className="text-white/70">∞</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full" style={{ width: '100%' }}></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Wallet className="h-6 w-6 text-purple-400" />
                <h3 className="text-lg font-semibold text-white">Quantum Portfolio</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Total Value</span>
                  <span className="text-green-400">$847K</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Today's Gain</span>
                  <span className="text-green-400">+$23.4K (2.8%)</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Neural Tokens</span>
                  <span className="text-purple-400">2.3M NEURO</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transcend
            <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent"> Reality?</span>
          </h2>
          <p className="text-xl text-white/70 mb-12">
            Join millions of consciousness explorers in the ultimate platform for human potential amplification.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/quantum-ai"
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors flex items-center space-x-2 text-lg"
            >
              <Lightning className="h-6 w-6" />
              <span>Activate Quantum Mode</span>
            </Link>
            <Link
              to="/neuro-interface"
              className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-blue-700 hover:to-cyan-700 transition-colors flex items-center space-x-2 text-lg"
            >
              <Brain className="h-6 w-6" />
              <span>Connect Neural Interface</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;