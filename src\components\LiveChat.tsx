import React, { useState, useRef, useEffect } from 'react';

interface ChatMessage {
  id: string;
  user: string;
  content: string;
  time: string;
}

const mockUsers = ['Alice', '<PERSON>', '<PERSON>', 'AI助手', '观众1', '观众2'];

const LiveChat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    { id: '1', user: 'AI助手', content: '欢迎来到直播间！', time: '00:00' }
  ]);
  const [input, setInput] = useState('');
  const chatEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 模拟弹幕/AI消息
  useEffect(() => {
    const timer = setInterval(() => {
      if (Math.random() < 0.3) {
        setMessages(msgs => [
          ...msgs,
          {
            id: Date.now().toString(),
            user: mockUsers[Math.floor(Math.random() * mockUsers.length)],
            content: '这是一条弹幕~',
            time: new Date().toLocaleTimeString().slice(0, 5)
          }
        ]);
      }
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const sendMessage = () => {
    if (!input.trim()) return;
    setMessages(msgs => [
      ...msgs,
      {
        id: Date.now().toString(),
        user: '我',
        content: input,
        time: new Date().toLocaleTimeString().slice(0, 5)
      }
    ]);
    setInput('');
  };

  return (
    <div className="bg-white/10 rounded-xl p-4 flex flex-col h-[32rem] max-h-[80vh]">
      <h2 className="text-lg font-semibold text-white mb-2">直播聊天室</h2>
      <div className="flex-1 overflow-y-auto space-y-2 mb-2">
        {messages.map(msg => (
          <div key={msg.id} className="flex items-center space-x-2">
            <span className="text-xs text-blue-300">{msg.user}</span>
            <span className="text-white">{msg.content}</span>
            <span className="text-xs text-white/40 ml-auto">{msg.time}</span>
          </div>
        ))}
        <div ref={chatEndRef} />
      </div>
      <div className="flex space-x-2">
        <input
          className="flex-1 rounded-lg px-3 py-2 bg-white/20 text-white placeholder-white/60 focus:outline-none"
          placeholder="发送弹幕/消息..."
          value={input}
          onChange={e => setInput(e.target.value)}
          onKeyDown={e => e.key === 'Enter' && sendMessage()}
        />
        <button
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          onClick={sendMessage}
        >
          发送
        </button>
      </div>
    </div>
  );
};

export default LiveChat; 