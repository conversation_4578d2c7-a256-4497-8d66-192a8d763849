import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  _id: string;
  name: string;
  email: string;
  password: string;
  avatar?: string;
  level: number;
  points: number;
  balance: number;
  subscriptions: string[];
  preferences: {
    theme: 'light' | 'dark';
    language: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    privacy: {
      profileVisibility: 'public' | 'private' | 'friends';
      showActivity: boolean;
      showStats: boolean;
    };
  };
  profile: {
    bio?: string;
    location?: string;
    website?: string;
    socialLinks: {
      twitter?: string;
      linkedin?: string;
      github?: string;
      instagram?: string;
    };
    skills: string[];
    interests: string[];
  };
  stats: {
    coursesCompleted: number;
    projectsCreated: number;
    nftsOwned: number;
    totalEarnings: number;
    streamsWatched: number;
    postsCreated: number;
  };
  verification: {
    isEmailVerified: boolean;
    isPhoneVerified: boolean;
    isKYCVerified: boolean;
    emailVerificationToken?: string;
    phoneVerificationCode?: string;
  };
  security: {
    twoFactorEnabled: boolean;
    twoFactorSecret?: string;
    loginAttempts: number;
    lockUntil?: Date;
    passwordResetToken?: string;
    passwordResetExpires?: Date;
  };
  activity: {
    lastLogin: Date;
    lastActive: Date;
    loginHistory: Array<{
      ip: string;
      userAgent: string;
      timestamp: Date;
      location?: string;
    }>;
  };
  roles: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
  generatePasswordResetToken(): string;
  generateEmailVerificationToken(): string;
}

const userSchema = new Schema<IUser>({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters'],
    select: false
  },
  avatar: {
    type: String,
    default: null
  },
  level: {
    type: Number,
    default: 1,
    min: 1,
    max: 100
  },
  points: {
    type: Number,
    default: 0,
    min: 0
  },
  balance: {
    type: Number,
    default: 0,
    min: 0
  },
  subscriptions: [{
    type: String,
    enum: ['basic', 'premium', 'creator-pro', 'enterprise']
  }],
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'dark'
    },
    language: {
      type: String,
      default: 'en'
    },
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: false }
    },
    privacy: {
      profileVisibility: {
        type: String,
        enum: ['public', 'private', 'friends'],
        default: 'public'
      },
      showActivity: { type: Boolean, default: true },
      showStats: { type: Boolean, default: true }
    }
  },
  profile: {
    bio: { type: String, maxlength: 500 },
    location: { type: String, maxlength: 100 },
    website: { type: String, maxlength: 200 },
    socialLinks: {
      twitter: String,
      linkedin: String,
      github: String,
      instagram: String
    },
    skills: [String],
    interests: [String]
  },
  stats: {
    coursesCompleted: { type: Number, default: 0 },
    projectsCreated: { type: Number, default: 0 },
    nftsOwned: { type: Number, default: 0 },
    totalEarnings: { type: Number, default: 0 },
    streamsWatched: { type: Number, default: 0 },
    postsCreated: { type: Number, default: 0 }
  },
  verification: {
    isEmailVerified: { type: Boolean, default: false },
    isPhoneVerified: { type: Boolean, default: false },
    isKYCVerified: { type: Boolean, default: false },
    emailVerificationToken: String,
    phoneVerificationCode: String
  },
  security: {
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorSecret: String,
    loginAttempts: { type: Number, default: 0 },
    lockUntil: Date,
    passwordResetToken: String,
    passwordResetExpires: Date
  },
  activity: {
    lastLogin: { type: Date, default: Date.now },
    lastActive: { type: Date, default: Date.now },
    loginHistory: [{
      ip: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      location: String
    }]
  },
  roles: [{
    type: String,
    enum: ['user', 'creator', 'admin', 'moderator'],
    default: 'user'
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ 'activity.lastActive': -1 });
userSchema.index({ level: -1, points: -1 });
userSchema.index({ createdAt: -1 });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Generate password reset token
userSchema.methods.generatePasswordResetToken = function(): string {
  const resetToken = require('crypto').randomBytes(32).toString('hex');
  this.security.passwordResetToken = require('crypto').createHash('sha256').update(resetToken).digest('hex');
  this.security.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  return resetToken;
};

// Generate email verification token
userSchema.methods.generateEmailVerificationToken = function(): string {
  const verificationToken = require('crypto').randomBytes(32).toString('hex');
  this.verification.emailVerificationToken = require('crypto').createHash('sha256').update(verificationToken).digest('hex');
  return verificationToken;
};

// Virtual for user's full profile
userSchema.virtual('fullProfile').get(function() {
  return {
    id: this._id,
    name: this.name,
    email: this.email,
    avatar: this.avatar,
    level: this.level,
    points: this.points,
    balance: this.balance,
    subscriptions: this.subscriptions,
    profile: this.profile,
    stats: this.stats,
    isActive: this.isActive,
    createdAt: this.createdAt
  };
});

export const User = mongoose.model<IUser>('User', userSchema);
