name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  MONGODB_VERSION: '6.0'
  REDIS_VERSION: '7'

jobs:
  # Test Frontend
  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npm run type-check

    - name: Run tests
      run: npm run test

    - name: Build frontend
      run: npm run build

  # Test Backend
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password
        ports:
          - 27017:27017
        options: >-
          --health-cmd mongo
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run backend linting
      working-directory: ./backend
      run: npm run lint

    - name: Run backend tests
      working-directory: ./backend
      run: npm test
      env:
        NODE_ENV: test
        MONGODB_URI: **************************************************************************
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
        JWT_REFRESH_SECRET: test-refresh-secret

    - name: Build backend
      working-directory: ./backend
      run: npm run build

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Deploy
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/neurosphere-frontend:latest
          ${{ secrets.DOCKER_USERNAME }}/neurosphere-frontend:${{ github.sha }}
        build-args: |
          VITE_API_BASE_URL=${{ secrets.VITE_API_BASE_URL }}
          VITE_WS_URL=${{ secrets.VITE_WS_URL }}
          VITE_APP_NAME=NeuroSphere
          VITE_APP_VERSION=1.0.0

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/neurosphere-backend:latest
          ${{ secrets.DOCKER_USERNAME }}/neurosphere-backend:${{ github.sha }}

    # Deploy to staging/production
    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to production..."
        # Add your deployment commands here
        # For example, using SSH to deploy to a server:
        # ssh user@server 'cd /path/to/app && docker-compose pull && docker-compose up -d'

  # Notify
  notify:
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: always()
    
    steps:
    - name: Notify Slack
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
